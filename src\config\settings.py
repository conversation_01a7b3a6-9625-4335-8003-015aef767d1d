# Configuration settings for the Product Comparison Tool

# Column Mapping Synonyms (add common variations)
COLUMN_SYNONYMS = {
    "product_name": ["Product Name", "Item Name", "Title", "Description"],
    "price": ["Price", "Cost", "Amount", "Value"],
    "product_id": ["Product ID", "SKU", "Item Number", "Identifier"],
    "brand": ["Brand", "Manufacturer"],
    "model": ["Model", "Model Number"],
    "unit": ["Unit", "Unit of Measure", "UOM"]
}

# Default columns to look for (keys should match standard_name in COLUMN_SYNONYMS)
DEFAULT_COLUMNS = {
    "product_name": "Product Name", # Preferred name for product_name standard key
    "price": "Price",
    "product_id": "Product ID",
    "brand": "Brand",
    "model": "Model",
    "unit": "Unit"
}

# Matching Thresholds
FUZZY_MATCH_THRESHOLD = 85  # Score out of 100 for RapidFuzz WRatio
SEMANTIC_SIMILARITY_THRESHOLD = 0.75 # Cosine similarity threshold for SentenceTransformer

# Price Analysis Settings
DEFAULT_TARGET_CURRENCY = "USD"
# Add API key if using a live currency conversion service
CURRENCY_CONVERSION_API_KEY = None

# Caching Settings
ENABLE_CACHING = True
CACHE_DIR_JOBLIB = "/home/<USER>/product_comparison_tool/cache/joblib"
CACHE_DIR_STREAMLIT = "/home/<USER>/product_comparison_tool/cache/streamlit"

# Performance Settings
BATCH_SIZE_EMBEDDINGS = 32 # Batch size for SentenceTransformer embeddings

