const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./data-grid-overlay-editor.msYws2Ou.js","./index.C1z8KpLA.js","../css/index.DqDwtg6_.css","./FormClearHelper.B67tgll0.js","./withFullScreenWrapper.Ov13692o.js","./Toolbar.D9RUZv9G.js","./checkbox.Z6iSfe5F.js","./mergeWith.B_7zmsM4.js","./sprintf.D7DtBTRn.js","./createDownloadLinkElement.DZMwyjvU.js","./toConsumableArray.CgkEPBwD.js","./possibleConstructorReturn.nNhsvgRd.js","./createSuper.B4oGDYRm.js","./FileDownload.esm.P9rKwKo8.js","./number-overlay-editor.CSeVhHRU.js","./es6.CxQz807-.js"])))=>i.map(i=>d[i]);
var ch=Object.defineProperty;var dh=(e,t,n)=>t in e?ch(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ft=(e,t,n)=>dh(e,typeof t!="symbol"?t+"":t,n);import{r as g,E as _s,_ as Ze,c as fh,g as wr,R as ie,i as qa,d as fo,e as hh,f as gh,h as mh,m as Ic,o as Tc,p as ph,q as Dc,s as vh,t as bh,u as wh,v as yh,w as Ch,x as Sh,a as lr,y as Ls,n as Ci,z as jr,j as ut,F as Oc,A as ya,T as Pc,B as Ca,C as Ln,D as gr,G as Sa,H as Wn,I as _c,J as Zo,K as xh,L as kh,M as Be,N as Lc,O as vt,Q as ji,S as qr,U as Ya,V as Mh,W as Rh,X as Eh,Y as Dl,Z as Ih,$ as Fc,a0 as Th,a1 as Dh,a2 as Cs,a3 as Ac,a4 as Hc,a5 as Oh,a6 as Ph,a7 as _h,a8 as Lh,a9 as Fh,aa as Ah,ab as Hh,ac as zh,ad as Vh,ae as $h,af as zc,ag as Nh,ah as Bh,ai as Wh,aj as Uh,ak as qh,al as Yh,am as Xh,an as Gh,ao as jh,ap as Kh,aq as Zh,ar as Jh,as as Mn,l as Fs,at as Ki,au as yr,av as Ne,aw as Qh,b as Vc,ax as $c,k as eg,ay as tg,az as ng,aA as Xa,aB as rg,aC as ig,aD as og,aE as ag,aF as sg}from"./index.C1z8KpLA.js";import{u as lg}from"./FormClearHelper.B67tgll0.js";import{w as ug,E as cg}from"./withFullScreenWrapper.Ov13692o.js";import{T as dg,a as di}from"./Toolbar.D9RUZv9G.js";import{L as fg,S as hg,a as gg}from"./checkbox.Z6iSfe5F.js";import{m as mg}from"./mergeWith.B_7zmsM4.js";import{s as pg}from"./sprintf.D7DtBTRn.js";import{c as vg}from"./createDownloadLinkElement.DZMwyjvU.js";import{_ as pr,a as As,C as bg}from"./toConsumableArray.CgkEPBwD.js";import{_ as wg,a as yg,b as Cg}from"./possibleConstructorReturn.nNhsvgRd.js";import{_ as Sg}from"./createSuper.B4oGDYRm.js";import{D as xg,F as kg}from"./FileDownload.esm.P9rKwKo8.js";var Nc=g.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return g.createElement(_s,Ze({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),g.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),g.createElement("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"}))});Nc.displayName="Add";var Bc=g.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return g.createElement(_s,Ze({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),g.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),g.createElement("path",{d:"M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}))});Bc.displayName="Search";var Wc=g.forwardRef(function(e,t){var n={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return g.createElement(_s,Ze({iconAttrs:n,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:t}),g.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),g.createElement("path",{d:"M12 6a9.77 9.77 0 018.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0112 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 010 5 2.5 2.5 0 010-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"}))});Wc.displayName="Visibility";function Uc(e="This should not happen"){throw new Error(e)}function Fn(e,t="Assertion failed"){if(!e)return Uc(t)}function io(e,t){return Uc(t??"Hell froze over")}function Mg(e,t){try{return e()}catch{return t}}const Ol=Object.prototype.hasOwnProperty;function wi(e,t){let n,r;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&wi(e[r],t[r]););return r===-1}if(!n||typeof e=="object"){r=0;for(n in e)if(Ol.call(e,n)&&++r&&!Ol.call(t,n)||!(n in t)||!wi(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!==e&&t!==t}var Ga,Pl;function Rg(){if(Pl)return Ga;Pl=1;var e=Object.prototype,t=e.hasOwnProperty;function n(r,i){return r!=null&&t.call(r,i)}return Ga=n,Ga}var ja,_l;function Eg(){if(_l)return ja;_l=1;var e=Rg(),t=fh();function n(r,i){return r!=null&&t(r,i,e)}return ja=n,ja}var Ig=Eg();const Tg=wr(Ig),ra=null,Hs=void 0;var te;(function(e){e.Uri="uri",e.Text="text",e.Image="image",e.RowID="row-id",e.Number="number",e.Bubble="bubble",e.Boolean="boolean",e.Loading="loading",e.Markdown="markdown",e.Drilldown="drilldown",e.Protected="protected",e.Custom="custom"})(te||(te={}));var Ll;(function(e){e.HeaderRowID="headerRowID",e.HeaderCode="headerCode",e.HeaderNumber="headerNumber",e.HeaderString="headerString",e.HeaderBoolean="headerBoolean",e.HeaderAudioUri="headerAudioUri",e.HeaderVideoUri="headerVideoUri",e.HeaderEmoji="headerEmoji",e.HeaderImage="headerImage",e.HeaderUri="headerUri",e.HeaderPhone="headerPhone",e.HeaderMarkdown="headerMarkdown",e.HeaderDate="headerDate",e.HeaderTime="headerTime",e.HeaderEmail="headerEmail",e.HeaderReference="headerReference",e.HeaderIfThenElse="headerIfThenElse",e.HeaderSingleValue="headerSingleValue",e.HeaderLookup="headerLookup",e.HeaderTextTemplate="headerTextTemplate",e.HeaderMath="headerMath",e.HeaderRollup="headerRollup",e.HeaderJoinStrings="headerJoinStrings",e.HeaderSplitString="headerSplitString",e.HeaderGeoDistance="headerGeoDistance",e.HeaderArray="headerArray",e.RowOwnerOverlay="rowOwnerOverlay",e.ProtectedColumnOverlay="protectedColumnOverlay"})(Ll||(Ll={}));var ia;(function(e){e.Triangle="triangle",e.Dots="dots"})(ia||(ia={}));function Fo(e){return"width"in e&&typeof e.width=="number"}async function Fl(e){return typeof e=="object"?e:await e()}function hi(e){return!(e.kind===te.Loading||e.kind===te.Bubble||e.kind===te.RowID||e.kind===te.Protected||e.kind===te.Drilldown)}function mi(e){return e.kind===Yn.Marker||e.kind===Yn.NewRow}function Zi(e){if(!hi(e)||e.kind===te.Image)return!1;if(e.kind===te.Text||e.kind===te.Number||e.kind===te.Markdown||e.kind===te.Uri||e.kind===te.Custom||e.kind===te.Boolean)return e.readonly!==!0;io(e,"A cell was passed with an invalid kind")}function Dg(e){return Tg(e,"editor")}function zs(e){return!(e.readonly??!1)}var Yn;(function(e){e.NewRow="new-row",e.Marker="marker"})(Yn||(Yn={}));function Og(e){if(e.length===0)return[];const t=[...e],n=[];t.sort(function(r,i){return r[0]-i[0]}),n.push([...t[0]]);for(const r of t.slice(1)){const i=n[n.length-1];i[1]<r[0]?n.push([...r]):i[1]<r[1]&&(i[1]=r[1])}return n}let Al;const mr=class mr{constructor(t){ft(this,"items");this.items=t}offset(t){if(t===0)return this;const n=this.items.map(r=>[r[0]+t,r[1]+t]);return new mr(n)}add(t){const n=typeof t=="number"?[t,t+1]:t,r=Og([...this.items,n]);return new mr(r)}remove(t){const n=[...this.items],r=typeof t=="number"?t:t[0],i=typeof t=="number"?t+1:t[1];for(const[o,a]of n.entries()){const[l,s]=a;if(l<=i&&r<=s){const u=[];l<r&&u.push([l,r]),i<s&&u.push([i,s]),n.splice(o,1,...u)}}return new mr(n)}first(){if(this.items.length!==0)return this.items[0][0]}last(){if(this.items.length!==0)return this.items.slice(-1)[0][1]-1}hasIndex(t){for(let n=0;n<this.items.length;n++){const[r,i]=this.items[n];if(t>=r&&t<i)return!0}return!1}hasAll(t){for(let n=t[0];n<t[1];n++)if(!this.hasIndex(n))return!1;return!0}some(t){for(const n of this)if(t(n))return!0;return!1}equals(t){if(t===this)return!0;if(t.items.length!==this.items.length)return!1;for(let n=0;n<this.items.length;n++){const r=t.items[n],i=this.items[n];if(r[0]!==i[0]||r[1]!==i[1])return!1}return!0}toArray(){const t=[];for(const[n,r]of this.items)for(let i=n;i<r;i++)t.push(i);return t}get length(){let t=0;for(const[n,r]of this.items)t+=r-n;return t}*[Symbol.iterator](){for(const[t,n]of this.items)for(let r=t;r<n;r++)yield r}};ft(mr,"empty",()=>Al??(Al=new mr([]))),ft(mr,"fromSingleSelection",t=>mr.empty().add(t));let pt=mr;var Pg=function(){const t=Array.prototype.slice.call(arguments).filter(Boolean),n={},r=[];t.forEach(o=>{(o?o.split(" "):[]).forEach(l=>{if(l.startsWith("atm_")){const[,s]=l.split("_");n[s]=l}else r.push(l)})});const i=[];for(const o in n)Object.prototype.hasOwnProperty.call(n,o)&&i.push(n[o]);return i.push(...r),i.join(" ")},Hl=Pg,_g=e=>e.toUpperCase()===e,Lg=e=>t=>e.indexOf(t)===-1,qc=(e,t)=>{const n={};return Object.keys(e).filter(Lg(t)).forEach(r=>{n[r]=e[r]}),n};function Fg(e,t,n){const r=qc(t,n);if(!e){const i=typeof qa=="function"?{default:qa}:qa;Object.keys(r).forEach(o=>{i.default(o)||delete r[o]})}return r}var Ag=(e,t)=>{};function Hg(e){let t="";return n=>{const r=(o,a)=>{const{as:l=e,class:s=t}=o,u=n.propsAsIs===void 0?!(typeof l=="string"&&l.indexOf("-")===-1&&!_g(l[0])):n.propsAsIs,c=Fg(u,o,["as","class"]);c.ref=a,c.className=n.atomic?Hl(n.class,c.className||s):Hl(c.className||s,n.class);const{vars:d}=n;if(d){const h={};for(const p in d){const v=d[p],w=v[0],b=v[1]||"",M=typeof w=="function"?w(o):w;Ag(M,n.name),h[`--${p}`]=`${M}${b}`}const f=c.style||{},m=Object.keys(f);m.length>0&&m.forEach(p=>{h[p]=f[p]}),c.style=h}return e.__linaria&&e!==l?(c.as=l,ie.createElement(e,c)):ie.createElement(l,c)},i=ie.forwardRef?ie.forwardRef(r):o=>{const a=qc(o,["innerRef"]);return r(a,o.innerRef)};return i.displayName=n.name,i.__linaria={className:n.class||t,extends:e},i}}var mn=Hg;const zg=mn("div")({name:"ImageOverlayEditorStyle",class:"gdg-i2iowwq",propsAsIs:!1});var Ka={},Hi={},Ao={},Ho={},zl;function Vg(){return zl||(zl=1,function(e){(function(t,n){n(e,fo(),hh())})(Ho,function(t,n,r){Object.defineProperty(t,"__esModule",{value:!0}),t.setHasSupportToCaptureOption=m;var i=a(n),o=a(r);function a(b){return b&&b.__esModule?b:{default:b}}var l=Object.assign||function(b){for(var M=1;M<arguments.length;M++){var O=arguments[M];for(var S in O)Object.prototype.hasOwnProperty.call(O,S)&&(b[S]=O[S])}return b};function s(b,M){var O={};for(var S in b)M.indexOf(S)>=0||Object.prototype.hasOwnProperty.call(b,S)&&(O[S]=b[S]);return O}function u(b,M){if(!(b instanceof M))throw new TypeError("Cannot call a class as a function")}var c=function(){function b(M,O){for(var S=0;S<O.length;S++){var R=O[S];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(M,R.key,R)}}return function(M,O,S){return O&&b(M.prototype,O),S&&b(M,S),M}}();function d(b,M){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return M&&(typeof M=="object"||typeof M=="function")?M:b}function h(b,M){if(typeof M!="function"&&M!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof M);b.prototype=Object.create(M&&M.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),M&&(Object.setPrototypeOf?Object.setPrototypeOf(b,M):b.__proto__=M)}var f=!1;function m(b){f=b}try{addEventListener("test",null,Object.defineProperty({},"capture",{get:function(){m(!0)}}))}catch{}function p(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{capture:!0};return f?b:b.capture}function v(b){if("touches"in b){var M=b.touches[0],O=M.pageX,S=M.pageY;return{x:O,y:S}}var R=b.screenX,_=b.screenY;return{x:R,y:_}}var w=function(b){h(M,b);function M(){var O;u(this,M);for(var S=arguments.length,R=Array(S),_=0;_<S;_++)R[_]=arguments[_];var E=d(this,(O=M.__proto__||Object.getPrototypeOf(M)).call.apply(O,[this].concat(R)));return E._handleSwipeStart=E._handleSwipeStart.bind(E),E._handleSwipeMove=E._handleSwipeMove.bind(E),E._handleSwipeEnd=E._handleSwipeEnd.bind(E),E._onMouseDown=E._onMouseDown.bind(E),E._onMouseMove=E._onMouseMove.bind(E),E._onMouseUp=E._onMouseUp.bind(E),E._setSwiperRef=E._setSwiperRef.bind(E),E}return c(M,[{key:"componentDidMount",value:function(){this.swiper&&this.swiper.addEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"componentWillUnmount",value:function(){this.swiper&&this.swiper.removeEventListener("touchmove",this._handleSwipeMove,p({capture:!0,passive:!1}))}},{key:"_onMouseDown",value:function(S){this.props.allowMouseEvents&&(this.mouseDown=!0,document.addEventListener("mouseup",this._onMouseUp),document.addEventListener("mousemove",this._onMouseMove),this._handleSwipeStart(S))}},{key:"_onMouseMove",value:function(S){this.mouseDown&&this._handleSwipeMove(S)}},{key:"_onMouseUp",value:function(S){this.mouseDown=!1,document.removeEventListener("mouseup",this._onMouseUp),document.removeEventListener("mousemove",this._onMouseMove),this._handleSwipeEnd(S)}},{key:"_handleSwipeStart",value:function(S){var R=v(S),_=R.x,E=R.y;this.moveStart={x:_,y:E},this.props.onSwipeStart(S)}},{key:"_handleSwipeMove",value:function(S){if(this.moveStart){var R=v(S),_=R.x,E=R.y,x=_-this.moveStart.x,L=E-this.moveStart.y;this.moving=!0;var D=this.props.onSwipeMove({x,y:L},S);D&&S.cancelable&&S.preventDefault(),this.movePosition={deltaX:x,deltaY:L}}}},{key:"_handleSwipeEnd",value:function(S){this.props.onSwipeEnd(S);var R=this.props.tolerance;this.moving&&this.movePosition&&(this.movePosition.deltaX<-R?this.props.onSwipeLeft(1,S):this.movePosition.deltaX>R&&this.props.onSwipeRight(1,S),this.movePosition.deltaY<-R?this.props.onSwipeUp(1,S):this.movePosition.deltaY>R&&this.props.onSwipeDown(1,S)),this.moveStart=null,this.moving=!1,this.movePosition=null}},{key:"_setSwiperRef",value:function(S){this.swiper=S,this.props.innerRef(S)}},{key:"render",value:function(){var S=this.props;S.tagName;var R=S.className,_=S.style,E=S.children;S.allowMouseEvents,S.onSwipeUp,S.onSwipeDown,S.onSwipeLeft,S.onSwipeRight,S.onSwipeStart,S.onSwipeMove,S.onSwipeEnd,S.innerRef,S.tolerance;var x=s(S,["tagName","className","style","children","allowMouseEvents","onSwipeUp","onSwipeDown","onSwipeLeft","onSwipeRight","onSwipeStart","onSwipeMove","onSwipeEnd","innerRef","tolerance"]);return i.default.createElement(this.props.tagName,l({ref:this._setSwiperRef,onMouseDown:this._onMouseDown,onTouchStart:this._handleSwipeStart,onTouchEnd:this._handleSwipeEnd,className:R,style:_},x),E)}}]),M}(n.Component);w.displayName="ReactSwipe",w.propTypes={tagName:o.default.string,className:o.default.string,style:o.default.object,children:o.default.node,allowMouseEvents:o.default.bool,onSwipeUp:o.default.func,onSwipeDown:o.default.func,onSwipeLeft:o.default.func,onSwipeRight:o.default.func,onSwipeStart:o.default.func,onSwipeMove:o.default.func,onSwipeEnd:o.default.func,innerRef:o.default.func,tolerance:o.default.number.isRequired},w.defaultProps={tagName:"div",allowMouseEvents:!1,onSwipeUp:function(){},onSwipeDown:function(){},onSwipeLeft:function(){},onSwipeRight:function(){},onSwipeStart:function(){},onSwipeMove:function(){},onSwipeEnd:function(){},innerRef:function(){},tolerance:0},t.default=w})}(Ho)),Ho}var Vl;function Yc(){return Vl||(Vl=1,function(e){(function(t,n){n(e,Vg())})(Ao,function(t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=i(n);function i(o){return o&&o.__esModule?o:{default:o}}t.default=r.default})}(Ao)),Ao}var zi={},$l;function Xc(){if($l)return zi;$l=1,Object.defineProperty(zi,"__esModule",{value:!0}),zi.default=void 0;var e=t(gh());function t(i){return i&&i.__esModule?i:{default:i}}function n(i,o,a){return o in i?Object.defineProperty(i,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):i[o]=a,i}var r={ROOT:function(o){return(0,e.default)(n({"carousel-root":!0},o||"",!!o))},CAROUSEL:function(o){return(0,e.default)({carousel:!0,"carousel-slider":o})},WRAPPER:function(o,a){return(0,e.default)({"thumbs-wrapper":!o,"slider-wrapper":o,"axis-horizontal":a==="horizontal","axis-vertical":a!=="horizontal"})},SLIDER:function(o,a){return(0,e.default)({thumbs:!o,slider:o,animated:!a})},ITEM:function(o,a,l){return(0,e.default)({thumb:!o,slide:o,selected:a,previous:l})},ARROW_PREV:function(o){return(0,e.default)({"control-arrow control-prev":!0,"control-disabled":o})},ARROW_NEXT:function(o){return(0,e.default)({"control-arrow control-next":!0,"control-disabled":o})},DOT:function(o){return(0,e.default)({dot:!0,selected:o})}};return zi.default=r,zi}var Vi={},$i={},Nl;function $g(){if(Nl)return $i;Nl=1,Object.defineProperty($i,"__esModule",{value:!0}),$i.outerWidth=void 0;var e=function(n){var r=n.offsetWidth,i=getComputedStyle(n);return r+=parseInt(i.marginLeft)+parseInt(i.marginRight),r};return $i.outerWidth=e,$i}var Ni={},Bl;function Vs(){if(Bl)return Ni;Bl=1,Object.defineProperty(Ni,"__esModule",{value:!0}),Ni.default=void 0;var e=function(n,r,i){var o=n===0?n:n+r,a=i==="horizontal"?[o,0,0]:[0,o,0],l="translate3d",s="("+a.join(",")+")";return l+s};return Ni.default=e,Ni}var Bi={},Wl;function Gc(){if(Wl)return Bi;Wl=1,Object.defineProperty(Bi,"__esModule",{value:!0}),Bi.default=void 0;var e=function(){return window};return Bi.default=e,Bi}var Ul;function jc(){if(Ul)return Vi;Ul=1,Object.defineProperty(Vi,"__esModule",{value:!0}),Vi.default=void 0;var e=s(fo()),t=a(Xc()),n=$g(),r=a(Vs()),i=a(Yc()),o=a(Gc());function a(E){return E&&E.__esModule?E:{default:E}}function l(){if(typeof WeakMap!="function")return null;var E=new WeakMap;return l=function(){return E},E}function s(E){if(E&&E.__esModule)return E;if(E===null||u(E)!=="object"&&typeof E!="function")return{default:E};var x=l();if(x&&x.has(E))return x.get(E);var L={},D=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var C in E)if(Object.prototype.hasOwnProperty.call(E,C)){var I=D?Object.getOwnPropertyDescriptor(E,C):null;I&&(I.get||I.set)?Object.defineProperty(L,C,I):L[C]=E[C]}return L.default=E,x&&x.set(E,L),L}function u(E){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?u=function(L){return typeof L}:u=function(L){return L&&typeof Symbol=="function"&&L.constructor===Symbol&&L!==Symbol.prototype?"symbol":typeof L},u(E)}function c(){return c=Object.assign||function(E){for(var x=1;x<arguments.length;x++){var L=arguments[x];for(var D in L)Object.prototype.hasOwnProperty.call(L,D)&&(E[D]=L[D])}return E},c.apply(this,arguments)}function d(E,x){if(!(E instanceof x))throw new TypeError("Cannot call a class as a function")}function h(E,x){for(var L=0;L<x.length;L++){var D=x[L];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(E,D.key,D)}}function f(E,x,L){return x&&h(E.prototype,x),E}function m(E,x){if(typeof x!="function"&&x!==null)throw new TypeError("Super expression must either be null or a function");E.prototype=Object.create(x&&x.prototype,{constructor:{value:E,writable:!0,configurable:!0}}),x&&p(E,x)}function p(E,x){return p=Object.setPrototypeOf||function(D,C){return D.__proto__=C,D},p(E,x)}function v(E){var x=M();return function(){var D=O(E),C;if(x){var I=O(this).constructor;C=Reflect.construct(D,arguments,I)}else C=D.apply(this,arguments);return w(this,C)}}function w(E,x){return x&&(u(x)==="object"||typeof x=="function")?x:b(E)}function b(E){if(E===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E}function M(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function O(E){return O=Object.setPrototypeOf?Object.getPrototypeOf:function(L){return L.__proto__||Object.getPrototypeOf(L)},O(E)}function S(E,x,L){return x in E?Object.defineProperty(E,x,{value:L,enumerable:!0,configurable:!0,writable:!0}):E[x]=L,E}var R=function(x){return x.hasOwnProperty("key")},_=function(E){m(L,E);var x=v(L);function L(D){var C;return d(this,L),C=x.call(this,D),S(b(C),"itemsWrapperRef",void 0),S(b(C),"itemsListRef",void 0),S(b(C),"thumbsRef",void 0),S(b(C),"setItemsWrapperRef",function(I){C.itemsWrapperRef=I}),S(b(C),"setItemsListRef",function(I){C.itemsListRef=I}),S(b(C),"setThumbsRef",function(I,T){C.thumbsRef||(C.thumbsRef=[]),C.thumbsRef[T]=I}),S(b(C),"updateSizes",function(){if(!(!C.props.children||!C.itemsWrapperRef||!C.thumbsRef)){var I=e.Children.count(C.props.children),T=C.itemsWrapperRef.clientWidth,k=C.props.thumbWidth?C.props.thumbWidth:(0,n.outerWidth)(C.thumbsRef[0]),z=Math.floor(T/k),N=z<I,X=N?I-z:0;C.setState(function(re,G){return{itemSize:k,visibleItems:z,firstItem:N?C.getFirstItem(G.selectedItem):0,lastPosition:X,showArrows:N}})}}),S(b(C),"handleClickItem",function(I,T,k){if(!R(k)||k.key==="Enter"){var z=C.props.onSelectItem;typeof z=="function"&&z(I,T)}}),S(b(C),"onSwipeStart",function(){C.setState({swiping:!0})}),S(b(C),"onSwipeEnd",function(){C.setState({swiping:!1})}),S(b(C),"onSwipeMove",function(I){var T=I.x;if(!C.state.itemSize||!C.itemsWrapperRef||!C.state.visibleItems)return!1;var k=0,z=e.Children.count(C.props.children),N=-(C.state.firstItem*100)/C.state.visibleItems,X=Math.max(z-C.state.visibleItems,0),re=-X*100/C.state.visibleItems;N===k&&T>0&&(T=0),N===re&&T<0&&(T=0);var G=C.itemsWrapperRef.clientWidth,j=N+100/(G/T);return C.itemsListRef&&["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(se){C.itemsListRef.style[se]=(0,r.default)(j,"%",C.props.axis)}),!0}),S(b(C),"slideRight",function(I){C.moveTo(C.state.firstItem-(typeof I=="number"?I:1))}),S(b(C),"slideLeft",function(I){C.moveTo(C.state.firstItem+(typeof I=="number"?I:1))}),S(b(C),"moveTo",function(I){I=I<0?0:I,I=I>=C.state.lastPosition?C.state.lastPosition:I,C.setState({firstItem:I})}),C.state={selectedItem:D.selectedItem,swiping:!1,showArrows:!1,firstItem:0,visibleItems:0,lastPosition:0},C}return f(L,[{key:"componentDidMount",value:function(){this.setupThumbs()}},{key:"componentDidUpdate",value:function(C){this.props.selectedItem!==this.state.selectedItem&&this.setState({selectedItem:this.props.selectedItem,firstItem:this.getFirstItem(this.props.selectedItem)}),this.props.children!==C.children&&this.updateSizes()}},{key:"componentWillUnmount",value:function(){this.destroyThumbs()}},{key:"setupThumbs",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.updateSizes()}},{key:"destroyThumbs",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes)}},{key:"getFirstItem",value:function(C){var I=C;return C>=this.state.lastPosition&&(I=this.state.lastPosition),C<this.state.firstItem+this.state.visibleItems&&(I=this.state.firstItem),C<this.state.firstItem&&(I=C),I}},{key:"renderItems",value:function(){var C=this;return this.props.children.map(function(I,T){var k=t.default.ITEM(!1,T===C.state.selectedItem),z={key:T,ref:function(X){return C.setThumbsRef(X,T)},className:k,onClick:C.handleClickItem.bind(C,T,C.props.children[T]),onKeyDown:C.handleClickItem.bind(C,T,C.props.children[T]),"aria-label":"".concat(C.props.labels.item," ").concat(T+1),style:{width:C.props.thumbWidth}};return e.default.createElement("li",c({},z,{role:"button",tabIndex:0}),I)})}},{key:"render",value:function(){var C=this;if(!this.props.children)return null;var I=e.Children.count(this.props.children)>1,T=this.state.showArrows&&this.state.firstItem>0,k=this.state.showArrows&&this.state.firstItem<this.state.lastPosition,z={},N=-this.state.firstItem*(this.state.itemSize||0),X=(0,r.default)(N,"px",this.props.axis),re=this.props.transitionTime+"ms";return z={WebkitTransform:X,MozTransform:X,MsTransform:X,OTransform:X,transform:X,msTransform:X,WebkitTransitionDuration:re,MozTransitionDuration:re,MsTransitionDuration:re,OTransitionDuration:re,transitionDuration:re,msTransitionDuration:re},e.default.createElement("div",{className:t.default.CAROUSEL(!1)},e.default.createElement("div",{className:t.default.WRAPPER(!1),ref:this.setItemsWrapperRef},e.default.createElement("button",{type:"button",className:t.default.ARROW_PREV(!T),onClick:function(){return C.slideRight()},"aria-label":this.props.labels.leftArrow}),I?e.default.createElement(i.default,{tagName:"ul",className:t.default.SLIDER(!1,this.state.swiping),onSwipeLeft:this.slideLeft,onSwipeRight:this.slideRight,onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:z,innerRef:this.setItemsListRef,allowMouseEvents:this.props.emulateTouch},this.renderItems()):e.default.createElement("ul",{className:t.default.SLIDER(!1,this.state.swiping),ref:function(j){return C.setItemsListRef(j)},style:z},this.renderItems()),e.default.createElement("button",{type:"button",className:t.default.ARROW_NEXT(!k),onClick:function(){return C.slideLeft()},"aria-label":this.props.labels.rightArrow})))}}]),L}(e.Component);return Vi.default=_,S(_,"displayName","Thumbs"),S(_,"defaultProps",{axis:"horizontal",labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},selectedItem:0,thumbWidth:80,transitionTime:350}),Vi}var Wi={},ql;function Ng(){if(ql)return Wi;ql=1,Object.defineProperty(Wi,"__esModule",{value:!0}),Wi.default=void 0;var e=function(){return document};return Wi.default=e,Wi}var _n={},Yl;function Kc(){if(Yl)return _n;Yl=1,Object.defineProperty(_n,"__esModule",{value:!0}),_n.setPosition=_n.getPosition=_n.isKeyboardEvent=_n.defaultStatusFormatter=_n.noop=void 0;var e=fo(),t=n(Vs());function n(s){return s&&s.__esModule?s:{default:s}}var r=function(){};_n.noop=r;var i=function(u,c){return"".concat(u," of ").concat(c)};_n.defaultStatusFormatter=i;var o=function(u){return u?u.hasOwnProperty("key"):!1};_n.isKeyboardEvent=o;var a=function(u,c){if(c.infiniteLoop&&++u,u===0)return 0;var d=e.Children.count(c.children);if(c.centerMode&&c.axis==="horizontal"){var h=-u*c.centerSlidePercentage,f=d-1;return u&&(u!==f||c.infiniteLoop)?h+=(100-c.centerSlidePercentage)/2:u===f&&(h+=100-c.centerSlidePercentage),h}return-u*100};_n.getPosition=a;var l=function(u,c){var d={};return["WebkitTransform","MozTransform","MsTransform","OTransform","transform","msTransform"].forEach(function(h){d[h]=(0,t.default)(u,"%",c)}),d};return _n.setPosition=l,_n}var er={},Xl;function Bg(){if(Xl)return er;Xl=1,Object.defineProperty(er,"__esModule",{value:!0}),er.fadeAnimationHandler=er.slideStopSwipingHandler=er.slideSwipeAnimationHandler=er.slideAnimationHandler=void 0;var e=fo(),t=r(Vs()),n=Kc();function r(d){return d&&d.__esModule?d:{default:d}}function i(d,h){var f=Object.keys(d);if(Object.getOwnPropertySymbols){var m=Object.getOwnPropertySymbols(d);h&&(m=m.filter(function(p){return Object.getOwnPropertyDescriptor(d,p).enumerable})),f.push.apply(f,m)}return f}function o(d){for(var h=1;h<arguments.length;h++){var f=arguments[h]!=null?arguments[h]:{};h%2?i(Object(f),!0).forEach(function(m){a(d,m,f[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(d,Object.getOwnPropertyDescriptors(f)):i(Object(f)).forEach(function(m){Object.defineProperty(d,m,Object.getOwnPropertyDescriptor(f,m))})}return d}function a(d,h,f){return h in d?Object.defineProperty(d,h,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[h]=f,d}var l=function(h,f){var m={},p=f.selectedItem,v=p,w=e.Children.count(h.children)-1,b=h.infiniteLoop&&(p<0||p>w);if(b)return v<0?h.centerMode&&h.centerSlidePercentage&&h.axis==="horizontal"?m.itemListStyle=(0,n.setPosition)(-(w+2)*h.centerSlidePercentage-(100-h.centerSlidePercentage)/2,h.axis):m.itemListStyle=(0,n.setPosition)(-(w+2)*100,h.axis):v>w&&(m.itemListStyle=(0,n.setPosition)(0,h.axis)),m;var M=(0,n.getPosition)(p,h),O=(0,t.default)(M,"%",h.axis),S=h.transitionTime+"ms";return m.itemListStyle={WebkitTransform:O,msTransform:O,OTransform:O,transform:O},f.swiping||(m.itemListStyle=o(o({},m.itemListStyle),{},{WebkitTransitionDuration:S,MozTransitionDuration:S,OTransitionDuration:S,transitionDuration:S,msTransitionDuration:S})),m};er.slideAnimationHandler=l;var s=function(h,f,m,p){var v={},w=f.axis==="horizontal",b=e.Children.count(f.children),M=0,O=(0,n.getPosition)(m.selectedItem,f),S=f.infiniteLoop?(0,n.getPosition)(b-1,f)-100:(0,n.getPosition)(b-1,f),R=w?h.x:h.y,_=R;O===M&&R>0&&(_=0),O===S&&R<0&&(_=0);var E=O+100/(m.itemSize/_),x=Math.abs(R)>f.swipeScrollTolerance;return f.infiniteLoop&&x&&(m.selectedItem===0&&E>-100?E-=b*100:m.selectedItem===b-1&&E<-b*100&&(E+=b*100)),(!f.preventMovementUntilSwipeScrollTolerance||x||m.swipeMovementStarted)&&(m.swipeMovementStarted||p({swipeMovementStarted:!0}),v.itemListStyle=(0,n.setPosition)(E,f.axis)),x&&!m.cancelClick&&p({cancelClick:!0}),v};er.slideSwipeAnimationHandler=s;var u=function(h,f){var m=(0,n.getPosition)(f.selectedItem,h),p=(0,n.setPosition)(m,h.axis);return{itemListStyle:p}};er.slideStopSwipingHandler=u;var c=function(h,f){var m=h.transitionTime+"ms",p="ease-in-out",v={position:"absolute",display:"block",zIndex:-2,minHeight:"100%",opacity:0,top:0,right:0,left:0,bottom:0,transitionTimingFunction:p,msTransitionTimingFunction:p,MozTransitionTimingFunction:p,WebkitTransitionTimingFunction:p,OTransitionTimingFunction:p};return f.swiping||(v=o(o({},v),{},{WebkitTransitionDuration:m,MozTransitionDuration:m,OTransitionDuration:m,transitionDuration:m,msTransitionDuration:m})),{slideStyle:v,selectedStyle:o(o({},v),{},{opacity:1,position:"relative"}),prevStyle:o({},v)}};return er.fadeAnimationHandler=c,er}var Gl;function Wg(){if(Gl)return Hi;Gl=1,Object.defineProperty(Hi,"__esModule",{value:!0}),Hi.default=void 0;var e=c(fo()),t=s(Yc()),n=s(Xc()),r=s(jc()),i=s(Ng()),o=s(Gc()),a=Kc(),l=Bg();function s(D){return D&&D.__esModule?D:{default:D}}function u(){if(typeof WeakMap!="function")return null;var D=new WeakMap;return u=function(){return D},D}function c(D){if(D&&D.__esModule)return D;if(D===null||d(D)!=="object"&&typeof D!="function")return{default:D};var C=u();if(C&&C.has(D))return C.get(D);var I={},T=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var k in D)if(Object.prototype.hasOwnProperty.call(D,k)){var z=T?Object.getOwnPropertyDescriptor(D,k):null;z&&(z.get||z.set)?Object.defineProperty(I,k,z):I[k]=D[k]}return I.default=D,C&&C.set(D,I),I}function d(D){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?d=function(I){return typeof I}:d=function(I){return I&&typeof Symbol=="function"&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},d(D)}function h(){return h=Object.assign||function(D){for(var C=1;C<arguments.length;C++){var I=arguments[C];for(var T in I)Object.prototype.hasOwnProperty.call(I,T)&&(D[T]=I[T])}return D},h.apply(this,arguments)}function f(D,C){var I=Object.keys(D);if(Object.getOwnPropertySymbols){var T=Object.getOwnPropertySymbols(D);C&&(T=T.filter(function(k){return Object.getOwnPropertyDescriptor(D,k).enumerable})),I.push.apply(I,T)}return I}function m(D){for(var C=1;C<arguments.length;C++){var I=arguments[C]!=null?arguments[C]:{};C%2?f(Object(I),!0).forEach(function(T){x(D,T,I[T])}):Object.getOwnPropertyDescriptors?Object.defineProperties(D,Object.getOwnPropertyDescriptors(I)):f(Object(I)).forEach(function(T){Object.defineProperty(D,T,Object.getOwnPropertyDescriptor(I,T))})}return D}function p(D,C){if(!(D instanceof C))throw new TypeError("Cannot call a class as a function")}function v(D,C){for(var I=0;I<C.length;I++){var T=C[I];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(D,T.key,T)}}function w(D,C,I){return C&&v(D.prototype,C),D}function b(D,C){if(typeof C!="function"&&C!==null)throw new TypeError("Super expression must either be null or a function");D.prototype=Object.create(C&&C.prototype,{constructor:{value:D,writable:!0,configurable:!0}}),C&&M(D,C)}function M(D,C){return M=Object.setPrototypeOf||function(T,k){return T.__proto__=k,T},M(D,C)}function O(D){var C=_();return function(){var T=E(D),k;if(C){var z=E(this).constructor;k=Reflect.construct(T,arguments,z)}else k=T.apply(this,arguments);return S(this,k)}}function S(D,C){return C&&(d(C)==="object"||typeof C=="function")?C:R(D)}function R(D){if(D===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return D}function _(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function E(D){return E=Object.setPrototypeOf?Object.getPrototypeOf:function(I){return I.__proto__||Object.getPrototypeOf(I)},E(D)}function x(D,C,I){return C in D?Object.defineProperty(D,C,{value:I,enumerable:!0,configurable:!0,writable:!0}):D[C]=I,D}var L=function(D){b(I,D);var C=O(I);function I(T){var k;p(this,I),k=C.call(this,T),x(R(k),"thumbsRef",void 0),x(R(k),"carouselWrapperRef",void 0),x(R(k),"listRef",void 0),x(R(k),"itemsRef",void 0),x(R(k),"timer",void 0),x(R(k),"animationHandler",void 0),x(R(k),"setThumbsRef",function(N){k.thumbsRef=N}),x(R(k),"setCarouselWrapperRef",function(N){k.carouselWrapperRef=N}),x(R(k),"setListRef",function(N){k.listRef=N}),x(R(k),"setItemsRef",function(N,X){k.itemsRef||(k.itemsRef=[]),k.itemsRef[X]=N}),x(R(k),"autoPlay",function(){e.Children.count(k.props.children)<=1||(k.clearAutoPlay(),k.props.autoPlay&&(k.timer=setTimeout(function(){k.increment()},k.props.interval)))}),x(R(k),"clearAutoPlay",function(){k.timer&&clearTimeout(k.timer)}),x(R(k),"resetAutoPlay",function(){k.clearAutoPlay(),k.autoPlay()}),x(R(k),"stopOnHover",function(){k.setState({isMouseEntered:!0},k.clearAutoPlay)}),x(R(k),"startOnLeave",function(){k.setState({isMouseEntered:!1},k.autoPlay)}),x(R(k),"isFocusWithinTheCarousel",function(){return k.carouselWrapperRef?!!((0,i.default)().activeElement===k.carouselWrapperRef||k.carouselWrapperRef.contains((0,i.default)().activeElement)):!1}),x(R(k),"navigateWithKeyboard",function(N){if(k.isFocusWithinTheCarousel()){var X=k.props.axis,re=X==="horizontal",G={ArrowUp:38,ArrowRight:39,ArrowDown:40,ArrowLeft:37},j=re?G.ArrowRight:G.ArrowDown,se=re?G.ArrowLeft:G.ArrowUp;j===N.keyCode?k.increment():se===N.keyCode&&k.decrement()}}),x(R(k),"updateSizes",function(){if(!(!k.state.initialized||!k.itemsRef||k.itemsRef.length===0)){var N=k.props.axis==="horizontal",X=k.itemsRef[0];if(X){var re=N?X.clientWidth:X.clientHeight;k.setState({itemSize:re}),k.thumbsRef&&k.thumbsRef.updateSizes()}}}),x(R(k),"setMountState",function(){k.setState({hasMount:!0}),k.updateSizes()}),x(R(k),"handleClickItem",function(N,X){if(e.Children.count(k.props.children)!==0){if(k.state.cancelClick){k.setState({cancelClick:!1});return}k.props.onClickItem(N,X),N!==k.state.selectedItem&&k.setState({selectedItem:N})}}),x(R(k),"handleOnChange",function(N,X){e.Children.count(k.props.children)<=1||k.props.onChange(N,X)}),x(R(k),"handleClickThumb",function(N,X){k.props.onClickThumb(N,X),k.moveTo(N)}),x(R(k),"onSwipeStart",function(N){k.setState({swiping:!0}),k.props.onSwipeStart(N)}),x(R(k),"onSwipeEnd",function(N){k.setState({swiping:!1,cancelClick:!1,swipeMovementStarted:!1}),k.props.onSwipeEnd(N),k.clearAutoPlay(),k.state.autoPlay&&k.autoPlay()}),x(R(k),"onSwipeMove",function(N,X){k.props.onSwipeMove(X);var re=k.props.swipeAnimationHandler(N,k.props,k.state,k.setState.bind(R(k)));return k.setState(m({},re)),!!Object.keys(re).length}),x(R(k),"decrement",function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;k.moveTo(k.state.selectedItem-(typeof N=="number"?N:1))}),x(R(k),"increment",function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1;k.moveTo(k.state.selectedItem+(typeof N=="number"?N:1))}),x(R(k),"moveTo",function(N){if(typeof N=="number"){var X=e.Children.count(k.props.children)-1;N<0&&(N=k.props.infiniteLoop?X:0),N>X&&(N=k.props.infiniteLoop?0:X),k.selectItem({selectedItem:N}),k.state.autoPlay&&k.state.isMouseEntered===!1&&k.resetAutoPlay()}}),x(R(k),"onClickNext",function(){k.increment(1)}),x(R(k),"onClickPrev",function(){k.decrement(1)}),x(R(k),"onSwipeForward",function(){k.increment(1),k.props.emulateTouch&&k.setState({cancelClick:!0})}),x(R(k),"onSwipeBackwards",function(){k.decrement(1),k.props.emulateTouch&&k.setState({cancelClick:!0})}),x(R(k),"changeItem",function(N){return function(X){(!(0,a.isKeyboardEvent)(X)||X.key==="Enter")&&k.moveTo(N)}}),x(R(k),"selectItem",function(N){k.setState(m({previousItem:k.state.selectedItem},N),function(){k.setState(k.animationHandler(k.props,k.state))}),k.handleOnChange(N.selectedItem,e.Children.toArray(k.props.children)[N.selectedItem])}),x(R(k),"getInitialImage",function(){var N=k.props.selectedItem,X=k.itemsRef&&k.itemsRef[N],re=X&&X.getElementsByTagName("img")||[];return re[0]}),x(R(k),"getVariableItemHeight",function(N){var X=k.itemsRef&&k.itemsRef[N];if(k.state.hasMount&&X&&X.children.length){var re=X.children[0].getElementsByTagName("img")||[];if(re.length>0){var G=re[0];if(!G.complete){var j=function ue(){k.forceUpdate(),G.removeEventListener("load",ue)};G.addEventListener("load",j)}}var se=re[0]||X.children[0],oe=se.clientHeight;return oe>0?oe:null}return null});var z={initialized:!1,previousItem:T.selectedItem,selectedItem:T.selectedItem,hasMount:!1,isMouseEntered:!1,autoPlay:T.autoPlay,swiping:!1,swipeMovementStarted:!1,cancelClick:!1,itemSize:1,itemListStyle:{},slideStyle:{},selectedStyle:{},prevStyle:{}};return k.animationHandler=typeof T.animationHandler=="function"&&T.animationHandler||T.animationHandler==="fade"&&l.fadeAnimationHandler||l.slideAnimationHandler,k.state=m(m({},z),k.animationHandler(T,z)),k}return w(I,[{key:"componentDidMount",value:function(){this.props.children&&this.setupCarousel()}},{key:"componentDidUpdate",value:function(k,z){!k.children&&this.props.children&&!this.state.initialized&&this.setupCarousel(),!k.autoFocus&&this.props.autoFocus&&this.forceFocus(),z.swiping&&!this.state.swiping&&this.setState(m({},this.props.stopSwipingHandler(this.props,this.state))),(k.selectedItem!==this.props.selectedItem||k.centerMode!==this.props.centerMode)&&(this.updateSizes(),this.moveTo(this.props.selectedItem)),k.autoPlay!==this.props.autoPlay&&(this.props.autoPlay?this.setupAutoPlay():this.destroyAutoPlay(),this.setState({autoPlay:this.props.autoPlay}))}},{key:"componentWillUnmount",value:function(){this.destroyCarousel()}},{key:"setupCarousel",value:function(){var k=this;this.bindEvents(),this.state.autoPlay&&e.Children.count(this.props.children)>1&&this.setupAutoPlay(),this.props.autoFocus&&this.forceFocus(),this.setState({initialized:!0},function(){var z=k.getInitialImage();z&&!z.complete?z.addEventListener("load",k.setMountState):k.setMountState()})}},{key:"destroyCarousel",value:function(){this.state.initialized&&(this.unbindEvents(),this.destroyAutoPlay())}},{key:"setupAutoPlay",value:function(){this.autoPlay();var k=this.carouselWrapperRef;this.props.stopOnHover&&k&&(k.addEventListener("mouseenter",this.stopOnHover),k.addEventListener("mouseleave",this.startOnLeave))}},{key:"destroyAutoPlay",value:function(){this.clearAutoPlay();var k=this.carouselWrapperRef;this.props.stopOnHover&&k&&(k.removeEventListener("mouseenter",this.stopOnHover),k.removeEventListener("mouseleave",this.startOnLeave))}},{key:"bindEvents",value:function(){(0,o.default)().addEventListener("resize",this.updateSizes),(0,o.default)().addEventListener("DOMContentLoaded",this.updateSizes),this.props.useKeyboardArrows&&(0,i.default)().addEventListener("keydown",this.navigateWithKeyboard)}},{key:"unbindEvents",value:function(){(0,o.default)().removeEventListener("resize",this.updateSizes),(0,o.default)().removeEventListener("DOMContentLoaded",this.updateSizes);var k=this.getInitialImage();k&&k.removeEventListener("load",this.setMountState),this.props.useKeyboardArrows&&(0,i.default)().removeEventListener("keydown",this.navigateWithKeyboard)}},{key:"forceFocus",value:function(){var k;(k=this.carouselWrapperRef)===null||k===void 0||k.focus()}},{key:"renderItems",value:function(k){var z=this;return this.props.children?e.Children.map(this.props.children,function(N,X){var re=X===z.state.selectedItem,G=X===z.state.previousItem,j=re&&z.state.selectedStyle||G&&z.state.prevStyle||z.state.slideStyle||{};z.props.centerMode&&z.props.axis==="horizontal"&&(j=m(m({},j),{},{minWidth:z.props.centerSlidePercentage+"%"})),z.state.swiping&&z.state.swipeMovementStarted&&(j=m(m({},j),{},{pointerEvents:"none"}));var se={ref:function(ue){return z.setItemsRef(ue,X)},key:"itemKey"+X+(k?"clone":""),className:n.default.ITEM(!0,X===z.state.selectedItem,X===z.state.previousItem),onClick:z.handleClickItem.bind(z,X,N),style:j};return e.default.createElement("li",se,z.props.renderItem(N,{isSelected:X===z.state.selectedItem,isPrevious:X===z.state.previousItem}))}):[]}},{key:"renderControls",value:function(){var k=this,z=this.props,N=z.showIndicators,X=z.labels,re=z.renderIndicator,G=z.children;return N?e.default.createElement("ul",{className:"control-dots"},e.Children.map(G,function(j,se){return re&&re(k.changeItem(se),se===k.state.selectedItem,se,X.item)})):null}},{key:"renderStatus",value:function(){return this.props.showStatus?e.default.createElement("p",{className:"carousel-status"},this.props.statusFormatter(this.state.selectedItem+1,e.Children.count(this.props.children))):null}},{key:"renderThumbs",value:function(){return!this.props.showThumbs||!this.props.children||e.Children.count(this.props.children)===0?null:e.default.createElement(r.default,{ref:this.setThumbsRef,onSelectItem:this.handleClickThumb,selectedItem:this.state.selectedItem,transitionTime:this.props.transitionTime,thumbWidth:this.props.thumbWidth,labels:this.props.labels,emulateTouch:this.props.emulateTouch},this.props.renderThumbs(this.props.children))}},{key:"render",value:function(){var k=this;if(!this.props.children||e.Children.count(this.props.children)===0)return null;var z=this.props.swipeable&&e.Children.count(this.props.children)>1,N=this.props.axis==="horizontal",X=this.props.showArrows&&e.Children.count(this.props.children)>1,re=X&&(this.state.selectedItem>0||this.props.infiniteLoop)||!1,G=X&&(this.state.selectedItem<e.Children.count(this.props.children)-1||this.props.infiniteLoop)||!1,j=this.renderItems(!0),se=j.shift(),oe=j.pop(),ue={className:n.default.SLIDER(!0,this.state.swiping),onSwipeMove:this.onSwipeMove,onSwipeStart:this.onSwipeStart,onSwipeEnd:this.onSwipeEnd,style:this.state.itemListStyle,tolerance:this.props.swipeScrollTolerance},he={};if(N){if(ue.onSwipeLeft=this.onSwipeForward,ue.onSwipeRight=this.onSwipeBackwards,this.props.dynamicHeight){var Q=this.getVariableItemHeight(this.state.selectedItem);he.height=Q||"auto"}}else ue.onSwipeUp=this.props.verticalSwipe==="natural"?this.onSwipeBackwards:this.onSwipeForward,ue.onSwipeDown=this.props.verticalSwipe==="natural"?this.onSwipeForward:this.onSwipeBackwards,ue.style=m(m({},ue.style),{},{height:this.state.itemSize}),he.height=this.state.itemSize;return e.default.createElement("div",{"aria-label":this.props.ariaLabel,className:n.default.ROOT(this.props.className),ref:this.setCarouselWrapperRef,tabIndex:this.props.useKeyboardArrows?0:void 0},e.default.createElement("div",{className:n.default.CAROUSEL(!0),style:{width:this.props.width}},this.renderControls(),this.props.renderArrowPrev(this.onClickPrev,re,this.props.labels.leftArrow),e.default.createElement("div",{className:n.default.WRAPPER(!0,this.props.axis),style:he},z?e.default.createElement(t.default,h({tagName:"ul",innerRef:this.setListRef},ue,{allowMouseEvents:this.props.emulateTouch}),this.props.infiniteLoop&&oe,this.renderItems(),this.props.infiniteLoop&&se):e.default.createElement("ul",{className:n.default.SLIDER(!0,this.state.swiping),ref:function(P){return k.setListRef(P)},style:this.state.itemListStyle||{}},this.props.infiniteLoop&&oe,this.renderItems(),this.props.infiniteLoop&&se)),this.props.renderArrowNext(this.onClickNext,G,this.props.labels.rightArrow),this.renderStatus()),this.renderThumbs())}}]),I}(e.default.Component);return Hi.default=L,x(L,"displayName","Carousel"),x(L,"defaultProps",{ariaLabel:void 0,axis:"horizontal",centerSlidePercentage:80,interval:3e3,labels:{leftArrow:"previous slide / item",rightArrow:"next slide / item",item:"slide item"},onClickItem:a.noop,onClickThumb:a.noop,onChange:a.noop,onSwipeStart:function(){},onSwipeEnd:function(){},onSwipeMove:function(){return!1},preventMovementUntilSwipeScrollTolerance:!1,renderArrowPrev:function(C,I,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_PREV(!I),onClick:C})},renderArrowNext:function(C,I,T){return e.default.createElement("button",{type:"button","aria-label":T,className:n.default.ARROW_NEXT(!I),onClick:C})},renderIndicator:function(C,I,T,k){return e.default.createElement("li",{className:n.default.DOT(I),onClick:C,onKeyDown:C,value:T,key:T,role:"button",tabIndex:0,"aria-label":"".concat(k," ").concat(T+1)})},renderItem:function(C){return C},renderThumbs:function(C){var I=e.Children.map(C,function(T){var k=T;if(T.type!=="img"&&(k=e.Children.toArray(T.props.children).find(function(z){return z.type==="img"})),!!k)return k});return I.filter(function(T){return T}).length===0?(console.warn("No images found! Can't build the thumb list without images. If you don't need thumbs, set showThumbs={false} in the Carousel. Note that it's not possible to get images rendered inside custom components. More info at https://github.com/leandrowd/react-responsive-carousel/blob/master/TROUBLESHOOTING.md"),[]):I},statusFormatter:a.defaultStatusFormatter,selectedItem:0,showArrows:!0,showIndicators:!0,showStatus:!0,showThumbs:!0,stopOnHover:!0,swipeScrollTolerance:5,swipeable:!0,transitionTime:350,verticalSwipe:"standard",width:"100%",animationHandler:"slide",swipeAnimationHandler:l.slideSwipeAnimationHandler,stopSwipingHandler:l.slideStopSwipingHandler}),Hi}var jl={},Kl;function Ug(){return Kl||(Kl=1),jl}var Zl;function qg(){return Zl||(Zl=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"Carousel",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"CarouselProps",{enumerable:!0,get:function(){return n.CarouselProps}}),Object.defineProperty(e,"Thumbs",{enumerable:!0,get:function(){return r.default}});var t=i(Wg()),n=Ug(),r=i(jc());function i(o){return o&&o.__esModule?o:{default:o}}}(Ka)),Ka}var Yg=qg(),Za,Jl;function Xg(){if(Jl)return Za;Jl=1;var e=mh(),t=function(){return e.Date.now()};return Za=t,Za}var Ja,Ql;function Zc(){if(Ql)return Ja;Ql=1;var e=Ic(),t=Xg(),n=Tc(),r="Expected a function",i=Math.max,o=Math.min;function a(l,s,u){var c,d,h,f,m,p,v=0,w=!1,b=!1,M=!0;if(typeof l!="function")throw new TypeError(r);s=n(s)||0,e(u)&&(w=!!u.leading,b="maxWait"in u,h=b?i(n(u.maxWait)||0,s):h,M="trailing"in u?!!u.trailing:M);function O(I){var T=c,k=d;return c=d=void 0,v=I,f=l.apply(k,T),f}function S(I){return v=I,m=setTimeout(E,s),w?O(I):f}function R(I){var T=I-p,k=I-v,z=s-T;return b?o(z,h-k):z}function _(I){var T=I-p,k=I-v;return p===void 0||T>=s||T<0||b&&k>=h}function E(){var I=t();if(_(I))return x(I);m=setTimeout(E,R(I))}function x(I){return m=void 0,M&&c?O(I):(c=d=void 0,f)}function L(){m!==void 0&&clearTimeout(m),v=0,c=p=d=m=void 0}function D(){return m===void 0?f:x(t())}function C(){var I=t(),T=_(I);if(c=arguments,d=this,p=I,T){if(m===void 0)return S(p);if(b)return clearTimeout(m),m=setTimeout(E,s),O(p)}return m===void 0&&(m=setTimeout(E,s)),f}return C.cancel=L,C.flush=D,C}return Ja=a,Ja}var Gg=Zc();const Jc=wr(Gg);function gn(e,t,n,r,i=!1){const o=g.useRef();o.current=t,g.useEffect(()=>{if(n===null||n.addEventListener===void 0)return;const a=n,l=s=>{var u;(u=o.current)==null||u.call(a,s)};return a.addEventListener(e,l,{passive:r,capture:i}),()=>{a.removeEventListener(e,l,{capture:i})}},[e,n,r,i])}function Yr(e,t){return e===void 0?void 0:t}const jg=Math.PI;function eu(e){return e*jg/180}const Qc=(e,t,n)=>({x1:e-n/2,y1:t-n/2,x2:e+n/2,y2:t+n/2}),ed=(e,t,n,r,i)=>{switch(e){case"left":return Math.floor(t)+r+i/2;case"center":return Math.floor(t+n/2);case"right":return Math.floor(t+n)-r-i/2}},td=(e,t,n)=>Math.min(e,t-n*2),nd=(e,t,n)=>n.x1<=e&&e<=n.x2&&n.y1<=t&&t<=n.y2,$s=e=>{const t=e.fgColor??"currentColor";return g.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},g.createElement("path",{d:"M12.7073 7.05029C7.87391 11.8837 10.4544 9.30322 6.03024 13.7273C5.77392 13.9836 5.58981 14.3071 5.50189 14.6587L4.52521 18.5655C4.38789 19.1148 4.88543 19.6123 5.43472 19.475L9.34146 18.4983C9.69313 18.4104 10.0143 18.2286 10.2706 17.9722L16.9499 11.2929",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}),g.createElement("path",{d:"M20.4854 4.92901L19.0712 3.5148C18.2901 2.73375 17.0238 2.73375 16.2428 3.5148L14.475 5.28257C15.5326 7.71912 16.4736 8.6278 18.7176 9.52521L20.4854 7.75744C21.2665 6.97639 21.2665 5.71006 20.4854 4.92901Z",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round",fill:"none",vectorEffect:"non-scaling-stroke"}))},Kg=e=>{const t=e.fgColor??"currentColor";return g.createElement("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},g.createElement("path",{d:"M19 6L10.3802 17L5.34071 11.8758",vectorEffect:"non-scaling-stroke",stroke:t,strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}))};function Zg(e,t,n){const[r,i]=g.useState(e),o=g.useRef(!0);g.useEffect(()=>()=>{o.current=!1},[]);const a=g.useRef(Jc(l=>{o.current&&i(l)},n));return g.useLayoutEffect(()=>{o.current&&a.current(()=>e())},t),r}const Jg="֑-߿יִ-﷽ﹰ-ﻼ",Qg="A-Za-zÀ-ÖØ-öø-ʸ̀-֐ࠀ-῿‎Ⰰ-﬜︀-﹯﻽-￿",em=new RegExp("^[^"+Qg+"]*["+Jg+"]");function Ns(e){return em.test(e)?"rtl":"not-rtl"}let zo;function Ss(){if(typeof document>"u")return 0;if(zo!==void 0)return zo;const e=document.createElement("p");e.style.width="100%",e.style.height="200px";const t=document.createElement("div");t.id="testScrollbar",t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.style.visibility="hidden",t.style.width="200px",t.style.height="150px",t.style.overflow="hidden",t.append(e),document.body.append(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),zo=n-r,zo}const Wr=Symbol();function tm(e){const t=g.useRef([Wr,e]);t.current[1]!==e&&(t.current[0]=e),t.current[1]=e;const[n,r]=g.useState(e),[,i]=g.useState(),o=g.useCallback(l=>{const s=t.current[0];s!==Wr&&(l=typeof l=="function"?l(s):l,l===s)||(s!==Wr&&i({}),r(u=>typeof l=="function"?l(s===Wr?u:s):l),t.current[0]=Wr)},[]),a=g.useCallback(()=>{t.current[0]=Wr,i({})},[]);return[t.current[0]===Wr?n:t.current[0],o,a]}function rd(e){if(e.length===0)return"";let t=0,n=0;for(const r of e){if(n+=r.length,n>1e4)break;t++}return e.slice(0,t).join(", ")}function nm(e){const t=g.useRef(e);return wi(e,t.current)||(t.current=e),t.current}const rm=e=>{const{urls:t,canWrite:n,onEditClick:r,renderImage:i}=e,o=t.filter(l=>l!=="");if(o.length===0)return null;const a=o.length>1;return g.createElement(zg,{"data-testid":"GDG-default-image-overlay-editor"},g.createElement(Yg.Carousel,{showArrows:a,showThumbs:!1,swipeable:a,emulateTouch:a,infiniteLoop:a},o.map(l=>{const s=(i==null?void 0:i(l))??g.createElement("img",{draggable:!1,src:l});return g.createElement("div",{className:"gdg-centering-container",key:l},s)})),n&&r&&g.createElement("button",{className:"gdg-edit-icon",onClick:r},g.createElement($s,null)))};function id(){return{async:!1,baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,hooks:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Kr=id();function im(e){Kr=e}const od=/[&<>"']/,om=new RegExp(od.source,"g"),ad=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,am=new RegExp(ad.source,"g"),sm={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},tu=e=>sm[e];function wn(e,t){if(t){if(od.test(e))return e.replace(om,tu)}else if(ad.test(e))return e.replace(am,tu);return e}const lm=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig;function sd(e){return e.replace(lm,(t,n)=>(n=n.toLowerCase(),n==="colon"?":":n.charAt(0)==="#"?n.charAt(1)==="x"?String.fromCharCode(parseInt(n.substring(2),16)):String.fromCharCode(+n.substring(1)):""))}const um=/(^|[^\[])\^/g;function Nt(e,t){e=typeof e=="string"?e:e.source,t=t||"";const n={replace:(r,i)=>(i=i.source||i,i=i.replace(um,"$1"),e=e.replace(r,i),n),getRegex:()=>new RegExp(e,t)};return n}const cm=/[^\w:]/g,dm=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function nu(e,t,n){if(e){let r;try{r=decodeURIComponent(sd(n)).replace(cm,"").toLowerCase()}catch{return null}if(r.indexOf("javascript:")===0||r.indexOf("vbscript:")===0||r.indexOf("data:")===0)return null}t&&!dm.test(n)&&(n=mm(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const Vo={},fm=/^[^:]+:\/*[^/]*$/,hm=/^([^:]+:)[\s\S]*$/,gm=/^([^:]+:\/*[^/]*)[\s\S]*$/;function mm(e,t){Vo[" "+e]||(fm.test(e)?Vo[" "+e]=e+"/":Vo[" "+e]=Jo(e,"/",!0)),e=Vo[" "+e];const n=e.indexOf(":")===-1;return t.substring(0,2)==="//"?n?t:e.replace(hm,"$1")+t:t.charAt(0)==="/"?n?t:e.replace(gm,"$1")+t:e+t}const oa={exec:function(){}};function ru(e,t){const n=e.replace(/\|/g,(o,a,l)=>{let s=!1,u=a;for(;--u>=0&&l[u]==="\\";)s=!s;return s?"|":" |"}),r=n.split(/ \|/);let i=0;if(r[0].trim()||r.shift(),r.length>0&&!r[r.length-1].trim()&&r.pop(),r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;i<r.length;i++)r[i]=r[i].trim().replace(/\\\|/g,"|");return r}function Jo(e,t,n){const r=e.length;if(r===0)return"";let i=0;for(;i<r;){const o=e.charAt(r-i-1);if(o===t&&!n)i++;else if(o!==t&&n)i++;else break}return e.slice(0,r-i)}function pm(e,t){if(e.indexOf(t[1])===-1)return-1;const n=e.length;let r=0,i=0;for(;i<n;i++)if(e[i]==="\\")i++;else if(e[i]===t[0])r++;else if(e[i]===t[1]&&(r--,r<0))return i;return-1}function vm(e){e&&e.sanitize&&!e.silent&&console.warn("marked(): sanitize and sanitizer parameters are deprecated since version 0.7.0, should not be used and will be removed in the future. Read more here: https://marked.js.org/#/USING_ADVANCED.md#options")}function iu(e,t){if(t<1)return"";let n="";for(;t>1;)t&1&&(n+=e),t>>=1,e+=e;return n+e}function ou(e,t,n,r){const i=t.href,o=t.title?wn(t.title):null,a=e[1].replace(/\\([\[\]])/g,"$1");if(e[0].charAt(0)!=="!"){r.state.inLink=!0;const l={type:"link",raw:n,href:i,title:o,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,l}return{type:"image",raw:n,href:i,title:o,text:wn(a)}}function bm(e,t){const n=e.match(/^(\s+)(?:```)/);if(n===null)return t;const r=n[1];return t.split(`
`).map(i=>{const o=i.match(/^\s+/);if(o===null)return i;const[a]=o;return a.length>=r.length?i.slice(r.length):i}).join(`
`)}class Bs{constructor(t){this.options=t||Kr}space(t){const n=this.rules.block.newline.exec(t);if(n&&n[0].length>0)return{type:"space",raw:n[0]}}code(t){const n=this.rules.block.code.exec(t);if(n){const r=n[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:n[0],codeBlockStyle:"indented",text:this.options.pedantic?r:Jo(r,`
`)}}}fences(t){const n=this.rules.block.fences.exec(t);if(n){const r=n[0],i=bm(r,n[3]||"");return{type:"code",raw:r,lang:n[2]?n[2].trim().replace(this.rules.inline._escapes,"$1"):n[2],text:i}}}heading(t){const n=this.rules.block.heading.exec(t);if(n){let r=n[2].trim();if(/#$/.test(r)){const i=Jo(r,"#");(this.options.pedantic||!i||/ $/.test(i))&&(r=i.trim())}return{type:"heading",raw:n[0],depth:n[1].length,text:r,tokens:this.lexer.inline(r)}}}hr(t){const n=this.rules.block.hr.exec(t);if(n)return{type:"hr",raw:n[0]}}blockquote(t){const n=this.rules.block.blockquote.exec(t);if(n){const r=n[0].replace(/^ *>[ \t]?/gm,""),i=this.lexer.state.top;this.lexer.state.top=!0;const o=this.lexer.blockTokens(r);return this.lexer.state.top=i,{type:"blockquote",raw:n[0],tokens:o,text:r}}}list(t){let n=this.rules.block.list.exec(t);if(n){let r,i,o,a,l,s,u,c,d,h,f,m,p=n[1].trim();const v=p.length>1,w={type:"list",raw:"",ordered:v,start:v?+p.slice(0,-1):"",loose:!1,items:[]};p=v?`\\d{1,9}\\${p.slice(-1)}`:`\\${p}`,this.options.pedantic&&(p=v?p:"[*+-]");const b=new RegExp(`^( {0,3}${p})((?:[	 ][^\\n]*)?(?:\\n|$))`);for(;t&&(m=!1,!(!(n=b.exec(t))||this.rules.block.hr.test(t)));){if(r=n[0],t=t.substring(r.length),c=n[2].split(`
`,1)[0].replace(/^\t+/,O=>" ".repeat(3*O.length)),d=t.split(`
`,1)[0],this.options.pedantic?(a=2,f=c.trimLeft()):(a=n[2].search(/[^ ]/),a=a>4?1:a,f=c.slice(a),a+=n[1].length),s=!1,!c&&/^ *$/.test(d)&&(r+=d+`
`,t=t.substring(d.length+1),m=!0),!m){const O=new RegExp(`^ {0,${Math.min(3,a-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),S=new RegExp(`^ {0,${Math.min(3,a-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),R=new RegExp(`^ {0,${Math.min(3,a-1)}}(?:\`\`\`|~~~)`),_=new RegExp(`^ {0,${Math.min(3,a-1)}}#`);for(;t&&(h=t.split(`
`,1)[0],d=h,this.options.pedantic&&(d=d.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!(R.test(d)||_.test(d)||O.test(d)||S.test(t)));){if(d.search(/[^ ]/)>=a||!d.trim())f+=`
`+d.slice(a);else{if(s||c.search(/[^ ]/)>=4||R.test(c)||_.test(c)||S.test(c))break;f+=`
`+d}!s&&!d.trim()&&(s=!0),r+=h+`
`,t=t.substring(h.length+1),c=d.slice(a)}}w.loose||(u?w.loose=!0:/\n *\n *$/.test(r)&&(u=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(f),i&&(o=i[0]!=="[ ] ",f=f.replace(/^\[[ xX]\] +/,""))),w.items.push({type:"list_item",raw:r,task:!!i,checked:o,loose:!1,text:f}),w.raw+=r}w.items[w.items.length-1].raw=r.trimRight(),w.items[w.items.length-1].text=f.trimRight(),w.raw=w.raw.trimRight();const M=w.items.length;for(l=0;l<M;l++)if(this.lexer.state.top=!1,w.items[l].tokens=this.lexer.blockTokens(w.items[l].text,[]),!w.loose){const O=w.items[l].tokens.filter(R=>R.type==="space"),S=O.length>0&&O.some(R=>/\n.*\n/.test(R.raw));w.loose=S}if(w.loose)for(l=0;l<M;l++)w.items[l].loose=!0;return w}}html(t){const n=this.rules.block.html.exec(t);if(n){const r={type:"html",raw:n[0],pre:!this.options.sanitizer&&(n[1]==="pre"||n[1]==="script"||n[1]==="style"),text:n[0]};if(this.options.sanitize){const i=this.options.sanitizer?this.options.sanitizer(n[0]):wn(n[0]);r.type="paragraph",r.text=i,r.tokens=this.lexer.inline(i)}return r}}def(t){const n=this.rules.block.def.exec(t);if(n){const r=n[1].toLowerCase().replace(/\s+/g," "),i=n[2]?n[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline._escapes,"$1"):"",o=n[3]?n[3].substring(1,n[3].length-1).replace(this.rules.inline._escapes,"$1"):n[3];return{type:"def",tag:r,raw:n[0],href:i,title:o}}}table(t){const n=this.rules.block.table.exec(t);if(n){const r={type:"table",header:ru(n[1]).map(i=>({text:i})),align:n[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:n[3]&&n[3].trim()?n[3].replace(/\n[ \t]*$/,"").split(`
`):[]};if(r.header.length===r.align.length){r.raw=n[0];let i=r.align.length,o,a,l,s;for(o=0;o<i;o++)/^ *-+: *$/.test(r.align[o])?r.align[o]="right":/^ *:-+: *$/.test(r.align[o])?r.align[o]="center":/^ *:-+ *$/.test(r.align[o])?r.align[o]="left":r.align[o]=null;for(i=r.rows.length,o=0;o<i;o++)r.rows[o]=ru(r.rows[o],r.header.length).map(u=>({text:u}));for(i=r.header.length,a=0;a<i;a++)r.header[a].tokens=this.lexer.inline(r.header[a].text);for(i=r.rows.length,a=0;a<i;a++)for(s=r.rows[a],l=0;l<s.length;l++)s[l].tokens=this.lexer.inline(s[l].text);return r}}}lheading(t){const n=this.rules.block.lheading.exec(t);if(n)return{type:"heading",raw:n[0],depth:n[2].charAt(0)==="="?1:2,text:n[1],tokens:this.lexer.inline(n[1])}}paragraph(t){const n=this.rules.block.paragraph.exec(t);if(n){const r=n[1].charAt(n[1].length-1)===`
`?n[1].slice(0,-1):n[1];return{type:"paragraph",raw:n[0],text:r,tokens:this.lexer.inline(r)}}}text(t){const n=this.rules.block.text.exec(t);if(n)return{type:"text",raw:n[0],text:n[0],tokens:this.lexer.inline(n[0])}}escape(t){const n=this.rules.inline.escape.exec(t);if(n)return{type:"escape",raw:n[0],text:wn(n[1])}}tag(t){const n=this.rules.inline.tag.exec(t);if(n)return!this.lexer.state.inLink&&/^<a /i.test(n[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(n[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(n[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(n[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:n[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):wn(n[0]):n[0]}}link(t){const n=this.rules.inline.link.exec(t);if(n){const r=n[2].trim();if(!this.options.pedantic&&/^</.test(r)){if(!/>$/.test(r))return;const a=Jo(r.slice(0,-1),"\\");if((r.length-a.length)%2===0)return}else{const a=pm(n[2],"()");if(a>-1){const s=(n[0].indexOf("!")===0?5:4)+n[1].length+a;n[2]=n[2].substring(0,a),n[0]=n[0].substring(0,s).trim(),n[3]=""}}let i=n[2],o="";if(this.options.pedantic){const a=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);a&&(i=a[1],o=a[3])}else o=n[3]?n[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(this.options.pedantic&&!/>$/.test(r)?i=i.slice(1):i=i.slice(1,-1)),ou(n,{href:i&&i.replace(this.rules.inline._escapes,"$1"),title:o&&o.replace(this.rules.inline._escapes,"$1")},n[0],this.lexer)}}reflink(t,n){let r;if((r=this.rules.inline.reflink.exec(t))||(r=this.rules.inline.nolink.exec(t))){let i=(r[2]||r[1]).replace(/\s+/g," ");if(i=n[i.toLowerCase()],!i){const o=r[0].charAt(0);return{type:"text",raw:o,text:o}}return ou(r,i,r[0],this.lexer)}}emStrong(t,n,r=""){let i=this.rules.inline.emStrong.lDelim.exec(t);if(!i||i[3]&&r.match(/[\p{L}\p{N}]/u))return;const o=i[1]||i[2]||"";if(!o||o&&(r===""||this.rules.inline.punctuation.exec(r))){const a=i[0].length-1;let l,s,u=a,c=0;const d=i[0][0]==="*"?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(d.lastIndex=0,n=n.slice(-1*t.length+a);(i=d.exec(n))!=null;){if(l=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!l)continue;if(s=l.length,i[3]||i[4]){u+=s;continue}else if((i[5]||i[6])&&a%3&&!((a+s)%3)){c+=s;continue}if(u-=s,u>0)continue;s=Math.min(s,s+u+c);const h=t.slice(0,a+i.index+(i[0].length-l.length)+s);if(Math.min(a,s)%2){const m=h.slice(1,-1);return{type:"em",raw:h,text:m,tokens:this.lexer.inlineTokens(m)}}const f=h.slice(2,-2);return{type:"strong",raw:h,text:f,tokens:this.lexer.inlineTokens(f)}}}}codespan(t){const n=this.rules.inline.code.exec(t);if(n){let r=n[2].replace(/\n/g," ");const i=/[^ ]/.test(r),o=/^ /.test(r)&&/ $/.test(r);return i&&o&&(r=r.substring(1,r.length-1)),r=wn(r,!0),{type:"codespan",raw:n[0],text:r}}}br(t){const n=this.rules.inline.br.exec(t);if(n)return{type:"br",raw:n[0]}}del(t){const n=this.rules.inline.del.exec(t);if(n)return{type:"del",raw:n[0],text:n[2],tokens:this.lexer.inlineTokens(n[2])}}autolink(t,n){const r=this.rules.inline.autolink.exec(t);if(r){let i,o;return r[2]==="@"?(i=wn(this.options.mangle?n(r[1]):r[1]),o="mailto:"+i):(i=wn(r[1]),o=i),{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}url(t,n){let r;if(r=this.rules.inline.url.exec(t)){let i,o;if(r[2]==="@")i=wn(this.options.mangle?n(r[0]):r[0]),o="mailto:"+i;else{let a;do a=r[0],r[0]=this.rules.inline._backpedal.exec(r[0])[0];while(a!==r[0]);i=wn(r[0]),r[1]==="www."?o="http://"+r[0]:o=r[0]}return{type:"link",raw:r[0],text:i,href:o,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t,n){const r=this.rules.inline.text.exec(t);if(r){let i;return this.lexer.state.inRawBlock?i=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(r[0]):wn(r[0]):r[0]:i=wn(this.options.smartypants?n(r[0]):r[0]),{type:"text",raw:r[0],text:i}}}}const Ge={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:oa,lheading:/^((?:.|\n(?!\n))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/};Ge._label=/(?!\s*\])(?:\\.|[^\[\]\\])+/;Ge._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/;Ge.def=Nt(Ge.def).replace("label",Ge._label).replace("title",Ge._title).getRegex();Ge.bullet=/(?:[*+-]|\d{1,9}[.)])/;Ge.listItemStart=Nt(/^( *)(bull) */).replace("bull",Ge.bullet).getRegex();Ge.list=Nt(Ge.list).replace(/bull/g,Ge.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+Ge.def.source+")").getRegex();Ge._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul";Ge._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/;Ge.html=Nt(Ge.html,"i").replace("comment",Ge._comment).replace("tag",Ge._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex();Ge.paragraph=Nt(Ge._paragraph).replace("hr",Ge.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ge._tag).getRegex();Ge.blockquote=Nt(Ge.blockquote).replace("paragraph",Ge.paragraph).getRegex();Ge.normal={...Ge};Ge.gfm={...Ge.normal,table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"};Ge.gfm.table=Nt(Ge.gfm.table).replace("hr",Ge.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ge._tag).getRegex();Ge.gfm.paragraph=Nt(Ge._paragraph).replace("hr",Ge.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",Ge.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ge._tag).getRegex();Ge.pedantic={...Ge.normal,html:Nt(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ge._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:oa,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Nt(Ge.normal._paragraph).replace("hr",Ge.hr).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Ge.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()};const Ie={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:oa,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^(?:[^_*\\]|\\.)*?\_\_(?:[^_*\\]|\\.)*?\*(?:[^_*\\]|\\.)*?(?=\_\_)|(?:[^*\\]|\\.)+(?=[^*])|[punct_](\*+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|(?:[^punct*_\s\\]|\\.)(\*+)(?=[^punct*_\s])/,rDelimUnd:/^(?:[^_*\\]|\\.)*?\*\*(?:[^_*\\]|\\.)*?\_(?:[^_*\\]|\\.)*?(?=\*\*)|(?:[^_\\]|\\.)+(?=[^_])|[punct*](\_+)(?=[\s]|$)|(?:[^punct*_\s\\]|\\.)(\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:oa,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};Ie._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~";Ie.punctuation=Nt(Ie.punctuation).replace(/punctuation/g,Ie._punctuation).getRegex();Ie.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g;Ie.escapedEmSt=/(?:^|[^\\])(?:\\\\)*\\[*_]/g;Ie._comment=Nt(Ge._comment).replace("(?:-->|$)","-->").getRegex();Ie.emStrong.lDelim=Nt(Ie.emStrong.lDelim).replace(/punct/g,Ie._punctuation).getRegex();Ie.emStrong.rDelimAst=Nt(Ie.emStrong.rDelimAst,"g").replace(/punct/g,Ie._punctuation).getRegex();Ie.emStrong.rDelimUnd=Nt(Ie.emStrong.rDelimUnd,"g").replace(/punct/g,Ie._punctuation).getRegex();Ie._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g;Ie._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/;Ie._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/;Ie.autolink=Nt(Ie.autolink).replace("scheme",Ie._scheme).replace("email",Ie._email).getRegex();Ie._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/;Ie.tag=Nt(Ie.tag).replace("comment",Ie._comment).replace("attribute",Ie._attribute).getRegex();Ie._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/;Ie._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/;Ie._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/;Ie.link=Nt(Ie.link).replace("label",Ie._label).replace("href",Ie._href).replace("title",Ie._title).getRegex();Ie.reflink=Nt(Ie.reflink).replace("label",Ie._label).replace("ref",Ge._label).getRegex();Ie.nolink=Nt(Ie.nolink).replace("ref",Ge._label).getRegex();Ie.reflinkSearch=Nt(Ie.reflinkSearch,"g").replace("reflink",Ie.reflink).replace("nolink",Ie.nolink).getRegex();Ie.normal={...Ie};Ie.pedantic={...Ie.normal,strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:Nt(/^!?\[(label)\]\((.*?)\)/).replace("label",Ie._label).getRegex(),reflink:Nt(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ie._label).getRegex()};Ie.gfm={...Ie.normal,escape:Nt(Ie.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/};Ie.gfm.url=Nt(Ie.gfm.url,"i").replace("email",Ie.gfm._extended_email).getRegex();Ie.breaks={...Ie.gfm,br:Nt(Ie.br).replace("{2,}","*").getRegex(),text:Nt(Ie.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()};function wm(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function au(e){let t="",n,r;const i=e.length;for(n=0;n<i;n++)r=e.charCodeAt(n),Math.random()>.5&&(r="x"+r.toString(16)),t+="&#"+r+";";return t}class _r{constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Kr,this.options.tokenizer=this.options.tokenizer||new Bs,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const n={block:Ge.normal,inline:Ie.normal};this.options.pedantic?(n.block=Ge.pedantic,n.inline=Ie.pedantic):this.options.gfm&&(n.block=Ge.gfm,this.options.breaks?n.inline=Ie.breaks:n.inline=Ie.gfm),this.tokenizer.rules=n}static get rules(){return{block:Ge,inline:Ie}}static lex(t,n){return new _r(n).lex(t)}static lexInline(t,n){return new _r(n).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);let n;for(;n=this.inlineQueue.shift();)this.inlineTokens(n.src,n.tokens);return this.tokens}blockTokens(t,n=[]){this.options.pedantic?t=t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t=t.replace(/^( *)(\t+)/gm,(l,s,u)=>s+"    ".repeat(u.length));let r,i,o,a;for(;t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>(r=l.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.space(t)){t=t.substring(r.raw.length),r.raw.length===1&&n.length>0?n[n.length-1].raw+=`
`:n.push(r);continue}if(r=this.tokenizer.code(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(r=this.tokenizer.fences(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.heading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.hr(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.blockquote(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.list(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.html(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.def(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&(i.type==="paragraph"||i.type==="text")?(i.raw+=`
`+r.raw,i.text+=`
`+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if(r=this.tokenizer.table(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.lheading(t)){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const s=t.slice(1);let u;this.options.extensions.startBlock.forEach(function(c){u=c.call({lexer:this},s),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(o=t.substring(0,l+1))}if(this.state.top&&(r=this.tokenizer.paragraph(o))){i=n[n.length-1],a&&i.type==="paragraph"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r),a=o.length!==t.length,t=t.substring(r.raw.length);continue}if(r=this.tokenizer.text(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&i.type==="text"?(i.raw+=`
`+r.raw,i.text+=`
`+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):n.push(r);continue}if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r,i,o,a=t,l,s,u;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(l=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(l[0].slice(l[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,l.index)+"["+iu("a",l[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(l=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,l.index)+"["+iu("a",l[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(l=this.tokenizer.rules.inline.escapedEmSt.exec(a))!=null;)a=a.slice(0,l.index+l[0].length-2)+"++"+a.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex),this.tokenizer.rules.inline.escapedEmSt.lastIndex--;for(;t;)if(s||(u=""),s=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>(r=c.call({lexer:this},t,n))?(t=t.substring(r.raw.length),n.push(r),!0):!1))){if(r=this.tokenizer.escape(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.tag(t)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.link(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(r.raw.length),i=n[n.length-1],i&&r.type==="text"&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(r=this.tokenizer.emStrong(t,a,u)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.codespan(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.br(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.del(t)){t=t.substring(r.raw.length),n.push(r);continue}if(r=this.tokenizer.autolink(t,au)){t=t.substring(r.raw.length),n.push(r);continue}if(!this.state.inLink&&(r=this.tokenizer.url(t,au))){t=t.substring(r.raw.length),n.push(r);continue}if(o=t,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=t.slice(1);let h;this.options.extensions.startInline.forEach(function(f){h=f.call({lexer:this},d),typeof h=="number"&&h>=0&&(c=Math.min(c,h))}),c<1/0&&c>=0&&(o=t.substring(0,c+1))}if(r=this.tokenizer.inlineText(o,wm)){t=t.substring(r.raw.length),r.raw.slice(-1)!=="_"&&(u=r.raw.slice(-1)),s=!0,i=n[n.length-1],i&&i.type==="text"?(i.raw+=r.raw,i.text+=r.text):n.push(r);continue}if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}else throw new Error(c)}}return n}}class Ws{constructor(t){this.options=t||Kr}code(t,n,r){const i=(n||"").match(/\S*/)[0];if(this.options.highlight){const o=this.options.highlight(t,i);o!=null&&o!==t&&(r=!0,t=o)}return t=t.replace(/\n$/,"")+`
`,i?'<pre><code class="'+this.options.langPrefix+wn(i)+'">'+(r?t:wn(t,!0))+`</code></pre>
`:"<pre><code>"+(r?t:wn(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t){return t}heading(t,n,r,i){if(this.options.headerIds){const o=this.options.headerPrefix+i.slug(r);return`<h${n} id="${o}">${t}</h${n}>
`}return`<h${n}>${t}</h${n}>
`}hr(){return this.options.xhtml?`<hr/>
`:`<hr>
`}list(t,n,r){const i=n?"ol":"ul",o=n&&r!==1?' start="'+r+'"':"";return"<"+i+o+`>
`+t+"</"+i+`>
`}listitem(t){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(t){return`<p>${t}</p>
`}table(t,n){return n&&(n=`<tbody>${n}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+n+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,n){const r=n.header?"th":"td";return(n.align?`<${r} align="${n.align}">`:`<${r}>`)+t+`</${r}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(t){return`<del>${t}</del>`}link(t,n,r){if(t=nu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i='<a href="'+t+'"';return n&&(i+=' title="'+n+'"'),i+=">"+r+"</a>",i}image(t,n,r){if(t=nu(this.options.sanitize,this.options.baseUrl,t),t===null)return r;let i=`<img src="${t}" alt="${r}"`;return n&&(i+=` title="${n}"`),i+=this.options.xhtml?"/>":">",i}text(t){return t}}class ld{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,n,r){return""+r}image(t,n,r){return""+r}br(){return""}}class ud{constructor(){this.seen={}}serialize(t){return t.toLowerCase().trim().replace(/<[!\/a-z].*?>/ig,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(t,n){let r=t,i=0;if(this.seen.hasOwnProperty(r)){i=this.seen[t];do i++,r=t+"-"+i;while(this.seen.hasOwnProperty(r))}return n||(this.seen[t]=i,this.seen[r]=0),r}slug(t,n={}){const r=this.serialize(t);return this.getNextSafeSlug(r,n.dryrun)}}class Lr{constructor(t){this.options=t||Kr,this.options.renderer=this.options.renderer||new Ws,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new ld,this.slugger=new ud}static parse(t,n){return new Lr(n).parse(t)}static parseInline(t,n){return new Lr(n).parseInline(t)}parse(t,n=!0){let r="",i,o,a,l,s,u,c,d,h,f,m,p,v,w,b,M,O,S,R;const _=t.length;for(i=0;i<_;i++){if(f=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[f.type]&&(R=this.options.extensions.renderers[f.type].call({parser:this},f),R!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(f.type))){r+=R||"";continue}switch(f.type){case"space":continue;case"hr":{r+=this.renderer.hr();continue}case"heading":{r+=this.renderer.heading(this.parseInline(f.tokens),f.depth,sd(this.parseInline(f.tokens,this.textRenderer)),this.slugger);continue}case"code":{r+=this.renderer.code(f.text,f.lang,f.escaped);continue}case"table":{for(d="",c="",l=f.header.length,o=0;o<l;o++)c+=this.renderer.tablecell(this.parseInline(f.header[o].tokens),{header:!0,align:f.align[o]});for(d+=this.renderer.tablerow(c),h="",l=f.rows.length,o=0;o<l;o++){for(u=f.rows[o],c="",s=u.length,a=0;a<s;a++)c+=this.renderer.tablecell(this.parseInline(u[a].tokens),{header:!1,align:f.align[a]});h+=this.renderer.tablerow(c)}r+=this.renderer.table(d,h);continue}case"blockquote":{h=this.parse(f.tokens),r+=this.renderer.blockquote(h);continue}case"list":{for(m=f.ordered,p=f.start,v=f.loose,l=f.items.length,h="",o=0;o<l;o++)b=f.items[o],M=b.checked,O=b.task,w="",b.task&&(S=this.renderer.checkbox(M),v?b.tokens.length>0&&b.tokens[0].type==="paragraph"?(b.tokens[0].text=S+" "+b.tokens[0].text,b.tokens[0].tokens&&b.tokens[0].tokens.length>0&&b.tokens[0].tokens[0].type==="text"&&(b.tokens[0].tokens[0].text=S+" "+b.tokens[0].tokens[0].text)):b.tokens.unshift({type:"text",text:S}):w+=S),w+=this.parse(b.tokens,v),h+=this.renderer.listitem(w,O,M);r+=this.renderer.list(h,m,p);continue}case"html":{r+=this.renderer.html(f.text);continue}case"paragraph":{r+=this.renderer.paragraph(this.parseInline(f.tokens));continue}case"text":{for(h=f.tokens?this.parseInline(f.tokens):f.text;i+1<_&&t[i+1].type==="text";)f=t[++i],h+=`
`+(f.tokens?this.parseInline(f.tokens):f.text);r+=n?this.renderer.paragraph(h):h;continue}default:{const E='Token with "'+f.type+'" type was not found.';if(this.options.silent){console.error(E);return}else throw new Error(E)}}}return r}parseInline(t,n){n=n||this.renderer;let r="",i,o,a;const l=t.length;for(i=0;i<l;i++){if(o=t[i],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]&&(a=this.options.extensions.renderers[o.type].call({parser:this},o),a!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type))){r+=a||"";continue}switch(o.type){case"escape":{r+=n.text(o.text);break}case"html":{r+=n.html(o.text);break}case"link":{r+=n.link(o.href,o.title,this.parseInline(o.tokens,n));break}case"image":{r+=n.image(o.href,o.title,o.text);break}case"strong":{r+=n.strong(this.parseInline(o.tokens,n));break}case"em":{r+=n.em(this.parseInline(o.tokens,n));break}case"codespan":{r+=n.codespan(o.text);break}case"br":{r+=n.br();break}case"del":{r+=n.del(this.parseInline(o.tokens,n));break}case"text":{r+=n.text(o.text);break}default:{const s='Token with "'+o.type+'" type was not found.';if(this.options.silent){console.error(s);return}else throw new Error(s)}}}return r}}class aa{constructor(t){this.options=t||Kr}preprocess(t){return t}postprocess(t){return t}}ft(aa,"passThroughHooks",new Set(["preprocess","postprocess"]));function ym(e,t,n){return r=>{if(r.message+=`
Please report this to https://github.com/markedjs/marked.`,e){const i="<p>An error occurred:</p><pre>"+wn(r.message+"",!0)+"</pre>";if(t)return Promise.resolve(i);if(n){n(null,i);return}return i}if(t)return Promise.reject(r);if(n){n(r);return}throw r}}function cd(e,t){return(n,r,i)=>{typeof r=="function"&&(i=r,r=null);const o={...r};r={...Je.defaults,...o};const a=ym(r.silent,r.async,i);if(typeof n>"u"||n===null)return a(new Error("marked(): input parameter is undefined or null"));if(typeof n!="string")return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(n)+", string expected"));if(vm(r),r.hooks&&(r.hooks.options=r),i){const l=r.highlight;let s;try{r.hooks&&(n=r.hooks.preprocess(n)),s=e(n,r)}catch(d){return a(d)}const u=function(d){let h;if(!d)try{r.walkTokens&&Je.walkTokens(s,r.walkTokens),h=t(s,r),r.hooks&&(h=r.hooks.postprocess(h))}catch(f){d=f}return r.highlight=l,d?a(d):i(null,h)};if(!l||l.length<3||(delete r.highlight,!s.length))return u();let c=0;Je.walkTokens(s,function(d){d.type==="code"&&(c++,setTimeout(()=>{l(d.text,d.lang,function(h,f){if(h)return u(h);f!=null&&f!==d.text&&(d.text=f,d.escaped=!0),c--,c===0&&u()})},0))}),c===0&&u();return}if(r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(n):n).then(l=>e(l,r)).then(l=>r.walkTokens?Promise.all(Je.walkTokens(l,r.walkTokens)).then(()=>l):l).then(l=>t(l,r)).then(l=>r.hooks?r.hooks.postprocess(l):l).catch(a);try{r.hooks&&(n=r.hooks.preprocess(n));const l=e(n,r);r.walkTokens&&Je.walkTokens(l,r.walkTokens);let s=t(l,r);return r.hooks&&(s=r.hooks.postprocess(s)),s}catch(l){return a(l)}}}function Je(e,t,n){return cd(_r.lex,Lr.parse)(e,t,n)}Je.options=Je.setOptions=function(e){return Je.defaults={...Je.defaults,...e},im(Je.defaults),Je};Je.getDefaults=id;Je.defaults=Kr;Je.use=function(...e){const t=Je.defaults.extensions||{renderers:{},childTokens:{}};e.forEach(n=>{const r={...n};if(r.async=Je.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if(i.renderer){const o=t.renderers[i.name];o?t.renderers[i.name]=function(...a){let l=i.renderer.apply(this,a);return l===!1&&(l=o.apply(this,a)),l}:t.renderers[i.name]=i.renderer}if(i.tokenizer){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");t[i.level]?t[i.level].unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),r.extensions=t),n.renderer){const i=Je.defaults.renderer||new Ws;for(const o in n.renderer){const a=i[o];i[o]=(...l)=>{let s=n.renderer[o].apply(i,l);return s===!1&&(s=a.apply(i,l)),s}}r.renderer=i}if(n.tokenizer){const i=Je.defaults.tokenizer||new Bs;for(const o in n.tokenizer){const a=i[o];i[o]=(...l)=>{let s=n.tokenizer[o].apply(i,l);return s===!1&&(s=a.apply(i,l)),s}}r.tokenizer=i}if(n.hooks){const i=Je.defaults.hooks||new aa;for(const o in n.hooks){const a=i[o];aa.passThroughHooks.has(o)?i[o]=l=>{if(Je.defaults.async)return Promise.resolve(n.hooks[o].call(i,l)).then(u=>a.call(i,u));const s=n.hooks[o].call(i,l);return a.call(i,s)}:i[o]=(...l)=>{let s=n.hooks[o].apply(i,l);return s===!1&&(s=a.apply(i,l)),s}}r.hooks=i}if(n.walkTokens){const i=Je.defaults.walkTokens;r.walkTokens=function(o){let a=[];return a.push(n.walkTokens.call(this,o)),i&&(a=a.concat(i.call(this,o))),a}}Je.setOptions(r)})};Je.walkTokens=function(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(Je,r)),r.type){case"table":{for(const i of r.header)n=n.concat(Je.walkTokens(i.tokens,t));for(const i of r.rows)for(const o of i)n=n.concat(Je.walkTokens(o.tokens,t));break}case"list":{n=n.concat(Je.walkTokens(r.items,t));break}default:Je.defaults.extensions&&Je.defaults.extensions.childTokens&&Je.defaults.extensions.childTokens[r.type]?Je.defaults.extensions.childTokens[r.type].forEach(function(i){n=n.concat(Je.walkTokens(r[i],t))}):r.tokens&&(n=n.concat(Je.walkTokens(r.tokens,t)))}return n};Je.parseInline=cd(_r.lexInline,Lr.parseInline);Je.Parser=Lr;Je.parser=Lr.parse;Je.Renderer=Ws;Je.TextRenderer=ld;Je.Lexer=_r;Je.lexer=_r.lex;Je.Tokenizer=Bs;Je.Slugger=ud;Je.Hooks=aa;Je.parse=Je;Je.options;Je.setOptions;Je.use;Je.walkTokens;Je.parseInline;Lr.parse;_r.lex;const Cm=mn("div")({name:"MarkdownContainer",class:"gdg-mnuv029",propsAsIs:!1});class Sm extends ie.PureComponent{constructor(){super(...arguments);ft(this,"targetElement",null);ft(this,"containerRefHook",n=>{this.targetElement=n,this.renderMarkdownIntoDiv()})}renderMarkdownIntoDiv(){const{targetElement:n,props:r}=this;if(n===null)return;const{contents:i,createNode:o}=r,a=Je(i),l=document.createRange();l.selectNodeContents(n),l.deleteContents();let s=o==null?void 0:o(a);if(s===void 0){const c=document.createElement("template");c.innerHTML=a,s=c.content}n.append(s);const u=n.getElementsByTagName("a");for(const c of u)c.target="_blank",c.rel="noreferrer noopener"}render(){return this.renderMarkdownIntoDiv(),ie.createElement(Cm,{ref:this.containerRefHook})}}const xm=mn("textarea")({name:"InputBox",class:"gdg-izpuzkl",propsAsIs:!1}),km=mn("div")({name:"ShadowBox",class:"gdg-s69h75o",propsAsIs:!1}),Mm=mn("div")({name:"GrowingEntryStyle",class:"gdg-g1y0xocz",propsAsIs:!1});let su=0;const Zr=e=>{const{placeholder:t,value:n,onKeyDown:r,highlight:i,altNewline:o,validatedSelection:a,...l}=e,{onChange:s,className:u}=l,c=g.useRef(null),d=n??"";Fn(s!==void 0,"GrowingEntry must be a controlled input area");const[h]=g.useState(()=>"input-box-"+(su=(su+1)%1e7));g.useEffect(()=>{const m=c.current;if(m===null||m.disabled)return;const p=d.toString().length;m.focus(),m.setSelectionRange(i?0:p,p)},[]),g.useLayoutEffect(()=>{var m;if(a!==void 0){const p=typeof a=="number"?[a,null]:a;(m=c.current)==null||m.setSelectionRange(p[0],p[1])}},[a]);const f=g.useCallback(m=>{m.key==="Enter"&&m.shiftKey&&o===!0||r==null||r(m)},[o,r]);return g.createElement(Mm,{className:"gdg-growing-entry"},g.createElement(km,{className:u},d+`
`),g.createElement(xm,{...l,className:(u??"")+" gdg-input",id:h,ref:c,onKeyDown:f,value:d,placeholder:t,dir:"auto"}))},Qa={};let Tr=null;function Rm(){const e=document.createElement("div");return e.style.opacity="0",e.style.pointerEvents="none",e.style.position="fixed",document.body.append(e),e}function sa(e){const t=e.toLowerCase().trim();if(Qa[t]!==void 0)return Qa[t];Tr=Tr||Rm(),Tr.style.color="#000",Tr.style.color=t;const n=getComputedStyle(Tr).color;Tr.style.color="#fff",Tr.style.color=t;const r=getComputedStyle(Tr).color;if(r!==n)return[0,0,0,1];let i=r.replace(/[^\d.,]/g,"").split(",").map(Number.parseFloat);return i.length<4&&i.push(1),i=i.map(o=>Number.isNaN(o)?0:o),Qa[t]=i,i}function Gr(e,t){const[n,r,i]=sa(e);return`rgba(${n}, ${r}, ${i}, ${t})`}const lu=new Map;function uu(e,t){const n=`${e}-${t}`,r=lu.get(n);if(r!==void 0)return r;const i=An(e,t);return lu.set(n,i),i}function An(e,t){if(t===void 0)return e;const[n,r,i,o]=sa(e);if(o===1)return e;const[a,l,s,u]=sa(t),c=o+u*(1-o),d=(o*n+u*a*(1-o))/c,h=(o*r+u*l*(1-o))/c,f=(o*i+u*s*(1-o))/c;return`rgba(${d}, ${h}, ${f}, ${c})`}var gi=new Map,pi=new Map,xs=new Map;function Em(){gi.clear(),xs.clear(),pi.clear()}function Im(e,t,n,r,i){var o,a,l;let s=0,u={};for(let d of e)s+=(o=n.get(d))!=null?o:i,u[d]=((a=u[d])!=null?a:0)+1;let c=t-s;for(let d of Object.keys(u)){let h=u[d],f=(l=n.get(d))!=null?l:i,m=f*h/s,p=c*m*r/h,v=f+p;n.set(d,v)}}function Tm(e,t){var n;let r=new Map,i=0;for(let u of"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,.-+=?"){let c=e.measureText(u).width;r.set(u,c),i+=c}let o=i/r.size,a=3,l=(t/o+a)/(a+1),s=r.keys();for(let u of s)r.set(u,((n=r.get(u))!=null?n:o)*l);return r}function Qi(e,t,n,r){var i,o;let a=pi.get(n);if(r&&a!==void 0&&a.count>2e4){let u=xs.get(n);if(u===void 0&&(u=Tm(e,a.size),xs.set(n,u)),a.count>5e5){let d=0;for(let h of t)d+=(i=u.get(h))!=null?i:a.size;return d*1.01}let c=e.measureText(t);return Im(t,c.width,u,Math.max(.05,1-a.count/2e5),a.size),pi.set(n,{count:a.count+t.length,size:a.size}),c.width}let l=e.measureText(t),s=l.width/t.length;if(((o=a==null?void 0:a.count)!=null?o:0)>2e4)return l.width;if(a===void 0)pi.set(n,{count:t.length,size:s});else{let u=s-a.size,c=t.length/(a.count+t.length),d=a.size+u*c;pi.set(n,{count:a.count+t.length,size:d})}return l.width}function Dm(e,t,n,r,i,o,a,l){if(t.length<=1)return t.length;if(i<n)return-1;let s=Math.floor(n/i*o),u=Qi(e,t.slice(0,Math.max(0,s)),r,a);if(u!==n)if(u<n){for(;u<n;)s++,u=Qi(e,t.slice(0,Math.max(0,s)),r,a);s--}else for(;u>n;){let c=t.lastIndexOf(" ",s-1);c>0?s=c:s--,u=Qi(e,t.slice(0,Math.max(0,s)),r,a)}if(t[s]!==" "){let c=0;c=t.lastIndexOf(" ",s),c>0&&(s=c)}return s}function Om(e,t,n,r,i,o){let a=`${t}_${n}_${r}px`,l=gi.get(a);if(l!==void 0)return l;if(r<=0)return[];let s=[],u=t.split(`
`),c=pi.get(n),d=c===void 0?t.length:r/c.size*1.5,h=i&&c!==void 0&&c.count>2e4;for(let f of u){let m=Qi(e,f.slice(0,Math.max(0,d)),n,h),p=Math.min(f.length,d);if(m<=r)s.push(f);else{for(;m>r;){let v=Dm(e,f,r,n,m,p,h),w=f.slice(0,Math.max(0,v));f=f.slice(w.length),s.push(w),m=Qi(e,f.slice(0,Math.max(0,d)),n,h),p=Math.min(f.length,d)}m>0&&s.push(f)}}return s=s.map((f,m)=>m===0?f.trimEnd():f.trim()),gi.set(a,s),gi.size>500&&gi.delete(gi.keys().next().value),s}function Pm(e,t){return ie.useMemo(()=>e.map((n,r)=>({group:n.group,grow:n.grow,hasMenu:n.hasMenu,icon:n.icon,id:n.id,menuIcon:n.menuIcon,overlayIcon:n.overlayIcon,sourceIndex:r,sticky:r<t,indicatorIcon:n.indicatorIcon,style:n.style,themeOverride:n.themeOverride,title:n.title,trailingRowOptions:n.trailingRowOptions,width:n.width,growOffset:n.growOffset,rowMarker:n.rowMarker,rowMarkerChecked:n.rowMarkerChecked,headerRowMarkerTheme:n.headerRowMarkerTheme,headerRowMarkerAlwaysVisible:n.headerRowMarkerAlwaysVisible,headerRowMarkerDisabled:n.headerRowMarkerDisabled})),[e,t])}function _m(e,t){const[n,r]=t;if(e.columns.hasIndex(n)||e.rows.hasIndex(r))return!0;if(e.current!==void 0){if(eo(e.current.cell,t))return!0;const i=[e.current.range,...e.current.rangeStack];for(const o of i)if(n>=o.x&&n<o.x+o.width&&r>=o.y&&r<o.y+o.height)return!0}return!1}function oo(e,t){return(e??"")===(t??"")}function Lm(e,t,n){return n.current===void 0||e[1]!==n.current.cell[1]?!1:t.span===void 0?n.current.cell[0]===e[0]:n.current.cell[0]>=t.span[0]&&n.current.cell[0]<=t.span[1]}function dd(e,t){const[n,r]=e;return n>=t.x&&n<t.x+t.width&&r>=t.y&&r<t.y+t.height}function eo(e,t){return(e==null?void 0:e[0])===(t==null?void 0:t[0])&&(e==null?void 0:e[1])===(t==null?void 0:t[1])}function fd(e){return[e.x+e.width-1,e.y+e.height-1]}function cu(e,t,n){const r=n.x,i=n.x+n.width-1,o=n.y,a=n.y+n.height-1,[l,s]=e;if(s<o||s>a)return!1;if(t.span===void 0)return l>=r&&l<=i;const[u,c]=t.span;return u>=r&&u<=i||c>=r&&u<=i||u<r&&c>i}function Fm(e,t,n,r){let i=0;if(n.current===void 0)return i;const o=n.current.range;(r||o.height*o.width>1)&&cu(e,t,o)&&i++;for(const a of n.current.rangeStack)cu(e,t,a)&&i++;return i}function hd(e,t){let n=e;if(t!==void 0){let r=[...e];const i=n[t.src];t.src>t.dest?(r.splice(t.src,1),r.splice(t.dest,0,i)):(r.splice(t.dest+1,0,i),r.splice(t.src,1)),r=r.map((o,a)=>({...o,sticky:e[a].sticky})),n=r}return n}function yi(e,t){let n=0;const r=hd(e,t);for(let i=0;i<r.length;i++){const o=r[i];if(o.sticky)n+=o.width;else break}return n}function Jr(e,t,n){if(typeof n=="number")return t*n;{let r=0;for(let i=e-t;i<e;i++)r+=n(i);return r}}function ks(e,t,n,r,i){const o=hd(e,r),a=[];for(const u of o)if(u.sticky)a.push(u);else break;if(a.length>0)for(const u of a)n-=u.width;let l=t,s=i??0;for(;s<=n&&l<o.length;)s+=o[l].width,l++;for(let u=t;u<l;u++){const c=o[u];c.sticky||a.push(c)}return a}function Am(e,t,n){let r=0;for(const i of t){const o=i.sticky?r:r+(n??0);if(e<=o+i.width)return i.sourceIndex;r+=i.width}return-1}function Hm(e,t,n,r,i,o,a,l,s,u){const c=r+i;if(n&&e<=i)return-2;if(e<=c)return-1;let d=t;for(let m=0;m<u;m++){const p=o-1-m,v=typeof a=="number"?a:a(p);if(d-=v,e>=d)return p}const h=o-u,f=e-(s??0);if(typeof a=="number"){const m=Math.floor((f-c)/a)+l;return m>=h?void 0:m}else{let m=c;for(let p=l;p<h;p++){const v=a(p);if(f<=m+v)return p;m+=v}return}}let Qo=0,to={};const zm=typeof window>"u";async function Vm(){var e;zm||((e=document==null?void 0:document.fonts)==null?void 0:e.ready)===void 0||(await document.fonts.ready,Qo=0,to={},Em())}Vm();function gd(e,t,n,r){return`${e}_${r??(t==null?void 0:t.font)}_${n}`}function Qr(e,t,n,r="middle"){const i=gd(e,t,r,n);let o=to[i];return o===void 0&&(o=t.measureText(e),to[i]=o,Qo++),Qo>1e4&&(to={},Qo=0),o}function md(e,t){const n=gd(e,void 0,"middle",t);return to[n]}function dr(e,t){return typeof t!="string"&&(t=t.baseFontFull),$m(e,t)}function du(e,t){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZ";e.save(),e.textBaseline=t;const r=e.measureText(n);return e.restore(),r}const fu=[];function $m(e,t){for(const o of fu)if(o.key===t)return o.val;const n=du(e,"alphabetic"),i=-(du(e,"middle").actualBoundingBoxDescent-n.actualBoundingBoxDescent)+n.actualBoundingBoxAscent/2;return fu.push({key:t,val:i}),i}function Nm(e,t,n,r,i,o){const{ctx:a,rect:l,theme:s}=e;let u=Number.MAX_SAFE_INTEGER;const c=500;if(t!==void 0&&(u=n-t,u<c)){const d=1-u/c;a.globalAlpha=d,a.fillStyle=s.bgSearchResult,a.fillRect(l.x+1,l.y+1,l.width-(i?2:1),l.height-(o?2:1)),a.globalAlpha=1,r!==void 0&&(r.fillStyle=s.bgSearchResult)}return u<c}function ho(e,t,n){const{ctx:r,theme:i}=e,o=t??{},a=n??i.textDark;return a!==o.fillStyle&&(r.fillStyle=a,o.fillStyle=a),o}function Us(e,t,n){const{rect:r,ctx:i,theme:o}=e;i.fillStyle=o.textDark,ur({ctx:i,rect:r,theme:o},t,n)}function pd(e,t,n,r,i,o,a,l,s){s==="right"?e.fillText(t,n+i-(l.cellHorizontalPadding+.5),r+o/2+a):s==="center"?e.fillText(t,n+i/2,r+o/2+a):e.fillText(t,n+l.cellHorizontalPadding+.5,r+o/2+a)}function vd(e,t){const n=Qr("ABCi09jgqpy",e,t);return n.actualBoundingBoxAscent+n.actualBoundingBoxDescent}function Bm(e,t){e.includes(`
`)&&(e=e.split(/\r?\n/,1)[0]);const n=t/4;return e.length>n&&(e=e.slice(0,n)),e}function Wm(e,t,n,r,i,o,a,l,s,u){const c=l.baseFontFull,d=Om(e,t,c,i-l.cellHorizontalPadding*2,u??!1),h=vd(e,c),f=l.lineHeight*h,m=h+f*(d.length-1),p=m+l.cellVerticalPadding>o;p&&(e.save(),e.rect(n,r,i,o),e.clip());const v=r+o/2-m/2;let w=Math.max(r+l.cellVerticalPadding,v);for(const b of d)if(pd(e,b,n,w,i,h,a,l,s),w+=f,w>r+o)break;p&&e.restore()}function ur(e,t,n,r,i){const{ctx:o,rect:a,theme:l}=e,{x:s,y:u,width:c,height:d}=a;r=r??!1,r||(t=Bm(t,c));const h=dr(o,l),f=Ns(t)==="rtl";if(n===void 0&&f&&(n="right"),f&&(o.direction="rtl"),t.length>0){let m=!1;n==="right"?(o.textAlign="right",m=!0):n!==void 0&&n!=="left"&&(o.textAlign=n,m=!0),r?Wm(o,t,s,u,c,d,h,l,n,i):pd(o,t,s,u,c,d,h,l,n),m&&(o.textAlign="start"),f&&(o.direction="inherit")}}function cr(e,t,n,r,i,o){typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.max(0,Math.min(o.tl,i/2,r/2)),tr:Math.max(0,Math.min(o.tr,i/2,r/2)),bl:Math.max(0,Math.min(o.bl,i/2,r/2)),br:Math.max(0,Math.min(o.br,i/2,r/2))},e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}function Um(e,t,n){e.arc(t,n-1.25*3.5,1.25,0,2*Math.PI,!1),e.arc(t,n,1.25,0,2*Math.PI,!1),e.arc(t,n******3.5,1.25,0,2*Math.PI,!1)}function qm(e,t,n){const r=function(l,s){const u=s.x-l.x,c=s.y-l.y,d=Math.sqrt(u*u+c*c),h=u/d,f=c/d;return{x:u,y:s.y-l.y,len:d,nx:h,ny:f,ang:Math.atan2(f,h)}};let i;const o=t.length;let a=t[o-1];for(let l=0;l<o;l++){let s=t[l%o];const u=t[(l+1)%o],c=r(s,a),d=r(s,u),h=c.nx*d.ny-c.ny*d.nx,f=c.nx*d.nx-c.ny*-d.ny;let m=Math.asin(h<-1?-1:h>1?1:h),p=1,v=!1;f<0?m<0?m=Math.PI+m:(m=Math.PI-m,p=-1,v=!0):m>0&&(p=-1,v=!0),i=s.radius!==void 0?s.radius:n;const w=m/2;let b=Math.abs(Math.cos(w)*i/Math.sin(w)),M;b>Math.min(c.len/2,d.len/2)?(b=Math.min(c.len/2,d.len/2),M=Math.abs(b*Math.sin(w)/Math.cos(w))):M=i;let O=s.x+d.nx*b,S=s.y+d.ny*b;O+=-d.ny*M*p,S+=d.nx*M*p,e.arc(O,S,M,c.ang+Math.PI/2*p,d.ang-Math.PI/2*p,v),a=s,s=u}e.closePath()}function Ms(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m){const p={x:0,y:o+u,width:0,height:0};if(e>=f.length||t>=c||t<-2||e<0)return p;const v=o-i;if(e>=d){const w=a>e?-1:1,b=yi(f);p.x+=b+s;for(let M=a;M!==e;M+=w)p.x+=f[w===1?M:M-1].width*w}else for(let w=0;w<e;w++)p.x+=f[w].width;if(p.width=f[e].width+1,t===-1)p.y=i,p.height=v;else if(t===-2){p.y=0,p.height=i;let w=e;const b=f[e].group,M=f[e].sticky;for(;w>0&&oo(f[w-1].group,b)&&f[w-1].sticky===M;){const S=f[w-1];p.x-=S.width,p.width+=S.width,w--}let O=e;for(;O+1<f.length&&oo(f[O+1].group,b)&&f[O+1].sticky===M;){const S=f[O+1];p.width+=S.width,O++}if(!M){const S=yi(f),R=p.x-S;R<0&&(p.x-=R,p.width+=R),p.x+p.width>n&&(p.width=n-p.x)}}else if(t>=c-h){let w=c-t;for(p.y=r;w>0;){const b=t+w-1;p.height=typeof m=="number"?m:m(b),p.y-=p.height,w--}p.height+=1}else{const w=l>t?-1:1;if(typeof m=="number"){const b=t-l;p.y+=b*m}else for(let b=l;b!==t;b+=w)p.y+=m(b)*w;p.height=(typeof m=="number"?m:m(t))+1}return p}const qs=1<<21;function tr(e,t){return(t+2)*qs+e}function bd(e){return e%qs}function Ys(e){return Math.floor(e/qs)-2}function Xs(e){const t=bd(e),n=Ys(e);return[t,n]}class wd{constructor(){ft(this,"visibleWindow",{x:0,y:0,width:0,height:0});ft(this,"freezeCols",0);ft(this,"freezeRows",[]);ft(this,"isInWindow",t=>{const n=bd(t),r=Ys(t),i=this.visibleWindow,o=n>=i.x&&n<=i.x+i.width||n<this.freezeCols,a=r>=i.y&&r<=i.y+i.height||this.freezeRows.includes(r);return o&&a})}setWindow(t,n,r){this.visibleWindow.x===t.x&&this.visibleWindow.y===t.y&&this.visibleWindow.width===t.width&&this.visibleWindow.height===t.height&&this.freezeCols===n&&wi(this.freezeRows,r)||(this.visibleWindow=t,this.freezeCols=n,this.freezeRows=r,this.clearOutOfWindow())}}class Ym extends wd{constructor(){super(...arguments);ft(this,"cache",new Map);ft(this,"setValue",(n,r)=>{this.cache.set(tr(n[0],n[1]),r)});ft(this,"getValue",n=>this.cache.get(tr(n[0],n[1])));ft(this,"clearOutOfWindow",()=>{for(const[n]of this.cache.entries())this.isInWindow(n)||this.cache.delete(n)})}}class no{constructor(t=[]){ft(this,"cells");this.cells=new Set(t.map(n=>tr(n[0],n[1])))}add(t){this.cells.add(tr(t[0],t[1]))}has(t){return t===void 0?!1:this.cells.has(tr(t[0],t[1]))}remove(t){this.cells.delete(tr(t[0],t[1]))}clear(){this.cells.clear()}get size(){return this.cells.size}hasHeader(){for(const t of this.cells)if(Ys(t)<0)return!0;return!1}hasItemInRectangle(t){for(let n=t.y;n<t.y+t.height;n++)for(let r=t.x;r<t.x+t.width;r++)if(this.cells.has(tr(r,n)))return!0;return!1}hasItemInRegion(t){for(const n of t)if(this.hasItemInRectangle(n))return!0;return!1}*values(){for(const t of this.cells)yield Xs(t)}}function Xm(e){return{"--gdg-accent-color":e.accentColor,"--gdg-accent-fg":e.accentFg,"--gdg-accent-light":e.accentLight,"--gdg-text-dark":e.textDark,"--gdg-text-medium":e.textMedium,"--gdg-text-light":e.textLight,"--gdg-text-bubble":e.textBubble,"--gdg-bg-icon-header":e.bgIconHeader,"--gdg-fg-icon-header":e.fgIconHeader,"--gdg-text-header":e.textHeader,"--gdg-text-group-header":e.textGroupHeader??e.textHeader,"--gdg-text-header-selected":e.textHeaderSelected,"--gdg-bg-cell":e.bgCell,"--gdg-bg-cell-medium":e.bgCellMedium,"--gdg-bg-header":e.bgHeader,"--gdg-bg-header-has-focus":e.bgHeaderHasFocus,"--gdg-bg-header-hovered":e.bgHeaderHovered,"--gdg-bg-bubble":e.bgBubble,"--gdg-bg-bubble-selected":e.bgBubbleSelected,"--gdg-bg-search-result":e.bgSearchResult,"--gdg-border-color":e.borderColor,"--gdg-horizontal-border-color":e.horizontalBorderColor??e.borderColor,"--gdg-drilldown-border":e.drilldownBorder,"--gdg-link-color":e.linkColor,"--gdg-cell-horizontal-padding":`${e.cellHorizontalPadding}px`,"--gdg-cell-vertical-padding":`${e.cellVerticalPadding}px`,"--gdg-header-font-style":e.headerFontStyle,"--gdg-base-font-style":e.baseFontStyle,"--gdg-marker-font-style":e.markerFontStyle,"--gdg-font-family":e.fontFamily,"--gdg-editor-font-size":e.editorFontSize,...e.resizeIndicatorColor===void 0?{}:{"--gdg-resize-indicator-color":e.resizeIndicatorColor},...e.headerBottomBorderColor===void 0?{}:{"--gdg-header-bottom-border-color":e.headerBottomBorderColor},...e.roundingRadius===void 0?{}:{"--gdg-rounding-radius":`${e.roundingRadius}px`}}}const yd={accentColor:"#4F5DFF",accentFg:"#FFFFFF",accentLight:"rgba(62, 116, 253, 0.1)",textDark:"#313139",textMedium:"#737383",textLight:"#B2B2C0",textBubble:"#313139",bgIconHeader:"#737383",fgIconHeader:"#FFFFFF",textHeader:"#313139",textGroupHeader:"#313139BB",textHeaderSelected:"#FFFFFF",bgCell:"#FFFFFF",bgCellMedium:"#FAFAFB",bgHeader:"#F7F7F8",bgHeaderHasFocus:"#E9E9EB",bgHeaderHovered:"#EFEFF1",bgBubble:"#EDEDF3",bgBubbleSelected:"#FFFFFF",bgSearchResult:"#fff9e3",borderColor:"rgba(115, 116, 131, 0.16)",drilldownBorder:"rgba(0, 0, 0, 0)",linkColor:"#353fb5",cellHorizontalPadding:8,cellVerticalPadding:3,headerIconSize:18,headerFontStyle:"600 13px",baseFontStyle:"13px",markerFontStyle:"9px",fontFamily:"Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif",editorFontSize:"13px",lineHeight:1.4};function Cd(){return yd}const Sd=ie.createContext(yd);function Gm(){return ie.useContext(Sd)}function vr(e,...t){const n={...e};for(const r of t)if(r!==void 0)for(const i in r)r.hasOwnProperty(i)&&(i==="bgCell"?n[i]=An(r[i],n[i]):n[i]=r[i]);return(n.headerFontFull===void 0||e.fontFamily!==n.fontFamily||e.headerFontStyle!==n.headerFontStyle)&&(n.headerFontFull=`${n.headerFontStyle} ${n.fontFamily}`),(n.baseFontFull===void 0||e.fontFamily!==n.fontFamily||e.baseFontStyle!==n.baseFontStyle)&&(n.baseFontFull=`${n.baseFontStyle} ${n.fontFamily}`),(n.markerFontFull===void 0||e.fontFamily!==n.fontFamily||e.markerFontStyle!==n.markerFontStyle)&&(n.markerFontFull=`${n.markerFontStyle} ${n.fontFamily}`),n}const Rs=150;function jm(e,t,n,r){var o;const i=r(t);return((o=i==null?void 0:i.measure)==null?void 0:o.call(i,e,t,n))??Rs}function xd(e,t,n,r,i,o,a,l,s){let u=0;const c=i===void 0?[]:i.map(h=>{const f=jm(e,h[r],t,s);return u=Math.max(u,f),f});if(c.length>5&&l){u=0;let h=0;for(const m of c)h+=m;const f=h/c.length;for(let m=0;m<c.length;m++)c[m]>=f*2?c[m]=0:u=Math.max(u,c[m])}u=Math.max(u,e.measureText(n.title).width+t.cellHorizontalPadding*2+(n.icon===void 0?0:28));const d=Math.max(Math.ceil(o),Math.min(Math.floor(a),Math.ceil(u)));return{...n,width:d}}function Km(e,t,n,r,i,o,a,l,s){const u=g.useRef(t),c=g.useRef(n),d=g.useRef(a);u.current=t,c.current=n,d.current=a;const[h,f]=g.useMemo(()=>{if(typeof window>"u")return[null,null];const b=document.createElement("canvas");return b.style.display="none",b.style.opacity="0",b.style.position="fixed",[b,b.getContext("2d",{alpha:!1})]},[]);g.useLayoutEffect(()=>(h&&document.documentElement.append(h),()=>{h==null||h.remove()}),[h]);const m=g.useRef({}),p=g.useRef(),[v,w]=g.useState();return g.useLayoutEffect(()=>{const b=c.current;if(b===void 0||e.every(Fo))return;let M=Math.max(1,10-Math.floor(e.length/1e4)),O=0;M<u.current&&M>1&&(M--,O=1);const S={x:0,y:0,width:e.length,height:Math.min(u.current,M)},R={x:0,y:u.current-1,width:e.length,height:1};(async()=>{const E=b(S,s.signal),x=O>0?b(R,s.signal):void 0;let L;typeof E=="object"?L=E:L=await Fl(E),x!==void 0&&(typeof x=="object"?L=[...L,...x]:L=[...L,...await Fl(x)]),p.current=e,w(L)})()},[s.signal,e]),g.useMemo(()=>{let M=e.every(Fo)?e:f===null?e.map(_=>Fo(_)?_:{..._,width:Rs}):(f.font=d.current.baseFontFull,e.map((_,E)=>{if(Fo(_))return _;if(m.current[_.id]!==void 0)return{..._,width:m.current[_.id]};if(v===void 0||p.current!==e||_.id===void 0)return{..._,width:Rs};const x=xd(f,a,_,E,v,i,o,!0,l);return m.current[_.id]=x.width,x})),O=0,S=0;const R=[];for(const[_,E]of M.entries())O+=E.width,E.grow!==void 0&&E.grow>0&&(S+=E.grow,R.push(_));if(O<r&&R.length>0){const _=[...M],E=r-O;let x=E;for(let L=0;L<R.length;L++){const D=R[L],C=(M[D].grow??0)/S,I=L===R.length-1?x:Math.min(x,Math.floor(E*C));_[D]={...M[D],growOffset:I,width:M[D].width+I},x-=I}M=_}return{sizedColumns:M,nonGrowWidth:O}},[r,e,f,v,a,i,o,l])}var es,hu;function Zm(){if(hu)return es;hu=1;function e(t,n,r){return t===t&&(r!==void 0&&(t=t<=r?t:r),n!==void 0&&(t=t>=n?t:n)),t}return es=e,es}var ts,gu;function Jm(){if(gu)return ts;gu=1;var e=Zm(),t=Tc();function n(r,i,o){return o===void 0&&(o=i,i=void 0),o!==void 0&&(o=t(o),o=o===o?o:0),i!==void 0&&(i=t(i),i=i===i?i:0),e(t(r),i,o)}return ts=n,ts}var Qm=Jm();const Un=wr(Qm);var ns,mu;function ep(){if(mu)return ns;mu=1;function e(){}return ns=e,ns}var rs,pu;function tp(){if(pu)return rs;pu=1;var e=ph(),t=ep(),n=Dc(),r=1/0,i=e&&1/n(new e([,-0]))[1]==r?function(o){return new e(o)}:t;return rs=i,rs}var is,vu;function np(){if(vu)return is;vu=1;var e=vh(),t=bh(),n=yh(),r=wh(),i=tp(),o=Dc(),a=200;function l(s,u,c){var d=-1,h=t,f=s.length,m=!0,p=[],v=p;if(c)m=!1,h=n;else if(f>=a){var w=u?null:i(s);if(w)return o(w);m=!1,h=r,v=new e}else v=u?[]:p;e:for(;++d<f;){var b=s[d],M=u?u(b):b;if(b=c||b!==0?b:0,m&&M===M){for(var O=v.length;O--;)if(v[O]===M)continue e;u&&v.push(M),p.push(b)}else h(v,M,c)||(v!==p&&v.push(M),p.push(b))}return p}return is=l,is}var os,bu;function rp(){if(bu)return os;bu=1;var e=np();function t(n){return n&&n.length?e(n):[]}return os=t,os}var ip=rp();const op=wr(ip);var ap=Ch();const wu=wr(ap),qt='<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">',sp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}<rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/><path d="M15.75 4h-1.5a.25.25 0 0 0-.177.074L9.308 8.838a3.75 3.75 0 1 0 1.854 1.854l1.155-1.157.967.322a.5.5 0 0 0 .65-.55l-.18-1.208.363-.363.727.331a.5.5 0 0 0 .69-.59l-.254-.904.647-.647A.25.25 0 0 0 16 5.75v-1.5a.25.25 0 0 0-.25-.25zM7.5 13.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0z" fill="${t}"/></svg>`},lp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}<rect x="2" y="2" width="16" height="16" rx="4" fill="${n}"/><path d="m12.223 13.314 3.052-2.826a.65.65 0 0 0 0-.984l-3.052-2.822c-.27-.25-.634-.242-.865.022-.232.263-.206.636.056.882l2.601 2.41-2.601 2.41c-.262.245-.288.619-.056.882.231.263.595.277.865.026Zm-4.444.005c.266.25.634.241.866-.027.231-.263.206-.636-.06-.882L5.983 10l2.602-2.405c.266-.25.291-.62.06-.887-.232-.263-.596-.272-.866-.022L4.723 9.51a.653.653 0 0 0 0 .983l3.056 2.827Z" fill="${t}"/></svg>`},up=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M6.52 12.78H5.51V8.74l-1.33.47v-.87l2.29-.83h.05v5.27zm5.2 0H8.15v-.69l1.7-1.83a6.38 6.38 0 0 0 .34-.4c.09-.11.16-.22.22-.32s.1-.19.12-.27a.9.9 0 0 0 0-.56.63.63 0 0 0-.15-.23.58.58 0 0 0-.22-.15.75.75 0 0 0-.29-.05c-.27 0-.48.08-.62.23a.95.95 0 0 0-.2.65H8.03c0-.24.04-.46.13-.67a1.67 1.67 0 0 1 .97-.91c.23-.1.49-.14.77-.14.26 0 .5.04.7.11.21.08.38.18.52.32.14.13.25.3.32.48a1.74 1.74 0 0 1 .03 1.13 2.05 2.05 0 0 1-.24.47 4.16 4.16 0 0 1-.35.47l-.47.5-1 1.05h2.32v.8zm1.8-3.08h.55c.28 0 .48-.06.61-.2a.76.76 0 0 0 .2-.55.8.8 0 0 0-.05-.28.56.56 0 0 0-.13-.22.6.6 0 0 0-.23-.15.93.93 0 0 0-.32-.05.92.92 0 0 0-.29.05.72.72 0 0 0-.23.12.57.57 0 0 0-.21.46H12.4a1.3 1.3 0 0 1 .5-1.04c.15-.13.33-.23.54-.3a2.48 2.48 0 0 1 1.4 0c.2.06.4.15.55.28.15.13.27.28.36.47.08.19.13.4.13.65a1.15 1.15 0 0 1-.2.65 1.36 1.36 0 0 1-.58.49c.15.05.28.12.38.2a1.14 1.14 0 0 1 .43.62c.03.13.05.26.05.4 0 .25-.05.47-.14.66a1.42 1.42 0 0 1-.4.49c-.16.13-.35.23-.58.3a2.51 2.51 0 0 1-.73.1c-.22 0-.44-.03-.65-.09a1.8 1.8 0 0 1-.57-.28 1.43 1.43 0 0 1-.4-.47 1.41 1.41 0 0 1-.15-.66h1a.66.66 0 0 0 .22.5.87.87 0 0 0 .58.2c.25 0 .45-.07.6-.2a.71.71 0 0 0 .21-.56.97.97 0 0 0-.06-.36.61.61 0 0 0-.18-.25.74.74 0 0 0-.28-.15 1.33 1.33 0 0 0-.37-.04h-.55V9.7z" fill="${t}"/>
  </svg>`},cp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M8.182 12.4h3.636l.655 1.6H14l-3.454-8H9.455L6 14h1.527l.655-1.6zM10 7.44l1.36 3.651H8.64L10 7.441z" fill="${t}"/>
</svg>`},dp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
    <path
        d="M16.2222 2H3.77778C2.8 2 2 2.8 2 3.77778V16.2222C2 17.2 2.8 18 3.77778 18H16.2222C17.2 18 17.9911 17.2 17.9911 16.2222L18 3.77778C18 2.8 17.2 2 16.2222 2Z"
        fill="${n}"
    />
    <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.66667 6.66669C5.73368 6.66669 4.16667 8.15907 4.16667 10C4.16667 11.841 5.73368 13.3334 7.66667 13.3334H12.3333C14.2663 13.3334 15.8333 11.841 15.8333 10C15.8333 8.15907 14.2663 6.66669 12.3333 6.66669H7.66667ZM12.5 12.5C13.8807 12.5 15 11.3807 15 10C15 8.61931 13.8807 7.50002 12.5 7.50002C11.1193 7.50002 10 8.61931 10 10C10 11.3807 11.1193 12.5 12.5 12.5Z"
        fill="${t}"
    />
</svg>`},kd=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
<path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.29 4.947a3.368 3.368 0 014.723.04 3.375 3.375 0 01.041 4.729l-.009.009-1.596 1.597a3.367 3.367 0 01-5.081-.364.71.71 0 011.136-.85 1.95 1.95 0 002.942.21l1.591-1.593a1.954 1.954 0 00-.027-2.733 1.95 1.95 0 00-2.732-.027l-.91.907a.709.709 0 11-1.001-1.007l.915-.911.007-.007z" fill="${t}"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.55 8.678a3.368 3.368 0 015.082.364.71.71 0 01-1.136.85 1.95 1.95 0 00-2.942-.21l-1.591 1.593a1.954 1.954 0 00.027 2.733 1.95 1.95 0 002.73.028l.906-.906a.709.709 0 111.003 1.004l-.91.91-.008.01a3.368 3.368 0 01-4.724-.042 3.375 3.375 0 01-.041-4.728l.009-.009L6.55 8.678z" fill="${t}"/>
</svg>
  `},fp=e=>{const t=e.bgColor;return`${qt}
    <path stroke="${t}" stroke-width="2" d="M12 3v14"/>
    <path stroke="${t}" stroke-width="2" stroke-linecap="round" d="M10 4h4m-4 12h4"/>
    <path d="M11 14h4a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4v2ZM9.5 8H5a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h4.5v2H5a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h4.5v2Z" fill="${t}"/>
  </svg>
`},hp=kd,gp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7 13.138a.5.5 0 00.748.434l5.492-3.138a.5.5 0 000-.868L7.748 6.427A.5.5 0 007 6.862v6.276z" fill="${t}"/>
</svg>`},mp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M10 5a5 5 0 1 0 0 10 5 5 0 0 0 0-10zm0 9.17A4.17 4.17 0 0 1 5.83 10 4.17 4.17 0 0 1 10 5.83 4.17 4.17 0 0 1 14.17 10 4.17 4.17 0 0 1 10 14.17z" fill="${t}"/>
    <path d="M8.33 8.21a.83.83 0 1 0-.03 ********** 0 0 0 .03-1.67zm3.34 0a.83.83 0 1 0-.04 ********** 0 0 0 .04-1.67z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M14.53 13.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 1 0 0 12 6 6 0 0 0 0-12zm0 11a5 5 0 1 1 .01-10.01A5 5 0 0 1 10 15z" fill="${t}"/>
    <path d="M8 7.86a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2zm4 0a1 1 0 1 0-.04 2 1 1 0 0 0 .04-2z" fill="${t}"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.53 11.9a2.82 2.82 0 0 1-5.06 0l.77-.38a1.97 1.97 0 0 0 3.52 0l.77.39z" fill="${t}"/>
  </svg>`},pp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path opacity=".5" fill-rule="evenodd" clip-rule="evenodd" d="M12.499 10.801a.5.5 0 01.835 0l2.698 4.098a.5.5 0 01-.418.775H10.22a.5.5 0 01-.417-.775l2.697-4.098z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.07 8.934a.5.5 0 01.824 0l4.08 5.958a.5.5 0 01-.412.782h-8.16a.5.5 0 01-.413-.782l4.08-5.958zM13.75 8.333a2.083 2.083 0 100-4.166 2.083 2.083 0 000 4.166z" fill="${t}"/>
</svg>`},vp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path fill="${t}" d="M3 3h14v14H3z"/>
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2zm-7.24 9.78h1.23c.15 0 .27.06.36.18l.98 1.28a.43.43 0 0 1-.05.58l-1.2 1.21a.45.45 0 0 1-.6.04A6.72 6.72 0 0 1 7.33 10c0-.61.1-1.2.25-1.78a6.68 6.68 0 0 1 2.12-********* 0 0 1 .6.04l1.2 1.2c.***********.05.59l-.98 1.29a.43.43 0 0 1-.36.17H8.98A5.38 5.38 0 0 0 8.67 10c0 .62.11 1.23.3 1.79z" fill="${n}"/>
  </svg>`},bp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="m13.49 13.15-2.32-3.27h1.4V7h1.86v2.88h1.4l-2.34 3.27zM11 13H9v-3l-1.5 1.92L6 10v3H4V7h2l1.5 2L9 7h2v6z" fill="${t}"/>
  </svg>`},wp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M14.8 4.182h-.6V3H13v1.182H7V3H5.8v1.182h-.6c-.66 0-1.2.532-1.2 1.182v9.454C4 15.468 4.54 16 5.2 16h9.6c.66 0 1.2-.532 1.2-1.182V5.364c0-.65-.54-1.182-1.2-1.182zm0 10.636H5.2V7.136h9.6v7.682z" fill="${t}"/>
</svg>`},yp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 4a6 6 0 0 0-6 6 6 6 0 0 0 6 6 6 6 0 0 0 6-6 6 6 0 0 0-6-6zm0 10.8A4.8 4.8 0 0 1 5.2 10a4.8 4.8 0 1 1 4.8 4.8z" fill="${t}"/>
    <path d="M10 7H9v3.93L12.5 13l.5-.8-3-1.76V7z" fill="${t}"/>
  </svg>`},Cp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 8.643a1.357 1.357 0 100 2.714 1.357 1.357 0 000-2.714zM7.357 10a2.643 2.643 0 115.286 0 2.643 2.643 0 01-5.286 0z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.589 4.898A5.643 5.643 0 0115.643 10v.5a2.143 2.143 0 01-4.286 0V8a.643.643 0 011.286 0v2.5a.857.857 0 001.714 0V10a4.357 4.357 0 10-1.708 3.46.643.643 0 01.782 1.02 5.643 5.643 0 11-5.842-9.582z" fill="${t}"/>
</svg>`},Sp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="8" width="10" height="8" rx="2" fill="${n}"/>
    <rect x="8" y="4" width="10" height="8" rx="2" fill="${n}"/>
    <path d="M10.68 7.73V6l2.97 3.02-2.97 3.02v-1.77c-2.13 0-3.62.7-4.68 2.2.43-2.15 1.7-4.31 4.68-4.74z" fill="${t}"/>
  </svg>`},xp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path fill="${t}" d="M4 3h12v14H4z"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M3.6 2A1.6 1.6 0 002 3.6v12.8A1.6 1.6 0 003.6 18h12.8a1.6 1.6 0 001.6-1.6V3.6A1.6 1.6 0 0016.4 2H3.6zm11.3 10.8a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7h-1.4a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.6-.693.117.117 0 00.1-.115V10.35a.117.117 0 00-.117-.116h-2.8a.117.117 0 00-.117.116v2.333c0 .***************.117h.117a.7.7 0 01.7.7v1.4a.7.7 0 01-.7.7H9.3a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.117a.117.117 0 00.117-.117V10.35a.117.117 0 00-.117-.117h-2.8a.117.117 0 00-.117.117v2.342c0 .*************.115a.7.7 0 01.6.693v1.4a.7.7 0 01-.7.7H5.1a.7.7 0 01-.7-.7v-1.4a.7.7 0 01.7-.7h.35a.116.116 0 00.116-.117v-2.45c0-.515.418-.933.934-.933h2.917a.117.117 0 00.117-.117V6.85a.117.117 0 00-.117-.116h-2.45a.7.7 0 01-.7-.7V5.1a.7.7 0 01.7-.7h6.067a.7.7 0 01.7.7v.934a.7.7 0 01-.7.7h-2.45a.117.117 0 00-.118.116v2.333c0 .***************.117H13.5c.516 0 .934.418.934.934v2.45c0 .***************.116h.35z" fill="${n}"/>
</svg>`},kp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M9.98 13.33c.45 0 .74-.3.73-.75l-.01-.1-.16-1.67 1.45 1.05a.81.81 0 0 0 .5.18c.37 0 .72-.32.72-.76 0-.3-.17-.54-.49-.68l-1.63-.77 1.63-.77c.32-.14.49-.37.49-.67 0-.45-.34-.76-.71-.76a.81.81 0 0 0-.5.18l-1.47 1.03.16-1.74.01-.08c.01-.46-.27-.76-.72-.76-.46 0-.76.32-.75.76l.01.08.16 1.74-1.47-1.03a.77.77 0 0 0-.5-.18.74.74 0 0 0-.72.76c0 .3.17.53.49.67l1.63.77-1.62.77c-.32.14-.5.37-.5.68 0 .44.35.75.72.75a.78.78 0 0 0 .5-.17L9.4 10.8l-.16 1.68v.09c-.02.44.28.75.74.75z" fill="${t}"/>
  </svg>`},Mp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M8 5.83H5.83a.83.83 0 0 0 0 1.67h1.69A4.55 4.55 0 0 1 8 5.83zm-.33 3.34H5.83a.83.83 0 0 0 0 1.66h2.72a4.57 4.57 0 0 1-.88-1.66zM5.83 12.5a.83.83 0 0 0 0 1.67h7.5a.83.83 0 1 0 0-1.67h-7.5zm8.8-2.9a3.02 3.02 0 0 0 .46-1.6c0-1.66-1.32-3-2.94-3C10.52 5 9.2 6.34 9.2 8s1.31 3 2.93 3c.58 0 1.11-.17 1.56-.47l2.04 2.08.93-.94-2.04-2.08zm-2.48.07c-.9 0-1.63-.75-1.63-1.67s.73-1.67 1.63-1.67c.9 0 1.63.75 1.63 1.67s-.73 1.67-1.63 1.67z" fill="${t}"/>
  </svg>`},Rp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M7.676 4.726V3l2.976 3.021-2.976 3.022v-1.77c-2.125 0-3.613.69-4.676 2.201.425-2.158 1.7-4.316 4.676-4.748zM10.182 14.4h3.636l.655 1.6H16l-3.454-8h-1.091L8 16h1.527l.655-1.6zM12 9.44l1.36 3.65h-2.72L12 9.44z" fill="${t}"/>
</svg>`},Ep=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.167 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666H4.167z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M7.083 4.167a.833.833 0 10-1.666 0v4.166a.833.833 0 101.666 0V4.167zM11.667 5.417a.833.833 0 100 1.666h4.166a.833.833 0 100-1.666h-4.166zM5.367 11.688a.833.833 0 00-1.179 1.179l2.947 2.946a.833.833 0 001.178-1.178l-2.946-2.947z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.313 12.867a.833.833 0 10-1.178-1.179l-2.947 2.947a.833.833 0 101.179 1.178l2.946-2.946z" fill="${t}"/>
  <path d="M10.833 12.5c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833zM10.833 15c0-.46.373-.833.834-.833h4.166a.833.833 0 110 1.666h-4.166a.833.833 0 01-.834-.833z" fill="${t}"/>
</svg>`},Ip=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <path d="M16.22 2H3.78C2.8 2 2 2.8 2 3.78v12.44C2 17.2 2.8 18 3.78 18h12.44c.98 0 1.77-.8 1.77-1.78L18 3.78C18 2.8 17.2 2 16.22 2z" fill="${n}"/>
    <path d="M10 8.84a1.16 1.16 0 1 0 0 2.32 1.16 1.16 0 0 0 0-2.32zm3.02 3.61a3.92 3.92 0 0 0 .78-********** 0 1 0-.95.2c.19.87-.02 1.78-.58 2.47a2.92 2.92 0 1 1-4.13-4.08 2.94 2.94 0 0 1 2.43-.62.49.49 0 1 0 .17-.96 3.89 3.89 0 1 0 2.28 6.27zM10 4.17a5.84 5.84 0 0 0-5.44 ********** 0 1 0 .9-.35 4.86 4.86 0 1 1 2.5 ********** 0 1 0-.4.88c.76.35 1.6.54 2.44.53a5.83 5.83 0 0 0 0-11.66zm3.02 3.5a.7.7 0 1 0-1.4 0 .7.7 0 0 0 1.4 0zm-6.97 5.35a.7.7 0 1 1 0 ******* 0 0 1 0-1.4z" fill="${t}"/>
  </svg>`},Tp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path d="M12.4 13.565c1.865-.545 3.645-2.083 3.645-4.396 0-1.514-.787-2.604-2.071-2.604C12.69 6.565 12 7.63 12 8.939c1.114.072 1.865.726 1.865 1.683 0 .933-.8 1.647-1.84 2.023l.375.92zM4 5h6v2H4zM4 9h5v2H4zM4 13h4v2H4z" fill="${t}"/>
</svg>`},Dp=e=>{const t=e.fgColor,n=e.bgColor;return`
    ${qt}
    <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
    <path d="M12.4 13.56c1.86-.54 3.65-2.08 3.65-4.4 0-1.5-.8-2.6-2.08-2.6S12 7.64 12 8.95c1.11.07 1.86.73 1.86 1.68 0 .94-.8 1.65-1.83 2.03l.37.91zM4 5h6v2H4zm0 4h5v2H4zm0 4h4v2H4z" fill="${t}"/>
  </svg>`},Op=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <path d="M16.222 2H3.778C2.8 2 2 2.8 2 3.778v12.444C2 17.2 2.8 18 3.778 18h12.444c.978 0 1.77-.8 1.77-1.778L18 3.778C18 2.8 17.2 2 16.222 2z" fill="${n}"/>
  <path d="M10 7a1 1 0 100-2v2zm0 6a1 1 0 100 2v-2zm0-8H7v2h3V5zm-3 6h5V9H7v2zm5 2h-2v2h2v-2zm1-1a1 1 0 01-1 1v2a3 3 0 003-3h-2zm-1-1a1 1 0 011 1h2a3 3 0 00-3-3v2zM4 8a3 3 0 003 3V9a1 1 0 01-1-1H4zm3-3a3 3 0 00-3 3h2a1 1 0 011-1V5z" fill="${t}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M4.856 12.014a.5.5 0 00-.712.702L5.409 14l-1.265 1.284a.5.5 0 00.712.702l1.255-1.274 1.255 1.274a.5.5 0 00.712-.702L6.813 14l1.265-1.284a.5.5 0 00-.712-.702L6.11 13.288l-1.255-1.274zM12.856 4.014a.5.5 0 00-.712.702L13.409 6l-1.265 1.284a.5.5 0 10.712.702l1.255-1.274 1.255 1.274a.5.5 0 10.712-.702L14.813 6l1.265-1.284a.5.5 0 00-.712-.702L14.11 5.288l-1.255-1.274z" fill="${t}"/>
</svg>`},Pp=e=>{const t=e.fgColor,n=e.bgColor;return`${qt}
  <rect x="2" y="2" width="16" height="16" rx="2" fill="${n}"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 7.25a.75.75 0 000-1.5h-6.5a.75.75 0 100 1.5h6.5zM15 10a.75.75 0 01-.75.75h-6.5a.75.75 0 010-1.5h6.5A.75.75 0 0115 10zm-.75 4.25a.75.75 0 000-1.5h-6.5a.75.75 0 000 1.5h6.5zm-8.987-7a.75.75 0 100-********* 0 000 1.5zm.75 2.75a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 4.25a.75.75 0 100-********* 0 000 1.5z" fill="${t}"/>
</svg>`},_p=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 15v1h14v-2.5c0-.87-.44-1.55-.98-2.04a6.19 6.19 0 0 0-1.9-1.14 12.1 12.1 0 0 0-2.48-.67A4 4 0 1 0 5 6a4 4 0 0 0 2.36 3.65c-.82.13-1.7.36-2.48.67-.69.28-1.37.65-1.9 1.13A2.8 2.8 0 0 0 2 13.5V15z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>`},Lp=e=>{const t=e.fgColor;return`
    <svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.43 6.04v-.18a3.86 3.86 0 0 0-7.72 0v.18A2.15 2.15 0 0 0 3 8.14v5.72C3 15.04 3.96 16 5.14 16H12c1.18 0 2.14-.96 2.14-2.14V8.14c0-1.03-.73-1.9-1.71-2.1zM7.86 6v-.14a.71.71 0 1 1 1.43 0V6H7.86z" fill="${e.bgColor}" stroke="${t}" stroke-width="2"/>
  </svg>
`},Fp={headerRowID:sp,headerNumber:up,headerCode:lp,headerString:cp,headerBoolean:dp,headerAudioUri:hp,headerVideoUri:gp,headerEmoji:mp,headerImage:pp,headerUri:kd,headerPhone:vp,headerMarkdown:bp,headerDate:wp,headerTime:yp,headerEmail:Cp,headerReference:Sp,headerIfThenElse:xp,headerSingleValue:kp,headerLookup:Mp,headerTextTemplate:Rp,headerMath:Ep,headerRollup:Ip,headerJoinStrings:Tp,headerSplitString:Dp,headerGeoDistance:Op,headerArray:Pp,rowOwnerOverlay:_p,protectedColumnOverlay:Lp,renameIcon:fp};function Ap(e,t){return e==="normal"?[t.bgIconHeader,t.fgIconHeader]:e==="selected"?["white",t.accentColor]:[t.accentColor,t.bgHeader]}class Hp{constructor(t,n){ft(this,"onSettled");ft(this,"spriteMap",new Map);ft(this,"headerIcons");ft(this,"inFlight",0);this.onSettled=n,this.headerIcons=t??{}}drawSprite(t,n,r,i,o,a,l,s=1){const[u,c]=Ap(n,l),d=a*Math.ceil(window.devicePixelRatio),h=`${u}_${c}_${d}_${t}`;let f=this.spriteMap.get(h);if(f===void 0){const m=this.headerIcons[t];if(m===void 0)return;f=document.createElement("canvas");const p=f.getContext("2d");if(p===null)return;const v=new Image;v.src=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(m({fgColor:c,bgColor:u}))}`,this.spriteMap.set(h,f);const w=v.decode();if(w===void 0)return;this.inFlight++,w.then(()=>{p.drawImage(v,0,0,d,d)}).finally(()=>{this.inFlight--,this.inFlight===0&&this.onSettled()})}else s<1&&(r.globalAlpha=s),r.drawImage(f,0,0,d,d,i,o,a,a),s<1&&(r.globalAlpha=1)}}function Md(e){if(e.length===0)return;let t;for(const n of e)t=Math.min(t??n.y,n.y)}function xa(e,t,n,r,i,o,a,l,s){l=l??t;let u=t,c=e;const d=r-o;let h=!1;for(;u<n&&c<d;){const f=i(c);if(u+f>l&&s(u,c,f,!1,a&&c===r-1)===!0){h=!0;break}u+=f,c++}if(!h){u=n;for(let f=0;f<o;f++){c=r-1-f;const m=i(c);u-=m,s(u,c,m,!0,a&&c===r-1)}}}function Fr(e,t,n,r,i,o){let a=0,l=0;const s=i+r;for(const u of e){const c=u.sticky?l:a+n;if(o(u,c,s,u.sticky?0:l,t)===!0)break;a+=u.width,l+=u.sticky?u.width:0}}function Rd(e,t,n,r,i){let o=0,a=0;for(let l=0;l<e.length;l++){const s=e[l];let u=l+1,c=s.width;for(s.sticky&&(a+=c);u<e.length&&oo(e[u].group,s.group)&&e[u].sticky===e[l].sticky;){const p=e[u];c+=p.width,u++,l++,p.sticky&&(a+=p.width)}const d=s.sticky?0:n,h=o+d,f=s.sticky?0:Math.max(0,a-h),m=Math.min(c-f,t-(h+f));i([s.sourceIndex,e[u-1].sourceIndex],s.group??"",h+f,0,m,r),o+=c}}function Ed(e,t,n,r,i,o,a){var h;const[l,s]=e;let u,c;const d=((h=a.find(f=>!f.sticky))==null?void 0:h.sourceIndex)??0;if(s>d){const f=Math.max(l,d);let m=t,p=r;for(let v=o.sourceIndex-1;v>=f;v--)m-=a[v].width,p+=a[v].width;for(let v=o.sourceIndex+1;v<=s;v++)p+=a[v].width;c={x:m,y:n,width:p,height:i}}if(d>l){const f=Math.min(s,d-1);let m=t,p=r;for(let v=o.sourceIndex-1;v>=l;v--)m-=a[v].width,p+=a[v].width;for(let v=o.sourceIndex+1;v<=f;v++)p+=a[v].width;u={x:m,y:n,width:p,height:i}}return[u,c]}function zp(e,t,n,r){if(r==="any")return Id(e,{x:t,y:n,width:1,height:1});if(r==="vertical"&&(t=e.x),r==="horizontal"&&(n=e.y),dd([t,n],e))return;const i=t-e.x,o=e.x+e.width-t,a=n-e.y+1,l=e.y+e.height-n,s=Math.min(r==="vertical"?Number.MAX_SAFE_INTEGER:i,r==="vertical"?Number.MAX_SAFE_INTEGER:o,r==="horizontal"?Number.MAX_SAFE_INTEGER:a,r==="horizontal"?Number.MAX_SAFE_INTEGER:l);return s===l?{x:e.x,y:e.y+e.height,width:e.width,height:n-e.y-e.height+1}:s===a?{x:e.x,y:n,width:e.width,height:e.y-n}:s===o?{x:e.x+e.width,y:e.y,width:t-e.x-e.width+1,height:e.height}:{x:t,y:e.y,width:e.x-t,height:e.height}}function ao(e,t,n,r,i,o,a,l){return e<=i+a&&i<=e+n&&t<=o+l&&o<=t+r}function Xr(e,t,n){return t>=e.x&&t<=e.x+e.width&&n>=e.y&&n<=e.y+e.height}function Id(e,t){const n=Math.min(e.x,t.x),r=Math.min(e.y,t.y),i=Math.max(e.x+e.width,t.x+t.width)-n,o=Math.max(e.y+e.height,t.y+t.height)-r;return{x:n,y:r,width:i,height:o}}function Vp(e,t){return e.x<=t.x&&e.y<=t.y&&e.x+e.width>=t.x+t.width&&e.y+e.height>=t.y+t.height}function $p(e,t,n,r){if(e.x>t||e.y>n||e.x<0&&e.y<0&&e.x+e.width>t&&e.y+e.height>n)return;if(e.x>=0&&e.y>=0&&e.x+e.width<=t&&e.y+e.height<=n)return e;const i=-4,o=-4,a=t+4,l=n+4,s=i-e.x,u=e.x+e.width-a,c=o-e.y,d=e.y+e.height-l,h=s>0?e.x+Math.floor(s/r)*r:e.x,f=u>0?e.x+e.width-Math.floor(u/r)*r:e.x+e.width,m=c>0?e.y+Math.floor(c/r)*r:e.y,p=d>0?e.y+e.height-Math.floor(d/r)*r:e.y+e.height;return{x:h,y:m,width:f-h,height:p-m}}function Np(e,t,n,r,i){const[o,a,l,s]=t,[u,c,d,h]=i,{x:f,y:m,width:p,height:v}=e,w=[];if(p<=0||v<=0)return w;const b=f+p,M=m+v,O=f<o,S=m<a,R=f+p>l,_=m+v>s,E=f>=o&&f<l||b>o&&b<=l||f<o&&b>l,x=m>=a&&m<s||M>a&&M<=s||m<a&&M>s;if(E&&x){const D=Math.max(f,o),C=Math.max(m,a),I=Math.min(b,l),T=Math.min(M,s);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:c,width:d-u+1,height:h-c+1}})}if(O&&S){const D=f,C=m,I=Math.min(b,o),T=Math.min(M,a);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:0,width:u+1,height:c+1}})}if(S&&E){const D=Math.max(f,o),C=m,I=Math.min(b,l),T=Math.min(M,a);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:0,width:d-u+1,height:c+1}})}if(S&&R){const D=Math.max(f,l),C=m,I=b,T=Math.min(M,a);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:d,y:0,width:n-d+1,height:c+1}})}if(O&&x){const D=f,C=Math.max(m,a),I=Math.min(b,o),T=Math.min(M,s);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:c,width:u+1,height:h-c+1}})}if(R&&x){const D=Math.max(f,l),C=Math.max(m,a),I=b,T=Math.min(M,s);w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:d,y:c,width:n-d+1,height:h-c+1}})}if(O&&_){const D=f,C=Math.max(m,s),I=Math.min(b,o),T=M;w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:0,y:h,width:u+1,height:r-h+1}})}if(_&&E){const D=Math.max(f,o),C=Math.max(m,s),I=Math.min(b,l),T=M;w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:u,y:h,width:d-u+1,height:r-h+1}})}if(R&&_){const D=Math.max(f,l),C=Math.max(m,s),I=b,T=M;w.push({rect:{x:D,y:C,width:I-D,height:T-C},clip:{x:d,y:h,width:n-d+1,height:r-h+1}})}return w}const Bp={kind:te.Loading,allowOverlay:!1};function yu(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p,v,w,b,M,O,S,R,_,E,x,L,D,C,I,T,k,z,N,X){let re=(M==null?void 0:M.size)??Number.MAX_SAFE_INTEGER;const G=performance.now();let j=I.baseFontFull;e.font=j;const se={ctx:e},oe=[0,0],ue=v>0?Jr(s,v,u):0;let he,Q;const H=Md(b);return Fr(t,l,o,a,i,(P,W,ce,Oe,ze)=>{const Ce=Math.max(0,Oe-W),Ee=W+Ce,xe=i+1,It=P.width-Ce,yt=r-i-1;if(b.length>0){let Ve=!1;for(let _e=0;_e<b.length;_e++){const nt=b[_e];if(ao(Ee,xe,It,yt,nt.x,nt.y,nt.width,nt.height)){Ve=!0;break}}if(!Ve)return}const it=()=>{e.save(),e.beginPath(),e.rect(Ee,xe,It,yt),e.clip()},le=O.columns.hasIndex(P.sourceIndex),tt=d(P.group??"").overrideTheme,ve=P.themeOverride===void 0&&tt===void 0?I:vr(I,tt,P.themeOverride),ge=ve.baseFontFull;ge!==j&&(j=ge,e.font=ge),it();let pe;return xa(ze,ce,r,s,u,v,w,H,(Ve,_e,nt,be,de)=>{var fn,hn,pn;if(_e<0||(oe[0]=P.sourceIndex,oe[1]=_e,M!==void 0&&!M.has(oe)))return;if(b.length>0){let st=!1;for(let Xt=0;Xt<b.length;Xt++){const Pt=b[Xt];if(ao(W,Ve,P.width,nt,Pt.x,Pt.y,Pt.width,Pt.height)){st=!0;break}}if(!st)return}const ot=O.rows.hasIndex(_e),He=f.hasIndex(_e),et=_e<s?c(oe):Bp;let bt=W,gt=P.width,$e=!1,Ot=!1;if(et.span!==void 0){const[st,Xt]=et.span,Pt=`${_e},${st},${Xt},${P.sticky}`;if(Q===void 0&&(Q=new Set),Q.has(Pt)){re--;return}else{const rn=Ed(et.span,W,Ve,P.width,nt,P,n),Dt=P.sticky?rn[0]:rn[1];if(!P.sticky&&rn[0]!==void 0&&(Ot=!0),Dt!==void 0){bt=Dt.x,gt=Dt.width,Q.add(Pt),e.restore(),pe=void 0,e.save(),e.beginPath();const At=Math.max(0,Oe-Dt.x);e.rect(Dt.x+At,Ve,Dt.width-At,nt),he===void 0&&(he=[]),he.push({x:Dt.x+At,y:Ve,width:Dt.width-At,height:nt}),e.clip(),$e=!0}}}const Yt=h==null?void 0:h(_e),kt=de&&((fn=P.trailingRowOptions)==null?void 0:fn.themeOverride)!==void 0?(hn=P.trailingRowOptions)==null?void 0:hn.themeOverride:void 0,Lt=et.themeOverride===void 0&&Yt===void 0&&kt===void 0?ve:vr(ve,Yt,kt,et.themeOverride);e.beginPath();const nn=Lm(oe,et,O);let zt=Fm(oe,et,O,p);const ln=et.span!==void 0&&O.columns.some(st=>et.span!==void 0&&st>=et.span[0]&&st<=et.span[1]);nn&&!m&&p?zt=0:nn&&p&&(zt=Math.max(zt,1)),ln&&zt++,nn||(ot&&zt++,le&&!de&&zt++);const Tt=et.kind===te.Protected?Lt.bgCellMedium:Lt.bgCell;let Ct;if((be||Tt!==I.bgCell)&&(Ct=An(Tt,Ct)),zt>0||He){He&&(Ct=An(Lt.bgHeader,Ct));for(let st=0;st<zt;st++)Ct=An(Lt.accentLight,Ct)}else if(S!==void 0){for(const st of S)if(st[0]===P.sourceIndex&&st[1]===_e){Ct=An(Lt.bgSearchResult,Ct);break}}if(R!==void 0)for(let st=0;st<R.length;st++){const Xt=R[st],Pt=Xt.range;Xt.style!=="solid-outline"&&Pt.x<=P.sourceIndex&&P.sourceIndex<Pt.x+Pt.width&&Pt.y<=_e&&_e<Pt.y+Pt.height&&(Ct=An(Xt.color,Ct))}let Dn=!1;if(M!==void 0){const st=Ve+1,Pt=(be?st+nt-1:Math.min(st+nt-1,r-ue))-st;(Pt!==nt-1||bt+1<=Oe)&&(Dn=!0,e.save(),e.beginPath(),e.rect(bt+1,st,gt-1,Pt),e.clip()),Ct=Ct===void 0?Lt.bgCell:An(Ct,Lt.bgCell)}const yn=P.sourceIndex===n.length-1,Pe=_e===s-1;Ct!==void 0&&(e.fillStyle=Ct,pe!==void 0&&(pe.fillStyle=Ct),M!==void 0?e.fillRect(bt+1,Ve+1,gt-(yn?2:1),nt-(Pe?2:1)):e.fillRect(bt,Ve,gt,nt)),et.style==="faded"&&(e.globalAlpha=.6);let Ft;for(let st=0;st<x.length;st++){const Xt=x[st];if(Xt.item[0]===P.sourceIndex&&Xt.item[1]===_e){Ft=Xt;break}}if(gt>X&&!Ot){const st=Lt.baseFontFull;st!==j&&(e.font=st,j=st),pe=Td(e,et,P.sourceIndex,_e,yn,Pe,bt,Ve,gt,nt,zt>0,Lt,Ct??Lt.bgCell,_,E,(Ft==null?void 0:Ft.hoverAmount)??0,L,C,G,D,pe,T,k,z,N)}return Dn&&e.restore(),et.style==="faded"&&(e.globalAlpha=1),re--,$e&&(e.restore(),(pn=pe==null?void 0:pe.deprep)==null||pn.call(pe,se),pe=void 0,it(),j=ge,e.font=ge),re<=0}),e.restore(),re<=0}),he}const Ui=[0,0],qi={x:0,y:0,width:0,height:0},as=[void 0,()=>{}];let Es=!1;function Wp(){Es=!0}function Td(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p,v,w,b,M,O,S,R,_,E){var k,z;let x,L;v!==void 0&&v[0][0]===n&&v[0][1]===r&&(x=v[1][0],L=v[1][1]);let D;Ui[0]=n,Ui[1]=r,qi.x=a,qi.y=l,qi.width=s,qi.height=u,as[0]=R.getValue(Ui),as[1]=N=>R.setValue(Ui,N),Es=!1;const C={ctx:e,theme:d,col:n,row:r,cell:t,rect:qi,highlighted:c,cellFillColor:h,hoverAmount:p,frameTime:b,hoverX:x,drawState:as,hoverY:L,imageLoader:f,spriteManager:m,hyperWrapping:w,overrideCursor:x!==void 0?E:void 0,requestAnimationFrame:Wp},I=Nm(C,t.lastUpdated,b,O,i,o),T=_(t);if(T!==void 0){(O==null?void 0:O.renderer)!==T&&((k=O==null?void 0:O.deprep)==null||k.call(O,C),O=void 0);const N=(z=T.drawPrep)==null?void 0:z.call(T,C,O);M!==void 0&&!mi(C.cell)?M(C,()=>T.draw(C,t)):T.draw(C,t),D=N===void 0?void 0:{deprep:N==null?void 0:N.deprep,fillStyle:N==null?void 0:N.fillStyle,font:N==null?void 0:N.font,renderer:T}}return(I||Es)&&(S==null||S(Ui)),D}function Gs(e,t,n,r,i,o,a,l,s=-20,u=-20,c=32,d="center",h="square"){const f=Math.floor(i+a/2),m=h==="circle"?1e4:t.roundingRadius??4;let p=td(c,a,t.cellVerticalPadding),v=p/2;const w=ed(d,r,o,t.cellHorizontalPadding,p),b=Qc(w,f,p),M=nd(r+s,i+u,b);switch(n){case!0:{e.beginPath(),cr(e,w-p/2,f-p/2,p,p,m),h==="circle"&&(v*=.8,p*=.8),e.fillStyle=l?t.accentColor:t.textMedium,e.fill(),e.beginPath(),e.moveTo(w-v+p/4.23,f-v+p/1.97),e.lineTo(w-v+p/2.42,f-v+p/1.44),e.lineTo(w-v+p/1.29,f-v+p/3.25),e.strokeStyle=t.bgCell,e.lineJoin="round",e.lineCap="round",e.lineWidth=1.9,e.stroke();break}case ra:case!1:{e.beginPath(),cr(e,w-p/2+.5,f-p/2+.5,p-1,p-1,m),e.lineWidth=1,e.strokeStyle=M?t.textDark:t.textMedium,e.stroke();break}case Hs:{e.beginPath(),cr(e,w-p/2,f-p/2,p,p,m),e.fillStyle=M?t.textMedium:t.textLight,e.fill(),h==="circle"&&(v*=.8,p*=.8),e.beginPath(),e.moveTo(w-p/3,f),e.lineTo(w+p/3,f),e.strokeStyle=t.bgCell,e.lineCap="round",e.lineWidth=1.9,e.stroke();break}default:io()}}function Up(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p,v,w,b){var x,L,D,C;const M=a+l;if(M<=0)return;e.fillStyle=d.bgHeader,e.fillRect(0,0,i,M);const O=(x=r==null?void 0:r[0])==null?void 0:x[0],S=(L=r==null?void 0:r[0])==null?void 0:L[1],R=(D=r==null?void 0:r[1])==null?void 0:D[0],_=(C=r==null?void 0:r[1])==null?void 0:C[1],E=d.headerFontFull;e.font=E,Fr(t,0,o,0,M,(I,T,k,z)=>{var P;if(v!==void 0&&!v.has([I.sourceIndex,-1]))return;const N=Math.max(0,z-T);e.save(),e.beginPath(),e.rect(T+N,l,I.width-N,a),e.clip();const X=p(I.group??"").overrideTheme,re=I.themeOverride===void 0&&X===void 0?d:vr(d,X,I.themeOverride);re.bgHeader!==d.bgHeader&&(e.fillStyle=re.bgHeader,e.fill()),re!==d&&(e.font=re.baseFontFull);const G=c.columns.hasIndex(I.sourceIndex),j=s!==void 0||u,se=!j&&S===-1&&O===I.sourceIndex,oe=j?0:((P=f.find(W=>W.item[0]===I.sourceIndex&&W.item[1]===-1))==null?void 0:P.hoverAmount)??0,ue=(c==null?void 0:c.current)!==void 0&&c.current.cell[0]===I.sourceIndex,he=G?re.accentColor:ue?re.bgHeaderHasFocus:re.bgHeader,Q=n?l:0,H=I.sourceIndex===0?0:1;G?(e.fillStyle=he,e.fillRect(T+H,Q,I.width-H,a)):(ue||oe>0)&&(e.beginPath(),e.rect(T+H,Q,I.width-H,a),ue&&(e.fillStyle=re.bgHeaderHasFocus,e.fill()),oe>0&&(e.globalAlpha=oe,e.fillStyle=re.bgHeaderHovered,e.fill(),e.globalAlpha=1)),Pd(e,T,Q,I.width,a,I,G,re,se,se?R:void 0,se?_:void 0,ue,oe,h,w,b),e.restore()}),n&&qp(e,t,i,o,l,r,d,h,f,m,p,v)}function qp(e,t,n,r,i,o,a,l,s,u,c,d){const[f,m]=(o==null?void 0:o[0])??[];let p=0;Rd(t,n,r,i,(v,w,b,M,O,S)=>{if(d!==void 0&&!d.hasItemInRectangle({x:v[0],y:-2,width:v[1]-v[0]+1,height:1}))return;e.save(),e.beginPath(),e.rect(b,M,O,S),e.clip();const R=c(w),_=(R==null?void 0:R.overrideTheme)===void 0?a:vr(a,R.overrideTheme),E=m===-2&&f!==void 0&&f>=v[0]&&f<=v[1],x=E?_.bgHeaderHovered:_.bgHeader;if(x!==a.bgHeader&&(e.fillStyle=x,e.fill()),e.fillStyle=_.textGroupHeader??_.textHeader,R!==void 0){let L=b;if(R.icon!==void 0&&(l.drawSprite(R.icon,"normal",e,L+8,(i-20)/2,20,_),L+=26),e.fillText(R.name,L+8,i/2+dr(e,a.headerFontFull)),R.actions!==void 0&&E){const D=Dd({x:b,y:M,width:O,height:S},R.actions);e.beginPath();const C=D[0].x-10,I=b+O-C;e.rect(C,0,I,i);const T=e.createLinearGradient(C,0,C+I,0),k=Gr(x,0);T.addColorStop(0,k),T.addColorStop(10/I,x),T.addColorStop(1,x),e.fillStyle=T,e.fill(),e.globalAlpha=.6;const[z,N]=(o==null?void 0:o[1])??[-1,-1];for(let X=0;X<R.actions.length;X++){const re=R.actions[X],G=D[X],j=Xr(G,z+b,N);j&&(e.globalAlpha=1),l.drawSprite(re.icon,"normal",e,G.x+G.width/2-10,G.y+G.height/2-10,20,_),j&&(e.globalAlpha=.6)}e.globalAlpha=1}}b!==0&&u(v[0])&&(e.beginPath(),e.moveTo(b+.5,0),e.lineTo(b+.5,i),e.strokeStyle=a.borderColor,e.lineWidth=1,e.stroke()),e.restore(),p=b+O}),e.beginPath(),e.moveTo(p+.5,0),e.lineTo(p+.5,i),e.moveTo(0,i+.5),e.lineTo(n,i+.5),e.strokeStyle=a.borderColor,e.lineWidth=1,e.stroke()}const $o=30;function Yp(e,t,n,r,i){return{x:e+n-$o,y:Math.max(t,t+r/2-$o/2),width:$o,height:Math.min($o,r)}}function Dd(e,t){const n=[];let r=e.x+e.width-26*t.length;const i=e.y+e.height/2-13,o=26,a=26;for(let l=0;l<t.length;l++)n.push({x:r,y:i,width:a,height:o}),r+=26;return n}function Yi(e,t,n){return!n||e===void 0||(e.x=t-(e.x-t)-e.width),e}function Od(e,t,n,r,i,o,a,l){var w;const s=a.cellHorizontalPadding,u=a.headerIconSize,c=Yp(n,r,i,o);let d=n+s;const h=t.icon===void 0?void 0:{x:d,y:r+(o-u)/2,width:u,height:u},f=h===void 0||t.overlayIcon===void 0?void 0:{x:h.x+9,y:h.y+6,width:18,height:18};h!==void 0&&(d+=Math.ceil(u*1.3));const m={x:d,y:r,width:i-d,height:o};let p;if(t.indicatorIcon!==void 0){const b=e===void 0?((w=md(t.title,a.headerFontFull))==null?void 0:w.width)??0:Qr(t.title,e,a.headerFontFull).width;m.width=b,d+=b+s,p={x:d,y:r+(o-u)/2,width:u,height:u}}const v=n+i/2;return{menuBounds:Yi(c,v,l),iconBounds:Yi(h,v,l),iconOverlayBounds:Yi(f,v,l),textBounds:Yi(m,v,l),indicatorIconBounds:Yi(p,v,l)}}function Cu(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p){if(o.rowMarker!==void 0&&o.headerRowMarkerDisabled!==!0){const b=o.rowMarkerChecked;b!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=d);const M=o.headerRowMarkerTheme!==void 0?vr(l,o.headerRowMarkerTheme):l;Gs(e,M,b,t,n,r,i,!1,void 0,void 0,18,"center",o.rowMarker),b!==!0&&o.headerRowMarkerAlwaysVisible!==!0&&(e.globalAlpha=1);return}const v=a?l.textHeaderSelected:l.textHeader,w=o.hasMenu===!0&&(s||f&&a)&&p.menuBounds!==void 0;if(o.icon!==void 0&&p.iconBounds!==void 0){let b=a?"selected":"normal";o.style==="highlight"&&(b=a?"selected":"special"),h.drawSprite(o.icon,b,e,p.iconBounds.x,p.iconBounds.y,p.iconBounds.width,l),o.overlayIcon!==void 0&&p.iconOverlayBounds!==void 0&&h.drawSprite(o.overlayIcon,a?"selected":"special",e,p.iconOverlayBounds.x,p.iconOverlayBounds.y,p.iconOverlayBounds.width,l)}if(w&&r>35){const M=m?35:r-35,O=m?35*.7:r-35*.7,S=M/r,R=O/r,_=e.createLinearGradient(t,0,t+r,0),E=Gr(v,0);_.addColorStop(m?1:0,v),_.addColorStop(S,v),_.addColorStop(R,E),_.addColorStop(m?0:1,E),e.fillStyle=_}else e.fillStyle=v;if(m&&(e.textAlign="right"),p.textBounds!==void 0&&e.fillText(o.title,m?p.textBounds.x+p.textBounds.width:p.textBounds.x,n+i/2+dr(e,l.headerFontFull)),m&&(e.textAlign="left"),o.indicatorIcon!==void 0&&p.indicatorIconBounds!==void 0&&(!w||!ao(p.menuBounds.x,p.menuBounds.y,p.menuBounds.width,p.menuBounds.height,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,p.indicatorIconBounds.height))){let b=a?"selected":"normal";o.style==="highlight"&&(b=a?"selected":"special"),h.drawSprite(o.indicatorIcon,b,e,p.indicatorIconBounds.x,p.indicatorIconBounds.y,p.indicatorIconBounds.width,l)}if(w&&p.menuBounds!==void 0){const b=p.menuBounds,M=u!==void 0&&c!==void 0&&Xr(b,u+t,c+n);if(M||(e.globalAlpha=.7),o.menuIcon===void 0||o.menuIcon===ia.Triangle){e.beginPath();const O=b.x+b.width/2-5.5,S=b.y+b.height/2-3;qm(e,[{x:O,y:S},{x:O+11,y:S},{x:O+5.5,y:S+6}],1),e.fillStyle=v,e.fill()}else if(o.menuIcon===ia.Dots){e.beginPath();const O=b.x+b.width/2,S=b.y+b.height/2;Um(e,O,S),e.fillStyle=v,e.fill()}else{const O=b.x+(b.width-l.headerIconSize)/2,S=b.y+(b.height-l.headerIconSize)/2;h.drawSprite(o.menuIcon,"normal",e,O,S,l.headerIconSize,l)}M||(e.globalAlpha=1)}}function Pd(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p){const v=Ns(o.title)==="rtl",w=Od(e,o,t,n,r,i,l,v);m!==void 0?m({ctx:e,theme:l,rect:{x:t,y:n,width:r,height:i},column:o,columnIndex:o.sourceIndex,isSelected:a,hoverAmount:h,isHovered:s,hasSelectedCell:d,spriteManager:f,menuBounds:(w==null?void 0:w.menuBounds)??{x:0,y:0,height:0,width:0}},()=>Cu(e,t,n,r,i,o,a,l,s,u,c,h,f,p,v,w)):Cu(e,t,n,r,i,o,a,l,s,u,c,h,f,p,v,w)}function Xp(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p,v,w,b){if(w!==void 0||t[t.length-1]!==n[t.length-1])return;const M=Md(v);Fr(t,s,a,l,o,(O,S,R,_,E)=>{if(O!==t[t.length-1])return;S+=O.width;const x=Math.max(S,_);x>r||(e.save(),e.beginPath(),e.rect(x,o+1,1e4,i-o-1),e.clip(),xa(E,R,i,u,c,m,p,M,(L,D,C,I)=>{if(!I&&v.length>0&&!v.some(X=>ao(S,L,1e4,C,X.x,X.y,X.width,X.height)))return;const T=h.hasIndex(D),k=f.hasIndex(D);e.beginPath();const z=d==null?void 0:d(D),N=z===void 0?b:vr(b,z);N.bgCell!==b.bgCell&&(e.fillStyle=N.bgCell,e.fillRect(S,L,1e4,C)),k&&(e.fillStyle=N.bgHeader,e.fillRect(S,L,1e4,C)),T&&(e.fillStyle=N.accentLight,e.fillRect(S,L,1e4,C))}),e.restore())})}function Gp(e,t,n,r,i,o,a,l,s){let u=!1;for(const m of t)if(!m.sticky){u=a(m.sourceIndex);break}const c=s.horizontalBorderColor??s.borderColor,d=s.borderColor,h=u?yi(t):0;let f;if(h!==0&&(f=uu(d,s.bgCell),e.beginPath(),e.moveTo(h+.5,0),e.lineTo(h+.5,r),e.strokeStyle=f,e.stroke()),i>0){const m=d===c&&f!==void 0?f:uu(c,s.bgCell),p=Jr(o,i,l);e.beginPath(),e.moveTo(0,r-p+.5),e.lineTo(n,r-p+.5),e.strokeStyle=m,e.stroke()}}const _d=(e,t,n)=>{let r=0,i=t,o=0,a=n;if(e!==void 0&&e.length>0){r=Number.MAX_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER,i=Number.MIN_SAFE_INTEGER,a=Number.MIN_SAFE_INTEGER;for(const l of e)r=Math.min(r,l.x-1),i=Math.max(i,l.x+l.width+1),o=Math.min(o,l.y-1),a=Math.max(a,l.y+l.height+1)}return{minX:r,maxX:i,minY:o,maxY:a}};function jp(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m){var C;const p=m.bgCell,{minX:v,maxX:w,minY:b,maxY:M}=_d(l,o,a),O=[],S=a-Jr(f,h,u);let R=s,_=n,E=0;for(;R+i<S;){const I=R+i,T=u(_);if(I>=b&&I<=M-1){const k=c==null?void 0:c(_),z=k==null?void 0:k.bgCell;z!==void 0&&z!==p&&_>=f-h&&O.push({x:v,y:I,w:w-v,h:T,color:z})}R+=T,_<f-h&&(E=R),_++}let x=0;const L=Math.min(S,M)-E;if(L>0)for(let I=0;I<t.length;I++){const T=t[I];if(T.width===0)continue;const k=T.sticky?x:x+r,z=(C=T.themeOverride)==null?void 0:C.bgCell;z!==void 0&&z!==p&&k>=v&&k<=w&&d(I+1)&&O.push({x:k,y:E,w:T.width,h:L,color:z}),x+=T.width}if(O.length===0)return;let D;e.beginPath();for(let I=O.length-1;I>=0;I--){const T=O[I];D===void 0?D=T.color:T.color!==D&&(e.fillStyle=D,e.fill(),e.beginPath(),D=T.color),e.rect(T.x,T.y,T.w,T.h)}D!==void 0&&(e.fillStyle=D,e.fill()),e.beginPath()}function Su(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p,v,w=!1){if(s!==void 0){e.beginPath(),e.save(),e.rect(0,0,o,a);for(const C of s)e.rect(C.x+1,C.y+1,C.width-1,C.height-1);e.clip("evenodd")}const b=v.horizontalBorderColor??v.borderColor,M=v.borderColor,{minX:O,maxX:S,minY:R,maxY:_}=_d(l,o,a),E=[];e.beginPath();let x=.5;for(let C=0;C<t.length;C++){const I=t[C];if(I.width===0)continue;x+=I.width;const T=I.sticky?x:x+r;T>=O&&T<=S&&f(C+1)&&E.push({x1:T,y1:Math.max(u,R),x2:T,y2:Math.min(a,_),color:M})}let L=a+.5;for(let C=p-m;C<p;C++){const I=d(C);L-=I,E.push({x1:O,y1:L,x2:S,y2:L,color:b})}if(w!==!0){let C=c+.5,I=n;const T=L;for(;C+i<T;){const k=C+i;if(k>=R&&k<=_-1){const z=h==null?void 0:h(I);E.push({x1:O,y1:k,x2:S,y2:k,color:(z==null?void 0:z.horizontalBorderColor)??(z==null?void 0:z.borderColor)??b})}C+=d(I),I++}}const D=Sh(E,C=>C.color);for(const C of Object.keys(D)){e.strokeStyle=C;for(const I of D[C])e.moveTo(I.x1,I.y1),e.lineTo(I.x2,I.y2);e.stroke(),e.beginPath()}s!==void 0&&e.restore()}function Kp(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p,v,w,b){const M=[];e.imageSmoothingEnabled=!1;const O=Math.min(i.cellYOffset,a),S=Math.max(i.cellYOffset,a);let R=0;if(typeof w=="number")R+=(S-O)*w;else for(let T=O;T<S;T++)R+=w(T);a>i.cellYOffset&&(R=-R),R+=s-i.translateY;const _=Math.min(i.cellXOffset,o),E=Math.max(i.cellXOffset,o);let x=0;for(let T=_;T<E;T++)x+=p[T].width;o>i.cellXOffset&&(x=-x),x+=l-i.translateX;const L=yi(v);if(x!==0&&R!==0)return{regions:[]};const D=u>0?Jr(h,u,w):0,C=c-L-Math.abs(x),I=d-f-D-Math.abs(R)-1;if(C>150&&I>150){const T={sx:0,sy:0,sw:c*m,sh:d*m,dx:0,dy:0,dw:c*m,dh:d*m};if(R>0?(T.sy=(f+1)*m,T.sh=I*m,T.dy=(R+f+1)*m,T.dh=I*m,M.push({x:0,y:f,width:c,height:R+1})):R<0&&(T.sy=(-R+f+1)*m,T.sh=I*m,T.dy=(f+1)*m,T.dh=I*m,M.push({x:0,y:d+R-D,width:c,height:-R+D})),x>0?(T.sx=L*m,T.sw=C*m,T.dx=(x+L)*m,T.dw=C*m,M.push({x:L-1,y:0,width:x+2,height:d})):x<0&&(T.sx=(L-x)*m,T.sw=C*m,T.dx=L*m,T.dw=C*m,M.push({x:c+x,y:0,width:-x,height:d})),e.setTransform(1,0,0,1,0,0),b){if(L>0&&x!==0&&R===0&&(r===void 0||(n==null?void 0:n[1])!==!1)){const k=L*m,z=d*m;e.drawImage(t,0,0,k,z,0,0,k,z)}if(D>0&&x===0&&R!==0&&(r===void 0||(n==null?void 0:n[0])!==!1)){const k=(d-D)*m,z=c*m,N=D*m;e.drawImage(t,0,k,z,N,0,k,z,N)}}e.drawImage(t,T.sx,T.sy,T.sw,T.sh,T.dx,T.dy,T.dw,T.dh),e.scale(m,m)}return e.imageSmoothingEnabled=!0,{regions:M}}function Zp(e,t,n,r,i,o,a,l,s,u){const c=[];return t!==e.cellXOffset||n!==e.cellYOffset||r!==e.translateX||i!==e.translateY||Fr(s,n,r,i,l,(d,h,f,m)=>{if(d.sourceIndex===u){const p=Math.max(h,m)+1;return c.push({x:p,y:0,width:o-p,height:a}),!0}}),c}function Jp(e,t){if(t===void 0||e.width!==t.width||e.height!==t.height||e.theme!==t.theme||e.headerHeight!==t.headerHeight||e.rowHeight!==t.rowHeight||e.rows!==t.rows||e.freezeColumns!==t.freezeColumns||e.getRowThemeOverride!==t.getRowThemeOverride||e.isFocused!==t.isFocused||e.isResizing!==t.isResizing||e.verticalBorder!==t.verticalBorder||e.getCellContent!==t.getCellContent||e.highlightRegions!==t.highlightRegions||e.selection!==t.selection||e.dragAndDropState!==t.dragAndDropState||e.prelightCells!==t.prelightCells||e.touchMode!==t.touchMode||e.maxScaleFactor!==t.maxScaleFactor)return!1;if(e.mappedColumns!==t.mappedColumns){if(e.mappedColumns.length>100||e.mappedColumns.length!==t.mappedColumns.length)return!1;let n;for(let r=0;r<e.mappedColumns.length;r++){const i=e.mappedColumns[r],o=t.mappedColumns[r];if(wi(i,o))continue;if(n!==void 0||i.width===o.width)return!1;const{width:a,...l}=i,{width:s,...u}=o;if(!wi(l,u))return!1;n=r}return n===void 0?!0:n}return!0}function xu(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p){const v=m==null?void 0:m.filter(_=>_.style!=="no-outline");if(v===void 0||v.length===0)return;const w=yi(l),b=Jr(f,h,d),M=[s,0,l.length,f-h],O=[w,0,t,n-b],S=v.map(_=>{const E=_.range,x=_.style??"dashed";return Np(E,M,t,n,O).map(L=>{const D=L.rect,C=Ms(D.x,D.y,t,n,c,u+c,r,i,o,a,f,s,h,l,d),I=D.width===1&&D.height===1?C:Ms(D.x+D.width-1,D.y+D.height-1,t,n,c,u+c,r,i,o,a,f,s,h,l,d);return D.x+D.width>=l.length&&(I.width-=1),D.y+D.height>=f&&(I.height-=1),{color:_.color,style:x,clip:L.clip,rect:$p({x:C.x,y:C.y,width:I.x+I.width-C.x,height:I.y+I.height-C.y},t,n,8)}})}),R=()=>{e.lineWidth=1;let _=!1;for(const E of S)for(const x of E)if((x==null?void 0:x.rect)!==void 0&&ao(0,0,t,n,x.rect.x,x.rect.y,x.rect.width,x.rect.height)){const L=_,D=!Vp(x.clip,x.rect);D&&(e.save(),e.rect(x.clip.x,x.clip.y,x.clip.width,x.clip.height),e.clip()),x.style==="dashed"&&!_?(e.setLineDash([5,3]),_=!0):(x.style==="solid"||x.style==="solid-outline")&&_&&(e.setLineDash([]),_=!1),e.strokeStyle=x.style==="solid-outline"?An(An(x.color,p.borderColor),p.bgCell):Gr(x.color,1),e.strokeRect(x.rect.x+.5,x.rect.y+.5,x.rect.width-1,x.rect.height-1),D&&(e.restore(),_=L)}_&&e.setLineDash([])};return R(),R}function ku(e,t,n,r,i){e.beginPath(),e.moveTo(t,n),e.lineTo(t,r),e.lineWidth=2,e.strokeStyle=i,e.stroke(),e.globalAlpha=1}function ss(e,t,n,r,i,o,a,l,s,u,c,d,h,f,m,p,v){if(c.current===void 0)return;const w=c.current.range,b=c.current.cell,M=[w.x+w.width-1,w.y+w.height-1];if(b[1]>=v&&M[1]>=v||!a.some(T=>T.sourceIndex===b[0]||T.sourceIndex===M[0]))return;const[S,R]=c.current.cell,_=h(c.current.cell),E=_.span??[S,S],x=R>=v-f,L=f>0&&!x?Jr(v,f,d)-1:0,D=M[1];let C;if(Fr(a,r,i,o,u,(T,k,z,N,X)=>{if(T.sticky&&S>T.sourceIndex)return;const re=T.sourceIndex<E[0],G=T.sourceIndex>E[1],j=T.sourceIndex===M[0];if(!(!j&&(re||G)))return xa(X,z,n,v,d,f,m,void 0,(se,oe,ue)=>{if(oe!==R&&oe!==D)return;let he=k,Q=T.width;if(_.span!==void 0){const P=Ed(_.span,k,se,T.width,ue,T,l),W=T.sticky?P[0]:P[1];W!==void 0&&(he=W.x,Q=W.width)}return oe===D&&j&&p&&(C=()=>{var P;N>he&&!T.sticky&&(e.beginPath(),e.rect(N,0,t-N,n),e.clip()),e.beginPath(),e.rect(he+Q-4,se+ue-4,4,4),e.fillStyle=((P=T.themeOverride)==null?void 0:P.accentColor)??s.accentColor,e.fill()}),C!==void 0}),C!==void 0}),C===void 0)return;const I=()=>{e.save(),e.beginPath(),e.rect(0,u,t,n-u-L),e.clip(),C==null||C(),e.restore()};return I(),I}function Qp(e,t,n,r,i,o,a,l,s){s===void 0||s.size===0||(e.beginPath(),Rd(t,n,o,r,(u,c,d,h,f,m)=>{s.hasItemInRectangle({x:u[0],y:-2,width:u[1]-u[0]+1,height:1})&&e.rect(d,h,f,m)}),Fr(t,l,o,a,i,(u,c,d,h)=>{const f=Math.max(0,h-c),m=c+f+1,p=u.width-f-1;s.has([u.sourceIndex,-1])&&e.rect(m,r,p,i-r)}),e.clip())}function e0(e,t,n,r,i,o,a,l,s,u){let c=0;return Fr(e,o,r,i,n,(d,h,f,m,p)=>(xa(p,f,t,a,l,s,u,void 0,(v,w,b,M)=>{M||(c=Math.max(w,c))}),!0)),c}function Mu(e,t){var yn;const{canvasCtx:n,headerCanvasCtx:r,width:i,height:o,cellXOffset:a,cellYOffset:l,translateX:s,translateY:u,mappedColumns:c,enableGroups:d,freezeColumns:h,dragAndDropState:f,theme:m,drawFocus:p,headerHeight:v,groupHeaderHeight:w,disabledRows:b,rowHeight:M,verticalBorder:O,overrideCursor:S,isResizing:R,selection:_,fillHandle:E,freezeTrailingRows:x,rows:L,getCellContent:D,getGroupDetails:C,getRowThemeOverride:I,isFocused:T,drawHeaderCallback:k,prelightCells:z,drawCellCallback:N,highlightRegions:X,resizeCol:re,imageLoader:G,lastBlitData:j,hoverValues:se,hyperWrapping:oe,hoverInfo:ue,spriteManager:he,maxScaleFactor:Q,hasAppendRow:H,touchMode:P,enqueue:W,renderStateProvider:ce,getCellRenderer:Oe,renderStrategy:ze,bufferACtx:Ce,bufferBCtx:Ee,damage:xe,minimumCellWidth:It,resizeIndicator:yt}=e;if(i===0||o===0)return;const it=ze==="double-buffer",le=Math.min(Q,Math.ceil(window.devicePixelRatio??1)),tt=ze!=="direct"&&Jp(e,t),ve=n.canvas;(ve.width!==i*le||ve.height!==o*le)&&(ve.width=i*le,ve.height=o*le,ve.style.width=i+"px",ve.style.height=o+"px");const ge=r.canvas,pe=d?w+v:v,Ve=pe+1;(ge.width!==i*le||ge.height!==Ve*le)&&(ge.width=i*le,ge.height=Ve*le,ge.style.width=i+"px",ge.style.height=Ve+"px");const _e=Ce.canvas,nt=Ee.canvas;it&&(_e.width!==i*le||_e.height!==o*le)&&(_e.width=i*le,_e.height=o*le,j.current!==void 0&&(j.current.aBufferScroll=void 0)),it&&(nt.width!==i*le||nt.height!==o*le)&&(nt.width=i*le,nt.height=o*le,j.current!==void 0&&(j.current.bBufferScroll=void 0));const be=j.current;if(tt===!0&&a===(be==null?void 0:be.cellXOffset)&&l===(be==null?void 0:be.cellYOffset)&&s===(be==null?void 0:be.translateX)&&u===(be==null?void 0:be.translateY))return;let de=null;it&&(de=n);const ot=r;let He;it?xe!==void 0?He=(be==null?void 0:be.lastBuffer)==="b"?Ee:Ce:He=(be==null?void 0:be.lastBuffer)==="b"?Ce:Ee:He=n;const et=He.canvas,bt=it?et===_e?nt:_e:ve,gt=typeof M=="number"?()=>M:M;ot.save(),He.save(),ot.beginPath(),He.beginPath(),ot.textBaseline="middle",He.textBaseline="middle",le!==1&&(ot.scale(le,le),He.scale(le,le));const $e=ks(c,a,i,f,s);let Ot=[];const Yt=p&&((yn=_.current)==null?void 0:yn.cell[1])===l&&u===0;let kt=!1;if(X!==void 0){for(const Pe of X)if(Pe.style!=="no-outline"&&Pe.range.y===l&&u===0){kt=!0;break}}const Lt=()=>{Up(ot,$e,d,ue,i,s,v,w,f,R,_,m,he,se,O,C,xe,k,P),Su(ot,$e,l,s,u,i,o,void 0,void 0,w,pe,gt,I,O,x,L,m,!0),ot.beginPath(),ot.moveTo(0,Ve-.5),ot.lineTo(i,Ve-.5),ot.strokeStyle=An(m.headerBottomBorderColor??m.horizontalBorderColor??m.borderColor,m.bgHeader),ot.stroke(),kt&&xu(ot,i,o,a,l,s,u,c,h,v,w,M,x,L,X,m),Yt&&ss(ot,i,o,l,s,u,$e,c,m,pe,_,gt,D,x,H,E,L)};if(xe!==void 0){const Pe=$e[$e.length-1].sourceIndex+1,Ft=xe.hasItemInRegion([{x:a,y:-2,width:Pe,height:2},{x:a,y:l,width:Pe,height:300},{x:0,y:l,width:h,height:300},{x:0,y:-2,width:h,height:2},{x:a,y:L-x,width:Pe,height:x,when:x>0}]),fn=hn=>{yu(hn,$e,c,o,pe,s,u,l,L,gt,D,C,I,b,T,p,x,H,Ot,xe,_,z,X,G,he,se,ue,N,oe,m,W,ce,Oe,S,It);const pn=_.current;E&&p&&pn!==void 0&&xe.has(fd(pn.range))&&ss(hn,i,o,l,s,u,$e,c,m,pe,_,gt,D,x,H,E,L)};Ft&&(fn(He),de!==null&&(de.save(),de.scale(le,le),de.textBaseline="middle",fn(de),de.restore()),xe.hasHeader()&&(Qp(ot,$e,i,w,pe,s,u,l,xe),Lt())),He.restore(),ot.restore();return}if((tt!==!0||a!==(be==null?void 0:be.cellXOffset)||s!==(be==null?void 0:be.translateX)||Yt!==(be==null?void 0:be.mustDrawFocusOnHeader)||kt!==(be==null?void 0:be.mustDrawHighlightRingsOnHeader))&&Lt(),tt===!0){Fn(bt!==void 0&&be!==void 0);const{regions:Pe}=Kp(He,bt,bt===_e?be.aBufferScroll:be.bBufferScroll,bt===_e?be.bBufferScroll:be.aBufferScroll,be,a,l,s,u,x,i,o,L,pe,le,c,$e,M,it);Ot=Pe}else tt!==!1&&(Fn(be!==void 0),Ot=Zp(be,a,l,s,u,i,o,pe,$e,tt));Gp(He,$e,i,o,x,L,O,gt,m);const nn=xu(He,i,o,a,l,s,u,c,h,v,w,M,x,L,X,m),zt=p?ss(He,i,o,l,s,u,$e,c,m,pe,_,gt,D,x,H,E,L):void 0;if(He.fillStyle=m.bgCell,Ot.length>0){He.beginPath();for(const Pe of Ot)He.rect(Pe.x,Pe.y,Pe.width,Pe.height);He.clip(),He.fill(),He.beginPath()}else He.fillRect(0,0,i,o);const ln=yu(He,$e,c,o,pe,s,u,l,L,gt,D,C,I,b,T,p,x,H,Ot,xe,_,z,X,G,he,se,ue,N,oe,m,W,ce,Oe,S,It);Xp(He,$e,c,i,o,pe,s,u,l,L,gt,I,_.rows,b,x,H,Ot,xe,m),jp(He,$e,l,s,u,i,o,Ot,pe,gt,I,O,x,L,m),Su(He,$e,l,s,u,i,o,Ot,ln,w,pe,gt,I,O,x,L,m),nn==null||nn(),zt==null||zt(),R&&yt!=="none"&&Fr($e,0,s,0,pe,(Pe,Ft)=>Pe.sourceIndex===re?(ku(ot,Ft+Pe.width,0,pe+1,An(m.resizeIndicatorColor??m.accentLight,m.bgHeader)),yt==="full"&&ku(He,Ft+Pe.width,pe,o,An(m.resizeIndicatorColor??m.accentLight,m.bgCell)),!0):!1),de!==null&&(de.fillStyle=m.bgCell,de.fillRect(0,0,i,o),de.drawImage(He.canvas,0,0));const Tt=e0($e,o,pe,s,u,l,L,gt,x,H);G==null||G.setWindow({x:a,y:l,width:$e.length,height:Tt-l},h,Array.from({length:x},(Pe,Ft)=>L-1-Ft));const Ct=be!==void 0&&(a!==be.cellXOffset||s!==be.translateX),Dn=be!==void 0&&(l!==be.cellYOffset||u!==be.translateY);j.current={cellXOffset:a,cellYOffset:l,translateX:s,translateY:u,mustDrawFocusOnHeader:Yt,mustDrawHighlightRingsOnHeader:kt,lastBuffer:it?et===_e?"a":"b":void 0,aBufferScroll:et===_e?[Ct,Dn]:be==null?void 0:be.aBufferScroll,bBufferScroll:et===nt?[Ct,Dn]:be==null?void 0:be.bBufferScroll},He.restore(),ot.restore()}const t0=80;function n0(e){const t=e-1;return t*t*t+1}class r0{constructor(t){ft(this,"callback");ft(this,"currentHoveredItem");ft(this,"leavingItems",[]);ft(this,"lastAnimationTime");ft(this,"addToLeavingItems",t=>{this.leavingItems.some(r=>eo(r.item,t.item))||this.leavingItems.push(t)});ft(this,"removeFromLeavingItems",t=>{const n=this.leavingItems.find(r=>eo(r.item,t));return this.leavingItems=this.leavingItems.filter(r=>r!==n),(n==null?void 0:n.hoverAmount)??0});ft(this,"cleanUpLeavingElements",()=>{this.leavingItems=this.leavingItems.filter(t=>t.hoverAmount>0)});ft(this,"shouldStep",()=>{const t=this.leavingItems.length>0,n=this.currentHoveredItem!==void 0&&this.currentHoveredItem.hoverAmount<1;return t||n});ft(this,"getAnimatingItems",()=>this.currentHoveredItem!==void 0?[...this.leavingItems,this.currentHoveredItem]:this.leavingItems.map(t=>({...t,hoverAmount:n0(t.hoverAmount)})));ft(this,"step",t=>{if(this.lastAnimationTime===void 0)this.lastAnimationTime=t;else{const r=(t-this.lastAnimationTime)/t0;for(const o of this.leavingItems)o.hoverAmount=Un(o.hoverAmount-r,0,1);this.currentHoveredItem!==void 0&&(this.currentHoveredItem.hoverAmount=Un(this.currentHoveredItem.hoverAmount+r,0,1));const i=this.getAnimatingItems();this.callback(i),this.cleanUpLeavingElements()}this.shouldStep()?(this.lastAnimationTime=t,window.requestAnimationFrame(this.step)):this.lastAnimationTime=void 0});ft(this,"setHovered",t=>{var n;if(!eo((n=this.currentHoveredItem)==null?void 0:n.item,t)){if(this.currentHoveredItem!==void 0&&this.addToLeavingItems(this.currentHoveredItem),t!==void 0){const r=this.removeFromLeavingItems(t);this.currentHoveredItem={item:t,hoverAmount:r}}else this.currentHoveredItem=void 0;this.lastAnimationTime===void 0&&window.requestAnimationFrame(this.step)}});this.callback=t}}class i0{constructor(t){ft(this,"fn");ft(this,"val");this.fn=t}get value(){return this.val??(this.val=this.fn())}}function js(e){return new i0(e)}const o0=js(()=>window.navigator.userAgent.includes("Firefox")),la=js(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")&&!window.navigator.userAgent.includes("Chrome")),ua=js(()=>window.navigator.platform.toLowerCase().startsWith("mac"));function a0(e){const t=g.useRef([]),n=g.useRef(0),r=g.useRef(e);r.current=e;const i=g.useCallback(()=>{const o=()=>window.requestAnimationFrame(a),a=()=>{const l=t.current.map(Xs);t.current=[],r.current(new no(l)),t.current.length>0?n.current++:n.current=0};window.requestAnimationFrame(n.current>600?o:a)},[]);return g.useCallback(o=>{t.current.length===0&&i();const a=tr(o[0],o[1]);t.current.includes(a)||t.current.push(a)},[i])}const Or="header",qn="group-header",ca="out-of-bounds";var vi;(function(e){e[e.Start=-2]="Start",e[e.StartPadding=-1]="StartPadding",e[e.Center=0]="Center",e[e.EndPadding=1]="EndPadding",e[e.End=2]="End"})(vi||(vi={}));function Ld(e,t){return e===t?!0:(e==null?void 0:e.kind)==="out-of-bounds"?(e==null?void 0:e.kind)===(t==null?void 0:t.kind)&&(e==null?void 0:e.location[0])===(t==null?void 0:t.location[0])&&(e==null?void 0:e.location[1])===(t==null?void 0:t.location[1])&&(e==null?void 0:e.region[0])===(t==null?void 0:t.region[0])&&(e==null?void 0:e.region[1])===(t==null?void 0:t.region[1]):(e==null?void 0:e.kind)===(t==null?void 0:t.kind)&&(e==null?void 0:e.location[0])===(t==null?void 0:t.location[0])&&(e==null?void 0:e.location[1])===(t==null?void 0:t.location[1])}const Ru=6,s0=(e,t)=>{if(e.kind===te.Custom)return e.copyData;const n=t==null?void 0:t(e);return(n==null?void 0:n.getAccessibilityString(e))??""},l0=(e,t)=>{const{width:n,height:r,accessibilityHeight:i,columns:o,cellXOffset:a,cellYOffset:l,headerHeight:s,fillHandle:u=!1,groupHeaderHeight:c,rowHeight:d,rows:h,getCellContent:f,getRowThemeOverride:m,onHeaderMenuClick:p,onHeaderIndicatorClick:v,enableGroups:w,isFilling:b,onCanvasFocused:M,onCanvasBlur:O,isFocused:S,selection:R,freezeColumns:_,onContextMenu:E,freezeTrailingRows:x,fixedShadowX:L=!0,fixedShadowY:D=!0,drawFocusRing:C,onMouseDown:I,onMouseUp:T,onMouseMoveRaw:k,onMouseMove:z,onItemHovered:N,dragAndDropState:X,firstColAccessible:re,onKeyDown:G,onKeyUp:j,highlightRegions:se,canvasRef:oe,onDragStart:ue,onDragEnd:he,eventTargetRef:Q,isResizing:H,resizeColumn:P,isDragging:W,isDraggable:ce=!1,allowResize:Oe,disabledRows:ze,hasAppendRow:Ce,getGroupDetails:Ee,theme:xe,prelightCells:It,headerIcons:yt,verticalBorder:it,drawCell:le,drawHeader:tt,onCellFocused:ve,onDragOverCell:ge,onDrop:pe,onDragLeave:Ve,imageWindowLoader:_e,smoothScrollX:nt=!1,smoothScrollY:be=!1,experimental:de,getCellRenderer:ot,resizeIndicator:He="full"}=e,et=e.translateX??0,bt=e.translateY??0,gt=Math.max(_,Math.min(o.length-1,a)),$e=g.useRef(null),Ot=g.useRef((de==null?void 0:de.eventTarget)??window),Yt=Ot.current,kt=_e,Lt=g.useRef(),[nn,zt]=g.useState(!1),ln=g.useRef([]),Tt=g.useRef(),[Ct,Dn]=g.useState(),[yn,Pe]=g.useState(),Ft=g.useRef(null),[fn,hn]=g.useState(),[pn,st]=g.useState(!1),Xt=g.useRef(pn);Xt.current=pn;const Pt=g.useMemo(()=>new Hp(yt,()=>{Wt.current=void 0,Sn.current()}),[yt]),rn=w?c+s:s,Dt=g.useRef(-1),At=((de==null?void 0:de.enableFirefoxRescaling)??!1)&&o0.value,We=((de==null?void 0:de.enableSafariRescaling)??!1)&&la.value;g.useLayoutEffect(()=>{window.devicePixelRatio===1||!At&&!We||(Dt.current!==-1&&zt(!0),window.clearTimeout(Dt.current),Dt.current=window.setTimeout(()=>{zt(!1),Dt.current=-1},200))},[l,gt,et,bt,At,We]);const Bt=Pm(o,_),_t=L?yi(Bt,X):0,Vt=g.useCallback(($,ae,Se)=>{const ke=$.getBoundingClientRect();if(ae>=Bt.length||Se>=h)return;const me=ke.width/n,Re=Ms(ae,Se,n,r,c,rn,gt,l,et,bt,h,_,x,Bt,d);return me!==1&&(Re.x*=me,Re.y*=me,Re.width*=me,Re.height*=me),Re.x+=ke.x,Re.y+=ke.y,Re},[n,r,c,rn,gt,l,et,bt,h,_,x,Bt,d]),$t=g.useCallback(($,ae,Se,ke)=>{const me=$.getBoundingClientRect(),Re=me.width/n,rt=(ae-me.left)/Re,at=(Se-me.top)/Re,Te=5,Mt=ks(Bt,gt,n,void 0,et);let Me=0,Ke=0;ke instanceof MouseEvent&&(Me=ke.button,Ke=ke.buttons);const qe=Am(rt,Mt,et),ht=Hm(at,r,w,s,c,h,d,l,bt,x),sn=(ke==null?void 0:ke.shiftKey)===!0,En=(ke==null?void 0:ke.ctrlKey)===!0,St=(ke==null?void 0:ke.metaKey)===!0,Jt=ke!==void 0&&!(ke instanceof MouseEvent)||(ke==null?void 0:ke.pointerType)==="touch",jn=[rt<0?-1:n<rt?1:0,at<rn?-1:r<at?1:0];let xn;if(qe===-1||at<0||rt<0||ht===void 0||rt>n||at>r){const Et=rt>n?1:rt<0?-1:0,In=at>r?1:at<0?-1:0;let On=Et*2,xt=In*2;Et===0&&(On=qe===-1?vi.EndPadding:vi.Center),In===0&&(xt=ht===void 0?vi.EndPadding:vi.Center);let en=!1;if(qe===-1&&ht===-1){const Rr=Vt($,Bt.length-1,-1);Fn(Rr!==void 0),en=ae<Rr.x+Rr.width+Te}const Mr=rt>n&&rt<n+Ss()||at>r&&at<r+Ss();xn={kind:ca,location:[qe!==-1?qe:rt<0?0:Bt.length-1,ht??h-1],region:[On,xt],shiftKey:sn,ctrlKey:En,metaKey:St,isEdge:en,isTouch:Jt,button:Me,buttons:Ke,scrollEdge:jn,isMaybeScrollbar:Mr}}else if(ht<=-1){let Et=Vt($,qe,ht);Fn(Et!==void 0);let In=Et!==void 0&&Et.x+Et.width-ae<=Te;const On=qe-1;ae-Et.x<=Te&&On>=0?(In=!0,Et=Vt($,On,ht),Fn(Et!==void 0),xn={kind:w&&ht===-2?qn:Or,location:[On,ht],bounds:Et,group:Bt[On].group??"",isEdge:In,shiftKey:sn,ctrlKey:En,metaKey:St,isTouch:Jt,localEventX:ae-Et.x,localEventY:Se-Et.y,button:Me,buttons:Ke,scrollEdge:jn}):xn={kind:w&&ht===-2?qn:Or,group:Bt[qe].group??"",location:[qe,ht],bounds:Et,isEdge:In,shiftKey:sn,ctrlKey:En,metaKey:St,isTouch:Jt,localEventX:ae-Et.x,localEventY:Se-Et.y,button:Me,buttons:Ke,scrollEdge:jn}}else{const Et=Vt($,qe,ht);Fn(Et!==void 0);const In=Et!==void 0&&Et.x+Et.width-ae<Te;let On=!1;if(u&&R.current!==void 0){const xt=fd(R.current.range),en=Vt($,xt[0],xt[1]);if(en!==void 0){const Mr=en.x+en.width-2,Rr=en.y+en.height-2;On=Math.abs(Mr-ae)<Ru&&Math.abs(Rr-Se)<Ru}}xn={kind:"cell",location:[qe,ht],bounds:Et,isEdge:In,shiftKey:sn,ctrlKey:En,isFillHandle:On,metaKey:St,isTouch:Jt,localEventX:ae-Et.x,localEventY:Se-Et.y,button:Me,buttons:Ke,scrollEdge:jn}}return xn},[n,Bt,gt,et,r,w,s,c,h,d,l,bt,x,Vt,u,R,rn]),[Cn]=Ct??[],nr=g.useRef(()=>{}),Hn=g.useRef(Ct);Hn.current=Ct;const[V,Qe]=g.useMemo(()=>{const $=document.createElement("canvas"),ae=document.createElement("canvas");return $.style.display="none",$.style.opacity="0",$.style.position="fixed",ae.style.display="none",ae.style.opacity="0",ae.style.position="fixed",[$.getContext("2d",{alpha:!1}),ae.getContext("2d",{alpha:!1})]},[]);g.useLayoutEffect(()=>{if(!(V===null||Qe===null))return document.documentElement.append(V.canvas),document.documentElement.append(Qe.canvas),()=>{V.canvas.remove(),Qe.canvas.remove()}},[V,Qe]);const Ue=g.useMemo(()=>new Ym,[]),fe=At&&nn?1:We&&nn?2:5,lt=(de==null?void 0:de.disableMinimumCellWidth)===!0?1:10,Wt=g.useRef(),dn=g.useRef(null),Zt=g.useRef(null),jt=g.useCallback(()=>{var rt;const $=$e.current,ae=Ft.current;if($===null||ae===null||(dn.current===null&&(dn.current=$.getContext("2d",{alpha:!1}),$.width=0,$.height=0),Zt.current===null&&(Zt.current=ae.getContext("2d",{alpha:!1}),ae.width=0,ae.height=0),dn.current===null||Zt.current===null||V===null||Qe===null))return;let Se=!1;const ke=at=>{Se=!0,hn(at)},me=Wt.current,Re={headerCanvasCtx:Zt.current,canvasCtx:dn.current,bufferACtx:V,bufferBCtx:Qe,width:n,height:r,cellXOffset:gt,cellYOffset:l,translateX:Math.round(et),translateY:Math.round(bt),mappedColumns:Bt,enableGroups:w,freezeColumns:_,dragAndDropState:X,theme:xe,headerHeight:s,groupHeaderHeight:c,disabledRows:ze??pt.empty(),rowHeight:d,verticalBorder:it,isResizing:H,resizeCol:P,isFocused:S,selection:R,fillHandle:u,drawCellCallback:le,hasAppendRow:Ce,overrideCursor:ke,maxScaleFactor:fe,freezeTrailingRows:x,rows:h,drawFocus:C,getCellContent:f,getGroupDetails:Ee??(at=>({name:at})),getRowThemeOverride:m,drawHeaderCallback:tt,prelightCells:It,highlightRegions:se,imageLoader:kt,lastBlitData:Tt,damage:Lt.current,hoverValues:ln.current,hoverInfo:Hn.current,spriteManager:Pt,scrolling:nn,hyperWrapping:(de==null?void 0:de.hyperWrapping)??!1,touchMode:pn,enqueue:nr.current,renderStateProvider:Ue,renderStrategy:(de==null?void 0:de.renderStrategy)??(la.value?"double-buffer":"single-buffer"),getCellRenderer:ot,minimumCellWidth:lt,resizeIndicator:He};Re.damage===void 0?(Wt.current=Re,Mu(Re,me)):Mu(Re,void 0),!Se&&(Re.damage===void 0||Re.damage.has((rt=Hn==null?void 0:Hn.current)==null?void 0:rt[0]))&&hn(void 0)},[V,Qe,n,r,gt,l,et,bt,Bt,w,_,X,xe,s,c,ze,d,it,H,Ce,P,S,R,u,x,h,C,fe,f,Ee,m,le,tt,It,se,kt,Pt,nn,de==null?void 0:de.hyperWrapping,de==null?void 0:de.renderStrategy,pn,Ue,ot,lt,He]),Sn=g.useRef(jt);g.useLayoutEffect(()=>{jt(),Sn.current=jt},[jt]),g.useLayoutEffect(()=>{(async()=>{var ae;((ae=document==null?void 0:document.fonts)==null?void 0:ae.ready)!==void 0&&(await document.fonts.ready,Wt.current=void 0,Sn.current())})()},[]);const vn=g.useCallback($=>{Lt.current=$,Sn.current(),Lt.current=void 0},[]),Cr=a0(vn);nr.current=Cr;const Xn=g.useCallback($=>{vn(new no($.map(ae=>ae.cell)))},[vn]);kt.setCallback(vn);const[mo,Ea]=g.useState(!1),[ei,Sr]=Cn??[],Si=ei!==void 0&&Sr===-1,Ia=ei!==void 0&&Sr===-2;let xi=!1,ki=!1,Ar=fn;if(Ar===void 0&&ei!==void 0&&Sr!==void 0&&Sr>-1&&Sr<h){const $=f([ei,Sr],!0);xi=$.kind===Yn.NewRow||$.kind===Yn.Marker&&$.markerKind!=="number",ki=$.kind===te.Boolean&&zs($),Ar=$.cursor}const Rn=W?"grabbing":(yn??!1)||H?"col-resize":mo||b?"crosshair":Ar!==void 0?Ar:Si||xi||ki||Ia?"pointer":"default",Mi=g.useMemo(()=>({contain:"strict",display:"block",cursor:Rn}),[Rn]),Ri=g.useRef("default"),ti=Q==null?void 0:Q.current;ti!=null&&Ri.current!==Mi.cursor&&(ti.style.cursor=Ri.current=Mi.cursor);const fr=g.useCallback(($,ae,Se,ke)=>{if(Ee===void 0)return;const me=Ee($);if(me.actions!==void 0){const Re=Dd(ae,me.actions);for(const[rt,at]of Re.entries())if(Xr(at,Se+ae.x,ke+at.y))return me.actions[rt]}},[Ee]),hr=g.useCallback(($,ae,Se,ke)=>{const me=Bt[ae];if(!W&&!H&&!(yn??!1)){const Re=Vt($,ae,-1);Fn(Re!==void 0);const rt=Od(void 0,me,Re.x,Re.y,Re.width,Re.height,xe,Ns(me.title)==="rtl");if(me.hasMenu===!0&&rt.menuBounds!==void 0&&Xr(rt.menuBounds,Se,ke))return{area:"menu",bounds:rt.menuBounds};if(me.indicatorIcon!==void 0&&rt.indicatorIconBounds!==void 0&&Xr(rt.indicatorIconBounds,Se,ke))return{area:"indicator",bounds:rt.indicatorIconBounds}}},[Bt,Vt,yn,W,H,xe]),ni=g.useRef(0),rr=g.useRef(),ir=g.useRef(!1),Hr=g.useCallback($=>{const ae=$e.current,Se=Q==null?void 0:Q.current;if(ae===null||$.target!==ae&&$.target!==Se)return;ir.current=!0;let ke,me;if($ instanceof MouseEvent?(ke=$.clientX,me=$.clientY):(ke=$.touches[0].clientX,me=$.touches[0].clientY),$.target===Se&&Se!==null){const rt=Se.getBoundingClientRect();if(ke>rt.right||me>rt.bottom)return}const Re=$t(ae,ke,me,$);rr.current=Re.location,Re.isTouch&&(ni.current=Date.now()),Xt.current!==Re.isTouch&&st(Re.isTouch),!(Re.kind===Or&&hr(ae,Re.location[0],ke,me)!==void 0)&&(Re.kind===qn&&fr(Re.group,Re.bounds,Re.localEventX,Re.localEventY)!==void 0||(I==null||I(Re),!Re.isTouch&&ce!==!0&&ce!==Re.kind&&Re.button<3&&Re.button!==1&&$.preventDefault()))},[Q,ce,$t,fr,hr,I]);gn("touchstart",Hr,Yt,!1),gn("mousedown",Hr,Yt,!1);const po=g.useRef(0),Ei=g.useCallback($=>{var Ke,qe;const ae=po.current;po.current=Date.now();const Se=$e.current;if(ir.current=!1,T===void 0||Se===null)return;const ke=Q==null?void 0:Q.current,me=$.target!==Se&&$.target!==ke;let Re,rt,at=!0;if($ instanceof MouseEvent){if(Re=$.clientX,rt=$.clientY,at=$.button<3,$.pointerType==="touch")return}else Re=$.changedTouches[0].clientX,rt=$.changedTouches[0].clientY;let Te=$t(Se,Re,rt,$);Te.isTouch&&ni.current!==0&&Date.now()-ni.current>500&&(Te={...Te,isLongTouch:!0}),ae!==0&&Date.now()-ae<(Te.isTouch?1e3:500)&&(Te={...Te,isDoubleClick:!0}),Xt.current!==Te.isTouch&&st(Te.isTouch),!me&&$.cancelable&&at&&$.preventDefault();const[Mt]=Te.location,Me=hr(Se,Mt,Re,rt);if(Te.kind===Or&&Me!==void 0){(Te.button!==0||((Ke=rr.current)==null?void 0:Ke[0])!==Mt||((qe=rr.current)==null?void 0:qe[1])!==-1)&&T(Te,!0);return}else if(Te.kind===qn){const ht=fr(Te.group,Te.bounds,Te.localEventX,Te.localEventY);if(ht!==void 0){Te.button===0&&ht.onClick(Te);return}}T(Te,me)},[T,Q,$t,hr,fr]);gn("mouseup",Ei,Yt,!1),gn("touchend",Ei,Yt,!1);const je=g.useCallback($=>{var Mt,Me;const ae=$e.current;if(ae===null)return;const Se=Q==null?void 0:Q.current,ke=$.target!==ae&&$.target!==Se;let me,Re,rt=!0;$ instanceof MouseEvent?(me=$.clientX,Re=$.clientY,rt=$.button<3):(me=$.changedTouches[0].clientX,Re=$.changedTouches[0].clientY);const at=$t(ae,me,Re,$);Xt.current!==at.isTouch&&st(at.isTouch),!ke&&$.cancelable&&rt&&$.preventDefault();const[Te]=at.location;if(at.kind===Or){const Ke=hr(ae,Te,me,Re);Ke!==void 0&&at.button===0&&((Mt=rr.current)==null?void 0:Mt[0])===Te&&((Me=rr.current)==null?void 0:Me[1])===-1&&(Ke.area==="menu"?p==null||p(Te,Ke.bounds):Ke.area==="indicator"&&(v==null||v(Te,Ke.bounds)))}else if(at.kind===qn){const Ke=fr(at.group,at.bounds,at.localEventX,at.localEventY);Ke!==void 0&&at.button===0&&Ke.onClick(at)}},[Q,$t,hr,p,v,fr]);gn("click",je,Yt,!1);const vo=g.useCallback($=>{const ae=$e.current,Se=Q==null?void 0:Q.current;if(ae===null||$.target!==ae&&$.target!==Se||E===void 0)return;const ke=$t(ae,$.clientX,$.clientY,$);E(ke,()=>{$.cancelable&&$.preventDefault()})},[Q,$t,E]);gn("contextmenu",vo,(Q==null?void 0:Q.current)??null,!1);const bo=g.useCallback($=>{Lt.current=new no($.map(ae=>ae.item)),ln.current=$,Sn.current(),Lt.current=void 0},[]),zn=g.useMemo(()=>new r0(bo),[bo]),wo=g.useRef(zn);wo.current=zn,g.useLayoutEffect(()=>{const $=wo.current;if(Cn===void 0||Cn[1]<0){$.setHovered(Cn);return}const ae=f(Cn,!0),Se=ot(ae),ke=Se===void 0&&ae.kind===te.Custom||(Se==null?void 0:Se.needsHover)!==void 0&&(typeof Se.needsHover=="boolean"?Se.needsHover:Se.needsHover(ae));$.setHovered(ke?Cn:void 0)},[f,ot,Cn]);const Gn=g.useRef(),Ii=g.useCallback($=>{var at;const ae=$e.current;if(ae===null)return;const Se=Q==null?void 0:Q.current,ke=$.target!==ae&&$.target!==Se,me=$t(ae,$.clientX,$.clientY,$);if(me.kind!=="out-of-bounds"&&ke&&!ir.current&&!me.isTouch)return;const Re=(Te,Mt)=>{Dn(Me=>Me===Te||(Me==null?void 0:Me[0][0])===(Te==null?void 0:Te[0][0])&&(Me==null?void 0:Me[0][1])===(Te==null?void 0:Te[0][1])&&((Me==null?void 0:Me[1][0])===(Te==null?void 0:Te[1][0])&&(Me==null?void 0:Me[1][1])===(Te==null?void 0:Te[1][1])||!Mt)?Me:Te)};if(!Ld(me,Gn.current))hn(void 0),N==null||N(me),Re(me.kind===ca?void 0:[me.location,[me.localEventX,me.localEventY]],!0),Gn.current=me;else if(me.kind==="cell"||me.kind===Or||me.kind===qn){let Te=!1,Mt=!0;if(me.kind==="cell"){const Ke=f(me.location);Mt=((at=ot(Ke))==null?void 0:at.needsHoverPosition)??Ke.kind===te.Custom,Te=Mt}else Te=!0;const Me=[me.location,[me.localEventX,me.localEventY]];Re(Me,Mt),Hn.current=Me,Te&&vn(new no([me.location]))}const rt=me.location[0]>=(re?0:1);Pe(me.kind===Or&&me.isEdge&&rt&&Oe===!0),Ea(me.kind==="cell"&&me.isFillHandle),k==null||k($),z(me)},[Q,$t,re,Oe,k,z,N,f,ot,vn]);gn("mousemove",Ii,Yt,!0);const yo=g.useCallback($=>{const ae=$e.current;if(ae===null)return;let Se,ke;R.current!==void 0&&(Se=Vt(ae,R.current.cell[0],R.current.cell[1]),ke=R.current.cell),G==null||G({bounds:Se,stopPropagation:()=>$.stopPropagation(),preventDefault:()=>$.preventDefault(),cancel:()=>{},ctrlKey:$.ctrlKey,metaKey:$.metaKey,shiftKey:$.shiftKey,altKey:$.altKey,key:$.key,keyCode:$.keyCode,rawEvent:$,location:ke})},[G,R,Vt]),Co=g.useCallback($=>{const ae=$e.current;if(ae===null)return;let Se,ke;R.current!==void 0&&(Se=Vt(ae,R.current.cell[0],R.current.cell[1]),ke=R.current.cell),j==null||j({bounds:Se,stopPropagation:()=>$.stopPropagation(),preventDefault:()=>$.preventDefault(),cancel:()=>{},ctrlKey:$.ctrlKey,metaKey:$.metaKey,shiftKey:$.shiftKey,altKey:$.altKey,key:$.key,keyCode:$.keyCode,rawEvent:$,location:ke})},[j,R,Vt]),Ta=g.useCallback($=>{if($e.current=$,oe!==void 0&&(oe.current=$),de!=null&&de.eventTarget)Ot.current=de.eventTarget;else if($===null)Ot.current=window;else{const ae=$.getRootNode();ae===document&&(Ot.current=window),Ot.current=ae}},[oe,de==null?void 0:de.eventTarget]),Da=g.useCallback($=>{const ae=$e.current;if(ae===null||ce===!1||H){$.preventDefault();return}let Se,ke;const me=$t(ae,$.clientX,$.clientY);if(ce!==!0&&me.kind!==ce){$.preventDefault();return}const Re=(Ke,qe)=>{Se=Ke,ke=qe};let rt,at,Te;const Mt=(Ke,qe,ht)=>{rt=Ke,at=qe,Te=ht};let Me=!1;if(ue==null||ue({...me,setData:Re,setDragImage:Mt,preventDefault:()=>Me=!0,defaultPrevented:()=>Me}),!Me&&Se!==void 0&&ke!==void 0&&$.dataTransfer!==null)if($.dataTransfer.setData(Se,ke),$.dataTransfer.effectAllowed="copyLink",rt!==void 0&&at!==void 0&&Te!==void 0)$.dataTransfer.setDragImage(rt,at,Te);else{const[Ke,qe]=me.location;if(qe!==void 0){const ht=document.createElement("canvas"),sn=Vt(ae,Ke,qe);Fn(sn!==void 0);const En=Math.ceil(window.devicePixelRatio??1);ht.width=sn.width*En,ht.height=sn.height*En;const St=ht.getContext("2d");St!==null&&(St.scale(En,En),St.textBaseline="middle",qe===-1?(St.font=xe.headerFontFull,St.fillStyle=xe.bgHeader,St.fillRect(0,0,ht.width,ht.height),Pd(St,0,0,sn.width,sn.height,Bt[Ke],!1,xe,!1,void 0,void 0,!1,0,Pt,tt,!1)):(St.font=xe.baseFontFull,St.fillStyle=xe.bgCell,St.fillRect(0,0,ht.width,ht.height),Td(St,f([Ke,qe]),0,qe,!1,!1,0,0,sn.width,sn.height,!1,xe,xe.bgCell,kt,Pt,1,void 0,!1,0,void 0,void 0,void 0,Ue,ot,()=>{}))),ht.style.left="-100%",ht.style.position="absolute",ht.style.width=`${sn.width}px`,ht.style.height=`${sn.height}px`,document.body.append(ht),$.dataTransfer.setDragImage(ht,sn.width/2,sn.height/2),window.setTimeout(()=>{ht.remove()},0)}}else $.preventDefault()},[ce,H,$t,ue,Vt,xe,Bt,Pt,tt,f,kt,Ue,ot]);gn("dragstart",Da,(Q==null?void 0:Q.current)??null,!1,!1);const Vn=g.useRef(),xr=g.useCallback($=>{const ae=$e.current;if(pe!==void 0&&$.preventDefault(),ae===null||ge===void 0)return;const Se=$t(ae,$.clientX,$.clientY),[ke,me]=Se.location,Re=ke-(re?0:1),[rt,at]=Vn.current??[];(rt!==Re||at!==me)&&(Vn.current=[Re,me],ge([Re,me],$.dataTransfer))},[re,$t,ge,pe]);gn("dragover",xr,(Q==null?void 0:Q.current)??null,!1,!1);const $n=g.useCallback(()=>{Vn.current=void 0,he==null||he()},[he]);gn("dragend",$n,(Q==null?void 0:Q.current)??null,!1,!1);const q=g.useCallback($=>{const ae=$e.current;if(ae===null||pe===void 0)return;$.preventDefault();const Se=$t(ae,$.clientX,$.clientY),[ke,me]=Se.location,Re=ke-(re?0:1);pe([Re,me],$.dataTransfer)},[re,$t,pe]);gn("drop",q,(Q==null?void 0:Q.current)??null,!1,!1);const Kt=g.useCallback(()=>{Ve==null||Ve()},[Ve]);gn("dragleave",Kt,(Q==null?void 0:Q.current)??null,!1,!1);const kr=g.useRef(R);kr.current=R;const ri=g.useRef(null),ii=g.useCallback($=>{var ae;$e.current===null||!$e.current.contains(document.activeElement)||($===null&&kr.current.current!==void 0?(ae=oe==null?void 0:oe.current)==null||ae.focus({preventScroll:!0}):$!==null&&$.focus({preventScroll:!0}),ri.current=$)},[oe]);g.useImperativeHandle(t,()=>({focus:()=>{var ae;const $=ri.current;$===null||!document.contains($)?(ae=oe==null?void 0:oe.current)==null||ae.focus({preventScroll:!0}):$.focus({preventScroll:!0})},getBounds:($,ae)=>{if(!(oe===void 0||oe.current===null))return Vt(oe.current,$??0,ae??-1)},damage:Xn}),[oe,Xn,Vt]);const Ti=g.useRef(),Oa=Zg(()=>{var at,Te,Mt;if(n<50||(de==null?void 0:de.disableAccessibilityTree)===!0)return null;let $=ks(Bt,gt,n,X,et);const ae=re?0:-1;!re&&((at=$[0])==null?void 0:at.sourceIndex)===0&&($=$.slice(1));const[Se,ke]=((Te=R.current)==null?void 0:Te.cell)??[],me=(Mt=R.current)==null?void 0:Mt.range,Re=$.map(Me=>Me.sourceIndex),rt=lr(l,Math.min(h,l+i));return Se!==void 0&&ke!==void 0&&!(Re.includes(Se)&&rt.includes(ke))&&ii(null),g.createElement("table",{key:"access-tree",role:"grid","aria-rowcount":h+1,"aria-multiselectable":"true","aria-colcount":Bt.length+ae},g.createElement("thead",{role:"rowgroup"},g.createElement("tr",{role:"row","aria-rowindex":1},$.map(Me=>g.createElement("th",{role:"columnheader","aria-selected":R.columns.hasIndex(Me.sourceIndex),"aria-colindex":Me.sourceIndex+1+ae,tabIndex:-1,onFocus:Ke=>{if(Ke.target!==ri.current)return ve==null?void 0:ve([Me.sourceIndex,-1])},key:Me.sourceIndex},Me.title)))),g.createElement("tbody",{role:"rowgroup"},rt.map(Me=>g.createElement("tr",{role:"row","aria-selected":R.rows.hasIndex(Me),key:Me,"aria-rowindex":Me+2},$.map(Ke=>{const qe=Ke.sourceIndex,ht=tr(qe,Me),sn=Se===qe&&ke===Me,En=me!==void 0&&qe>=me.x&&qe<me.x+me.width&&Me>=me.y&&Me<me.y+me.height,St=`glide-cell-${qe}-${Me}`,Jt=[qe,Me],jn=f(Jt,!0);return g.createElement("td",{key:ht,role:"gridcell","aria-colindex":qe+1+ae,"aria-selected":En,"aria-readonly":mi(jn)||!Zi(jn),id:St,"data-testid":St,onClick:()=>{const xn=oe==null?void 0:oe.current;if(xn!=null)return G==null?void 0:G({bounds:Vt(xn,qe,Me),cancel:()=>{},preventDefault:()=>{},stopPropagation:()=>{},ctrlKey:!1,key:"Enter",keyCode:13,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:Jt})},onFocusCapture:xn=>{var Et,In;if(!(xn.target===ri.current||((Et=Ti.current)==null?void 0:Et[0])===qe&&((In=Ti.current)==null?void 0:In[1])===Me))return Ti.current=Jt,ve==null?void 0:ve(Jt)},ref:sn?ii:void 0,tabIndex:-1},s0(jn,ot))})))))},[n,Bt,gt,X,et,h,l,i,R,ii,f,oe,G,Vt,ve],200),Di=_===0||!L?0:gt>_?1:Un(-et/100,0,1),K=-l*32+bt,un=D?Un(-K/100,0,1):0,cn=g.useMemo(()=>{if(!Di&&!un)return null;const $={position:"absolute",top:0,left:_t,width:n-_t,height:r,opacity:Di,pointerEvents:"none",transition:nt?void 0:"opacity 0.2s",boxShadow:"inset 13px 0 10px -13px rgba(0, 0, 0, 0.2)"},ae={position:"absolute",top:rn,left:0,width:n,height:r,opacity:un,pointerEvents:"none",transition:be?void 0:"opacity 0.2s",boxShadow:"inset 0 13px 10px -13px rgba(0, 0, 0, 0.2)"};return g.createElement(g.Fragment,null,Di>0&&g.createElement("div",{id:"shadow-x",style:$}),un>0&&g.createElement("div",{id:"shadow-y",style:ae}))},[Di,un,_t,n,nt,rn,r,be]),Pa=g.useMemo(()=>({position:"absolute",top:0,left:0}),[]);return g.createElement(g.Fragment,null,g.createElement("canvas",{"data-testid":"data-grid-canvas",tabIndex:0,onKeyDown:yo,onKeyUp:Co,onFocus:M,onBlur:O,ref:Ta,style:Mi},Oa),g.createElement("canvas",{ref:Ft,style:Pa}),cn)},u0=g.memo(g.forwardRef(l0));function Xi(e,t,n,r){return Un(Math.round(t-(e.growOffset??0)),Math.ceil(n),Math.floor(r))}const c0=e=>{const[t,n]=g.useState(),[r,i]=g.useState(),[o,a]=g.useState(),[l,s]=g.useState(),[u,c]=g.useState(!1),[d,h]=g.useState(),[f,m]=g.useState(),[p,v]=g.useState(),[w,b]=g.useState(!1),[M,O]=g.useState(),{onHeaderMenuClick:S,onHeaderIndicatorClick:R,getCellContent:_,onColumnMoved:E,onColumnResize:x,onColumnResizeStart:L,onColumnResizeEnd:D,gridRef:C,maxColumnWidth:I,minColumnWidth:T,onRowMoved:k,lockColumns:z,onColumnProposeMove:N,onMouseDown:X,onMouseUp:re,onItemHovered:G,onDragStart:j,canvasRef:se}=e,oe=(x??D??L)!==void 0,{columns:ue,selection:he}=e,Q=he.columns,H=g.useCallback(le=>{const[tt,ve]=le.location;o!==void 0&&l!==tt&&tt>=z?(c(!0),s(tt)):f!==void 0&&ve!==void 0?(b(!0),v(Math.max(0,ve))):r===void 0&&!u&&!w&&(G==null||G(le))},[o,f,l,G,z,r,u,w]),P=E!==void 0,W=g.useCallback(le=>{var tt;if(le.button===0){const[ve,ge]=le.location;if(le.kind==="out-of-bounds"&&le.isEdge&&oe){const pe=(tt=C==null?void 0:C.current)==null?void 0:tt.getBounds(ue.length-1,-1);pe!==void 0&&(n(pe.x),i(ue.length-1))}else if(le.kind==="header"&&ve>=z){const pe=se==null?void 0:se.current;if(le.isEdge&&oe&&pe){n(le.bounds.x),i(ve);const _e=pe.getBoundingClientRect().width/pe.offsetWidth,nt=le.bounds.width/_e;L==null||L(ue[ve],nt,ve,nt+(ue[ve].growOffset??0))}else le.kind==="header"&&P&&(h(le.bounds.x),a(ve))}else le.kind==="cell"&&z>0&&ve===0&&ge!==void 0&&k!==void 0&&(O(le.bounds.y),m(ge))}X==null||X(le)},[X,oe,z,k,C,ue,P,L,se]),ce=g.useCallback((le,tt)=>{u||w||S==null||S(le,tt)},[u,w,S]),Oe=g.useCallback((le,tt)=>{u||w||R==null||R(le,tt)},[u,w,R]),ze=g.useRef(-1),Ce=g.useCallback(()=>{ze.current=-1,m(void 0),v(void 0),O(void 0),b(!1),a(void 0),s(void 0),h(void 0),c(!1),i(void 0),n(void 0)},[]),Ee=g.useCallback((le,tt)=>{if(le.button===0){if(r!==void 0){if((Q==null?void 0:Q.hasIndex(r))===!0)for(const ge of Q){if(ge===r)continue;const pe=ue[ge],Ve=Xi(pe,ze.current,T,I);x==null||x(pe,Ve,ge,Ve+(pe.growOffset??0))}const ve=Xi(ue[r],ze.current,T,I);if(D==null||D(ue[r],ve,r,ve+(ue[r].growOffset??0)),Q.hasIndex(r))for(const ge of Q){if(ge===r)continue;const pe=ue[ge],Ve=Xi(pe,ze.current,T,I);D==null||D(pe,Ve,ge,Ve+(pe.growOffset??0))}}Ce(),o!==void 0&&l!==void 0&&(E==null||E(o,l)),f!==void 0&&p!==void 0&&(k==null||k(f,p))}re==null||re(le,tt)},[re,r,o,l,f,p,Q,D,ue,T,I,x,E,k,Ce]),xe=g.useMemo(()=>{if(!(o===void 0||l===void 0)&&o!==l&&(N==null?void 0:N(o,l))!==!1)return{src:o,dest:l}},[o,l,N]),It=g.useCallback(le=>{const tt=se==null?void 0:se.current;if(o!==void 0&&d!==void 0)Math.abs(le.clientX-d)>20&&c(!0);else if(f!==void 0&&M!==void 0)Math.abs(le.clientY-M)>20&&b(!0);else if(r!==void 0&&t!==void 0&&tt){const ge=tt.getBoundingClientRect().width/tt.offsetWidth,pe=(le.clientX-t)/ge,Ve=ue[r],_e=Xi(Ve,pe,T,I);if(x==null||x(Ve,_e,r,_e+(Ve.growOffset??0)),ze.current=pe,(Q==null?void 0:Q.first())===r)for(const nt of Q){if(nt===r)continue;const be=ue[nt],de=Xi(be,ze.current,T,I);x==null||x(be,de,nt,de+(be.growOffset??0))}}},[o,d,f,M,r,t,ue,T,I,x,Q,se]),yt=g.useCallback((le,tt)=>{if(f===void 0||p===void 0)return _(le,tt);let[ve,ge]=le;return ge===p?ge=f:(ge>p&&(ge-=1),ge>=f&&(ge+=1)),_([ve,ge],tt)},[f,p,_]),it=g.useCallback(le=>{j==null||j(le),le.defaultPrevented()||Ce()},[Ce,j]);return g.createElement(u0,{accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,enableGroups:e.enableGroups,eventTargetRef:e.eventTargetRef,experimental:e.experimental,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,headerIcons:e.headerIcons,height:e.height,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,resizeColumn:r,isDraggable:e.isDraggable,isFilling:e.isFilling,isFocused:e.isFocused,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDrop:e.onDrop,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,resizeIndicator:e.resizeIndicator,verticalBorder:e.verticalBorder,width:e.width,getCellContent:yt,isResizing:r!==void 0,onHeaderMenuClick:ce,onHeaderIndicatorClick:Oe,isDragging:u,onItemHovered:H,onDragStart:it,onMouseDown:W,allowResize:oe,onMouseUp:Ee,dragAndDropState:xe,onMouseMoveRaw:It,ref:C})};function d0(e){const t=g.useRef(null),[n,r]=g.useState({width:e==null?void 0:e[0],height:e==null?void 0:e[1]});return g.useLayoutEffect(()=>{const i=a=>{for(const l of a){const{width:s,height:u}=l&&l.contentRect||{};r(c=>c.width===s&&c.height===u?c:{width:s,height:u})}},o=new window.ResizeObserver(i);return t.current&&o.observe(t.current,void 0),()=>{o.disconnect()}},[t.current]),{ref:t,...n}}const f0=(e,t,n)=>{const r=g.useRef(null),i=g.useRef(null),o=g.useRef(null),a=g.useRef(0),l=g.useRef(t);l.current=t;const s=n.current;g.useEffect(()=>{const u=()=>{var h,f;if(i.current===!1&&s!==null){const m=[s.scrollLeft,s.scrollTop];if(((h=o.current)==null?void 0:h[0])===m[0]&&((f=o.current)==null?void 0:f[1])===m[1])if(a.current>10){o.current=null,i.current=null;return}else a.current++;else a.current=0,l.current(m[0],m[1]),o.current=m;r.current=window.setTimeout(u,8.333333333333334)}},c=()=>{i.current=!0,o.current=null,r.current!==null&&(window.clearTimeout(r.current),r.current=null)},d=h=>{h.touches.length===0&&(i.current=!1,a.current=0,r.current=window.setTimeout(u,8.333333333333334))};if(e&&s!==null){const h=s;return h.addEventListener("touchstart",c),h.addEventListener("touchend",d),()=>{h.removeEventListener("touchstart",c),h.removeEventListener("touchend",d),r.current!==null&&window.clearTimeout(r.current)}}},[e,s])},h0=()=>e=>e.isSafari?"scroll":"auto",g0=mn("div")({name:"ScrollRegionStyle",class:"gdg-s1dgczr6",propsAsIs:!1,vars:{"s1dgczr6-0":[h0()]}});function m0(e){const[t,n]=g.useState(!1),r=typeof window>"u"?null:window,i=g.useRef(0);return gn("touchstart",g.useCallback(()=>{window.clearTimeout(i.current),n(!0)},[]),r,!0,!1),gn("touchend",g.useCallback(o=>{o.touches.length===0&&(i.current=window.setTimeout(()=>n(!1),e))},[e]),r,!0,!1),t}const p0=e=>{var oe,ue;const{children:t,clientHeight:n,scrollHeight:r,scrollWidth:i,update:o,draggable:a,className:l,preventDiagonalScrolling:s=!1,paddingBottom:u=0,paddingRight:c=0,rightElement:d,rightElementProps:h,kineticScrollPerfHack:f=!1,scrollRef:m,initialSize:p}=e,v=[],w=(h==null?void 0:h.sticky)??!1,b=(h==null?void 0:h.fill)??!1,M=g.useRef(0),O=g.useRef(0),S=g.useRef(null),R=typeof window>"u"?1:window.devicePixelRatio,_=g.useRef({scrollLeft:0,scrollTop:0,lockDirection:void 0}),E=g.useRef(null),x=m0(200),[L,D]=g.useState(!0),C=g.useRef(0);g.useLayoutEffect(()=>{if(!L||x||_.current.lockDirection===void 0)return;const he=S.current;if(he===null)return;const[Q,H]=_.current.lockDirection;Q!==void 0?he.scrollLeft=Q:H!==void 0&&(he.scrollTop=H),_.current.lockDirection=void 0},[x,L]);const I=g.useCallback((he,Q)=>{var it;const H=S.current;if(H===null)return;Q=Q??H.scrollTop,he=he??H.scrollLeft;const P=_.current.scrollTop,W=_.current.scrollLeft,ce=he-W,Oe=Q-P;x&&ce!==0&&Oe!==0&&(Math.abs(ce)>3||Math.abs(Oe)>3)&&s&&_.current.lockDirection===void 0&&(_.current.lockDirection=Math.abs(ce)<Math.abs(Oe)?[W,void 0]:[void 0,P]);const ze=_.current.lockDirection;he=(ze==null?void 0:ze[0])??he,Q=(ze==null?void 0:ze[1])??Q,_.current.scrollLeft=he,_.current.scrollTop=Q;const Ce=H.clientWidth,Ee=H.clientHeight,xe=Q,It=O.current-xe,yt=H.scrollHeight-Ee;if(O.current=xe,yt>0&&(Math.abs(It)>2e3||xe===0||xe===yt)&&r>H.scrollHeight+5){const le=xe/yt,tt=(r-Ee)*le;M.current=tt-xe}ze!==void 0&&(window.clearTimeout(C.current),D(!1),C.current=window.setTimeout(()=>D(!0),200)),o({x:he,y:xe+M.current,width:Ce-c,height:Ee-u,paddingRight:((it=E.current)==null?void 0:it.clientWidth)??0})},[u,c,r,o,s,x]);f0(f&&la.value,I,S);const T=g.useRef(I);T.current=I;const k=g.useRef(),z=g.useRef(!1);g.useLayoutEffect(()=>{z.current?I():z.current=!0},[I,u,c]);const N=g.useCallback(he=>{S.current=he,m!==void 0&&(m.current=he)},[m]);let X=0,re=0;for(v.push(g.createElement("div",{key:X++,style:{width:i,height:0}}));re<r;){const he=Math.min(5e6,r-re);v.push(g.createElement("div",{key:X++,style:{width:0,height:he}})),re+=he}const{ref:G,width:j,height:se}=d0(p);return typeof window<"u"&&(((oe=k.current)==null?void 0:oe.height)!==se||((ue=k.current)==null?void 0:ue.width)!==j)&&(window.setTimeout(()=>T.current(),0),k.current={width:j,height:se}),(j??0)===0||(se??0)===0?g.createElement("div",{ref:G}):g.createElement("div",{ref:G},g.createElement(g0,{isSafari:la.value},g.createElement("div",{className:"dvn-underlay"},t),g.createElement("div",{ref:N,style:k.current,draggable:a,onDragStart:he=>{a||(he.stopPropagation(),he.preventDefault())},className:"dvn-scroller "+(l??""),onScroll:()=>I()},g.createElement("div",{className:"dvn-scroll-inner"+(d===void 0?" dvn-hidden":"")},g.createElement("div",{className:"dvn-stack"},v),d!==void 0&&g.createElement(g.Fragment,null,!b&&g.createElement("div",{className:"dvn-spacer"}),g.createElement("div",{ref:E,style:{height:se,maxHeight:n-Math.ceil(R%1),position:"sticky",top:0,paddingLeft:1,marginBottom:-40,marginRight:c,flexGrow:b?1:void 0,right:w?c??0:void 0,pointerEvents:"auto"}},d))))))},v0=e=>{const{columns:t,rows:n,rowHeight:r,headerHeight:i,groupHeaderHeight:o,enableGroups:a,freezeColumns:l,experimental:s,nonGrowWidth:u,clientSize:c,className:d,onVisibleRegionChanged:h,scrollRef:f,preventDiagonalScrolling:m,rightElement:p,rightElementProps:v,overscrollX:w,overscrollY:b,initialSize:M,smoothScrollX:O=!1,smoothScrollY:S=!1,isDraggable:R}=e,{paddingRight:_,paddingBottom:E}=s??{},[x,L]=c,D=g.useRef(),C=g.useRef(),I=g.useRef(),T=g.useRef(),k=u+Math.max(0,w??0);let z=a?i+o:i;if(typeof r=="number")z+=n*r;else for(let G=0;G<n;G++)z+=r(G);b!==void 0&&(z+=b);const N=g.useRef(),X=g.useCallback(()=>{var Oe,ze;if(N.current===void 0)return;const G={...N.current};let j=0,se=G.x<0?-G.x:0,oe=0,ue=0;G.x=G.x<0?0:G.x;let he=0;for(let Ce=0;Ce<l;Ce++)he+=t[Ce].width;for(const Ce of t){const Ee=j-he;if(G.x>=Ee+Ce.width)j+=Ce.width,ue++,oe++;else if(G.x>Ee)j+=Ce.width,O?se+=Ee-G.x:ue++,oe++;else if(G.x+G.width>Ee)j+=Ce.width,oe++;else break}let Q=0,H=0,P=0;if(typeof r=="number")S?(H=Math.floor(G.y/r),Q=H*r-G.y):H=Math.ceil(G.y/r),P=Math.ceil(G.height/r)+H,Q<0&&P++;else{let Ce=0;for(let Ee=0;Ee<n;Ee++){const xe=r(Ee),It=Ce+(S?0:xe/2);if(G.y>=Ce+xe)Ce+=xe,H++,P++;else if(G.y>It)Ce+=xe,S?Q+=It-G.y:H++,P++;else if(G.y+G.height>xe/2+Ce)Ce+=xe,P++;else break}}const W={x:ue,y:H,width:oe-ue,height:P-H},ce=D.current;(ce===void 0||ce.y!==W.y||ce.x!==W.x||ce.height!==W.height||ce.width!==W.width||C.current!==se||I.current!==Q||G.width!==((Oe=T.current)==null?void 0:Oe[0])||G.height!==((ze=T.current)==null?void 0:ze[1]))&&(h==null||h({x:ue,y:H,width:oe-ue,height:P-H},G.width,G.height,G.paddingRight??0,se,Q),D.current=W,C.current=se,I.current=Q,T.current=[G.width,G.height])},[t,r,n,h,l,O,S]),re=g.useCallback(G=>{N.current=G,X()},[X]);return g.useEffect(()=>{X()},[X]),g.createElement(p0,{scrollRef:f,className:d,kineticScrollPerfHack:s==null?void 0:s.kineticScrollPerfHack,preventDiagonalScrolling:m,draggable:R===!0||typeof R=="string",scrollWidth:k+(_??0),scrollHeight:z+(E??0),clientHeight:L,rightElement:p,paddingBottom:E,paddingRight:_,rightElementProps:v,update:re,initialSize:M},g.createElement(c0,{eventTargetRef:f,width:x,height:L,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,prelightCells:e.prelightCells,rowHeight:e.rowHeight,rows:e.rows,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,onColumnProposeMove:e.onColumnProposeMove,verticalBorder:e.verticalBorder,drawFocusRing:e.drawFocusRing,drawHeader:e.drawHeader,drawCell:e.drawCell,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}))},b0=mn("div")({name:"SearchWrapper",class:"gdg-seveqep",propsAsIs:!1}),w0=g.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},g.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 244l144-144 144 144M256 120v292"})),y0=g.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},g.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 268l144 144 144-144M256 392V100"})),C0=g.createElement("svg",{className:"button-icon",viewBox:"0 0 512 512"},g.createElement("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M368 368L144 144M368 144L144 368"})),S0=10,x0=e=>{const{canvasRef:t,cellYOffset:n,rows:r,columns:i,searchInputRef:o,searchValue:a,searchResults:l,onSearchValueChange:s,getCellsForSelection:u,onSearchResultsChanged:c,showSearch:d=!1,onSearchClose:h}=e,[f]=g.useState(()=>"search-box-"+Math.round(Math.random()*1e3)),[m,p]=g.useState(""),v=a??m,w=g.useCallback(j=>{p(j),s==null||s(j)},[s]),[b,M]=g.useState(),O=g.useRef(b);O.current=b,g.useEffect(()=>{l!==void 0&&(l.length>0?M(j=>({rowsSearched:r,results:l.length,selectedIndex:(j==null?void 0:j.selectedIndex)??-1})):M(void 0))},[r,l]);const S=g.useRef();S.current===void 0&&(S.current=new AbortController);const R=g.useRef(),[_,E]=g.useState([]),x=l??_,L=g.useCallback(()=>{R.current!==void 0&&(window.cancelAnimationFrame(R.current),R.current=void 0,S.current.abort())},[]),D=g.useRef(n);D.current=n;const C=g.useCallback(j=>{const se=new RegExp(j.replace(/([$()*+.?[\\\]^{|}-])/g,"\\$1"),"i");let oe=D.current,ue=Math.min(10,r),he=0;M(void 0),E([]);const Q=[],H=async()=>{var yt;if(u===void 0)return;const P=performance.now(),W=r-he;let ce=u({x:0,y:oe,width:i.length,height:Math.min(ue,W,r-oe)},S.current.signal);typeof ce=="function"&&(ce=await ce());let Oe=!1;for(const[it,le]of ce.entries())for(const[tt,ve]of le.entries()){let ge;switch(ve.kind){case te.Text:case te.Number:ge=ve.displayData;break;case te.Uri:case te.Markdown:ge=ve.data;break;case te.Boolean:ge=typeof ve.data=="boolean"?ve.data.toString():void 0;break;case te.Image:case te.Bubble:ge=ve.data.join("🐳");break;case te.Custom:ge=ve.copyData;break}ge!==void 0&&se.test(ge)&&(Q.push([tt,it+oe]),Oe=!0)}const ze=performance.now();Oe&&E([...Q]),he+=ce.length,Fn(he<=r);const Ce=((yt=O.current)==null?void 0:yt.selectedIndex)??-1;M({results:Q.length,rowsSearched:he,selectedIndex:Ce}),c==null||c(Q,Ce),oe+ue>=r?oe=0:oe+=ue;const Ee=ze-P,xe=Math.max(Ee,1),It=S0/xe;ue=Math.ceil(ue*It),he<r&&Q.length<1e3&&(R.current=window.requestAnimationFrame(H))};L(),R.current=window.requestAnimationFrame(H)},[L,i.length,u,c,r]),I=g.useCallback(()=>{var j;h==null||h(),M(void 0),E([]),c==null||c([],-1),L(),(j=t==null?void 0:t.current)==null||j.focus()},[L,t,h,c]),T=g.useCallback(j=>{w(j.target.value),l===void 0&&(j.target.value===""?(M(void 0),E([]),L()):C(j.target.value))},[C,L,w,l]);g.useEffect(()=>{d&&o.current!==null&&(w(""),o.current.focus({preventScroll:!0}))},[d,o,w]);const k=g.useCallback(j=>{var oe;if((oe=j==null?void 0:j.stopPropagation)==null||oe.call(j),b===void 0)return;const se=(b.selectedIndex+1)%b.results;M({...b,selectedIndex:se}),c==null||c(x,se)},[b,c,x]),z=g.useCallback(j=>{var oe;if((oe=j==null?void 0:j.stopPropagation)==null||oe.call(j),b===void 0)return;let se=(b.selectedIndex-1)%b.results;se<0&&(se+=b.results),M({...b,selectedIndex:se}),c==null||c(x,se)},[c,x,b]),N=g.useCallback(j=>{(j.ctrlKey||j.metaKey)&&j.nativeEvent.code==="KeyF"||j.key==="Escape"?(I(),j.stopPropagation(),j.preventDefault()):j.key==="Enter"&&(j.shiftKey?z():k())},[I,k,z]);g.useEffect(()=>()=>{L()},[L]);const[X,re]=g.useState(!1);g.useEffect(()=>{if(d)re(!0);else{const j=setTimeout(()=>re(!1),150);return()=>clearTimeout(j)}},[d]);const G=g.useMemo(()=>{if(!d&&!X)return null;let j;b!==void 0&&(j=b.results>=1e3?"over 1000":`${b.results} result${b.results!==1?"s":""}`,b.selectedIndex>=0&&(j=`${b.selectedIndex+1} of ${j}`));const se=he=>{he.stopPropagation()},ue={width:`${Math.floor(((b==null?void 0:b.rowsSearched)??0)/r*100)}%`};return g.createElement(b0,{className:d?"":"out",onMouseDown:se,onMouseMove:se,onMouseUp:se,onClick:se},g.createElement("div",{className:"gdg-search-bar-inner"},g.createElement("input",{id:f,"aria-hidden":!d,"data-testid":"search-input",ref:o,onChange:T,value:v,tabIndex:d?void 0:-1,onKeyDownCapture:N}),g.createElement("button",{"aria-label":"Previous Result","aria-hidden":!d,tabIndex:d?void 0:-1,onClick:z,disabled:((b==null?void 0:b.results)??0)===0},w0),g.createElement("button",{"aria-label":"Next Result","aria-hidden":!d,tabIndex:d?void 0:-1,onClick:k,disabled:((b==null?void 0:b.results)??0)===0},y0),h!==void 0&&g.createElement("button",{"aria-label":"Close Search","aria-hidden":!d,"data-testid":"search-close-button",tabIndex:d?void 0:-1,onClick:I},C0)),b!==void 0?g.createElement(g.Fragment,null,g.createElement("div",{className:"gdg-search-status"},g.createElement("div",{"data-testid":"search-result-area"},j)),g.createElement("div",{className:"gdg-search-progress",style:ue})):g.createElement("div",{className:"gdg-search-status"},g.createElement("label",{htmlFor:f},"Type to search")))},[d,X,b,r,f,o,T,v,N,z,k,h,I]);return g.createElement(g.Fragment,null,g.createElement(v0,{prelightCells:x,accessibilityHeight:e.accessibilityHeight,canvasRef:e.canvasRef,cellXOffset:e.cellXOffset,cellYOffset:e.cellYOffset,className:e.className,clientSize:e.clientSize,columns:e.columns,disabledRows:e.disabledRows,enableGroups:e.enableGroups,fillHandle:e.fillHandle,firstColAccessible:e.firstColAccessible,nonGrowWidth:e.nonGrowWidth,fixedShadowX:e.fixedShadowX,fixedShadowY:e.fixedShadowY,freezeColumns:e.freezeColumns,getCellContent:e.getCellContent,getCellRenderer:e.getCellRenderer,getGroupDetails:e.getGroupDetails,getRowThemeOverride:e.getRowThemeOverride,groupHeaderHeight:e.groupHeaderHeight,headerHeight:e.headerHeight,highlightRegions:e.highlightRegions,imageWindowLoader:e.imageWindowLoader,initialSize:e.initialSize,isFilling:e.isFilling,isFocused:e.isFocused,lockColumns:e.lockColumns,maxColumnWidth:e.maxColumnWidth,minColumnWidth:e.minColumnWidth,onHeaderMenuClick:e.onHeaderMenuClick,onHeaderIndicatorClick:e.onHeaderIndicatorClick,onMouseMove:e.onMouseMove,onVisibleRegionChanged:e.onVisibleRegionChanged,overscrollX:e.overscrollX,overscrollY:e.overscrollY,preventDiagonalScrolling:e.preventDiagonalScrolling,rightElement:e.rightElement,rightElementProps:e.rightElementProps,rowHeight:e.rowHeight,rows:e.rows,scrollRef:e.scrollRef,selection:e.selection,theme:e.theme,freezeTrailingRows:e.freezeTrailingRows,hasAppendRow:e.hasAppendRow,translateX:e.translateX,translateY:e.translateY,verticalBorder:e.verticalBorder,onColumnProposeMove:e.onColumnProposeMove,drawFocusRing:e.drawFocusRing,drawCell:e.drawCell,drawHeader:e.drawHeader,experimental:e.experimental,gridRef:e.gridRef,headerIcons:e.headerIcons,isDraggable:e.isDraggable,onCanvasBlur:e.onCanvasBlur,onCanvasFocused:e.onCanvasFocused,onCellFocused:e.onCellFocused,onColumnMoved:e.onColumnMoved,onColumnResize:e.onColumnResize,onColumnResizeEnd:e.onColumnResizeEnd,onColumnResizeStart:e.onColumnResizeStart,onContextMenu:e.onContextMenu,onDragEnd:e.onDragEnd,onDragLeave:e.onDragLeave,onDragOverCell:e.onDragOverCell,onDragStart:e.onDragStart,onDrop:e.onDrop,onItemHovered:e.onItemHovered,onKeyDown:e.onKeyDown,onKeyUp:e.onKeyUp,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onRowMoved:e.onRowMoved,smoothScrollX:e.smoothScrollX,smoothScrollY:e.smoothScrollY,resizeIndicator:e.resizeIndicator}),G)};class k0 extends g.PureComponent{constructor(){super(...arguments);ft(this,"wrapperRef",g.createRef());ft(this,"clickOutside",n=>{if(!(this.props.isOutsideClick&&!this.props.isOutsideClick(n))&&this.wrapperRef.current!==null&&!this.wrapperRef.current.contains(n.target)){let r=n.target;for(;r!==null;){if(r.classList.contains("click-outside-ignore"))return;r=r.parentElement}this.props.onClickOutside()}})}componentDidMount(){const n=this.props.customEventTarget??document;n.addEventListener("touchend",this.clickOutside,!0),n.addEventListener("mousedown",this.clickOutside,!0),n.addEventListener("contextmenu",this.clickOutside,!0)}componentWillUnmount(){const n=this.props.customEventTarget??document;n.removeEventListener("touchend",this.clickOutside,!0),n.removeEventListener("mousedown",this.clickOutside,!0),n.removeEventListener("contextmenu",this.clickOutside,!0)}render(){const{onClickOutside:n,isOutsideClick:r,customEventTarget:i,...o}=this.props;return g.createElement("div",{...o,ref:this.wrapperRef},this.props.children)}}const M0=()=>e=>Math.max(16,e.targetHeight-10),R0=mn("input")({name:"RenameInput",class:"gdg-r17m35ur",propsAsIs:!1,vars:{"r17m35ur-0":[M0(),"px"]}}),E0=e=>{const{bounds:t,group:n,onClose:r,canvasBounds:i,onFinish:o}=e,[a,l]=ie.useState(n);return ie.createElement(k0,{style:{position:"absolute",left:t.x-i.left+1,top:t.y-i.top,width:t.width-2,height:t.height},className:"gdg-c1tqibwd",onClickOutside:r},ie.createElement(R0,{targetHeight:t.height,"data-testid":"group-rename-input",value:a,onBlur:r,onFocus:s=>s.target.setSelectionRange(0,a.length),onChange:s=>l(s.target.value),onKeyDown:s=>{s.key==="Enter"?o(a):s.key==="Escape"&&r()},autoFocus:!0}))};function I0(e,t){return e===void 0?!1:e.length>1&&e.startsWith("_")?Number.parseInt(e.slice(1))===t.keyCode:e.length===1&&e>="a"&&e<="z"?e.toUpperCase().codePointAt(0)===t.keyCode:e===t.key}function ct(e,t,n){const r=Fd(e,t);return r&&(n.didMatch=!0),r}function Fd(e,t){if(e.length===0)return!1;if(e.includes("|")){const s=e.split("|");for(const u of s)if(Fd(u,t))return!0;return!1}let n=!1,r=!1,i=!1,o=!1;const a=e.split("+"),l=a.pop();if(!I0(l,t))return!1;if(a[0]==="any")return!0;for(const s of a)switch(s){case"ctrl":n=!0;break;case"shift":r=!0;break;case"alt":i=!0;break;case"meta":o=!0;break;case"primary":ua.value?o=!0:n=!0;break}return t.altKey===i&&t.ctrlKey===n&&t.shiftKey===r&&t.metaKey===o}function T0(e,t,n,r,i,o,a){const l=ie.useCallback((c,d,h,f)=>{var M;(o==="cell"||o==="multi-cell")&&c!==void 0&&(c={...c,range:{x:c.cell[0],y:c.cell[1],width:1,height:1}}),!a&&c!==void 0&&c.range.width>1&&(c={...c,range:{...c.range,width:1,x:c.cell[0]}});const m=n==="mixed"&&(h||f==="drag"),p=r==="mixed"&&m,v=i==="mixed"&&m;let w={current:c===void 0?void 0:{...c,rangeStack:f==="drag"?((M=e.current)==null?void 0:M.rangeStack)??[]:[]},columns:p?e.columns:pt.empty(),rows:v?e.rows:pt.empty()};h&&(o==="multi-rect"||o==="multi-cell")&&w.current!==void 0&&e.current!==void 0&&(w={...w,current:{...w.current,rangeStack:[...e.current.rangeStack,e.current.range]}}),t(w,d)},[r,e,n,o,a,i,t]),s=ie.useCallback((c,d,h)=>{c=c??e.rows,d!==void 0&&(c=c.add(d));let f;if(i==="exclusive"&&c.length>0)f={current:void 0,columns:pt.empty(),rows:c};else{const m=h&&n==="mixed",p=h&&r==="mixed";f={current:m?e.current:void 0,columns:p?e.columns:pt.empty(),rows:c}}t(f,!1)},[r,e,n,i,t]),u=ie.useCallback((c,d,h)=>{c=c??e.columns,d!==void 0&&(c=c.add(d));let f;if(r==="exclusive"&&c.length>0)f={current:void 0,rows:pt.empty(),columns:c};else{const m=h&&n==="mixed",p=h&&i==="mixed";f={current:m?e.current:void 0,rows:p?e.rows:pt.empty(),columns:c}}t(f,!1)},[r,e,n,i,t]);return[l,s,u]}function D0(e,t,n,r,i){const o=g.useCallback(u=>{if(e===!0){const c=[];for(let d=u.y;d<u.y+u.height;d++){const h=[];for(let f=u.x;f<u.x+u.width;f++)f<0||d>=i?h.push({kind:te.Loading,allowOverlay:!1}):h.push(t([f,d]));c.push(h)}return c}return(e==null?void 0:e(u,r.signal))??[]},[r.signal,t,e,i]),a=e!==void 0?o:void 0,l=g.useCallback(u=>{if(a===void 0)return[];const c={...u,x:u.x-n};if(c.x<0){c.x=0,c.width--;const d=a(c,r.signal);return typeof d=="function"?async()=>(await d()).map(h=>[{kind:te.Loading,allowOverlay:!1},...h]):d.map(h=>[{kind:te.Loading,allowOverlay:!1},...h])}return a(c,r.signal)},[r.signal,a,n]);return[e!==void 0?l:void 0,a]}function O0(e){if(e.copyData!==void 0)return{formatted:e.copyData,rawValue:e.copyData,format:"string"};switch(e.kind){case te.Boolean:return{formatted:e.data===!0?"TRUE":e.data===!1?"FALSE":e.data===Hs?"INDETERMINATE":"",rawValue:e.data,format:"boolean"};case te.Custom:return{formatted:e.copyData,rawValue:e.copyData,format:"string"};case te.Image:case te.Bubble:return{formatted:e.data,rawValue:e.data,format:"string-array"};case te.Drilldown:return{formatted:e.data.map(t=>t.text),rawValue:e.data.map(t=>t.text),format:"string-array"};case te.Text:return{formatted:e.displayData??e.data,rawValue:e.data,format:"string"};case te.Uri:return{formatted:e.displayData??e.data,rawValue:e.data,format:"url"};case te.Markdown:case te.RowID:return{formatted:e.data,rawValue:e.data,format:"string"};case te.Number:return{formatted:e.displayData,rawValue:e.data,format:"number"};case te.Loading:return{formatted:"#LOADING",rawValue:"",format:"string"};case te.Protected:return{formatted:"************",rawValue:"",format:"string"};default:io()}}function P0(e,t){return e.map((r,i)=>{const o=t[i];return r.map(a=>a.span!==void 0&&a.span[0]!==o?{formatted:"",rawValue:"",format:"string"}:O0(a))})}function Eu(e,t){return(t?/[\t\n",]/:/[\t\n"]/).test(e)&&(e=`"${e.replace(/"/g,'""')}"`),e}function _0(e){var n;const t=[];for(const r of e){const i=[];for(const o of r)o.format==="url"?i.push(((n=o.rawValue)==null?void 0:n.toString())??""):o.format==="string-array"?i.push(o.formatted.map(a=>Eu(a,!0)).join(",")):i.push(Eu(o.formatted,!1));t.push(i.join("	"))}return t.join(`
`)}function ls(e){return e.replace(/\t/g,"    ").replace(/ {2,}/g,t=>"<span> </span>".repeat(t.length))}function Iu(e){return'"'+e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")+'"'}function L0(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")}function F0(e){var n;const t=[];t.push('<style type="text/css"><!--br {mso-data-placement:same-cell;}--></style>',"<table><tbody>");for(const r of e){t.push("<tr>");for(const i of r){const o=`gdg-format="${i.format}"`;i.format==="url"?t.push(`<td ${o}><a href="${i.rawValue}">${ls(i.formatted)}</a></td>`):i.format==="string-array"?t.push(`<td ${o}><ol>${i.formatted.map((a,l)=>`<li gdg-raw-value=${Iu(i.rawValue[l])}>`+ls(a)+"</li>").join("")}</ol></td>`):t.push(`<td gdg-raw-value=${Iu(((n=i.rawValue)==null?void 0:n.toString())??"")} ${o}>${ls(i.formatted)}</td>`)}t.push("</tr>")}return t.push("</tbody></table>"),t.join("")}function A0(e,t){const n=P0(e,t),r=_0(n),i=F0(n);return{textPlain:r,textHtml:i}}function Tu(e){var a;const t=document.createElement("html");t.innerHTML=e.replace(/&nbsp;/g," ");const n=t.querySelector("table");if(n===null)return;const r=[n],i=[];let o;for(;r.length>0;){const l=r.pop();if(l===void 0)break;if(l instanceof HTMLTableElement||l.nodeName==="TBODY")r.push(...[...l.children].reverse());else if(l instanceof HTMLTableRowElement)o!==void 0&&i.push(o),o=[],r.push(...[...l.children].reverse());else if(l instanceof HTMLTableCellElement){const s=l.cloneNode(!0),c=s.children.length===1&&s.children[0].nodeName==="P"?s.children[0]:null,d=(c==null?void 0:c.children.length)===1&&c.children[0].nodeName==="FONT",h=s.querySelectorAll("br");for(const p of h)p.replaceWith(`
`);const f=s.getAttribute("gdg-raw-value"),m=s.getAttribute("gdg-format")??"string";if(s.querySelector("a")!==null)o==null||o.push({rawValue:((a=s.querySelector("a"))==null?void 0:a.getAttribute("href"))??"",formatted:s.textContent??"",format:m});else if(s.querySelector("ol")!==null){const p=s.querySelectorAll("li");o==null||o.push({rawValue:[...p].map(v=>v.getAttribute("gdg-raw-value")??""),formatted:[...p].map(v=>v.textContent??""),format:"string-array"})}else if(f!==null)o==null||o.push({rawValue:L0(f),formatted:s.textContent??"",format:m});else{let p=s.textContent??"";d&&(p=p.replace(/\n(?!\n)/g,"")),o==null||o.push({rawValue:p??"",formatted:p??"",format:m})}}}return o!==void 0&&i.push(o),i}function H0(e,t,n,r,i){var l;const o=e;if(r==="allowPartial"||e.current===void 0||t===void 0)return e;let a=!1;do{if((e==null?void 0:e.current)===void 0)break;const s=(l=e.current)==null?void 0:l.range,u=[];if(s.width>2){const h=t({x:s.x,y:s.y,width:1,height:s.height},i.signal);if(typeof h=="function")return o;u.push(...h);const f=t({x:s.x+s.width-1,y:s.y,width:1,height:s.height},i.signal);if(typeof f=="function")return o;u.push(...f)}else{const h=t({x:s.x,y:s.y,width:s.width,height:s.height},i.signal);if(typeof h=="function")return o;u.push(...h)}let c=s.x-n,d=s.x+s.width-1-n;for(const h of u)for(const f of h)f.span!==void 0&&(c=Math.min(f.span[0],c),d=Math.max(f.span[1],d));c===s.x-n&&d===s.x+s.width-1-n?a=!0:e={current:{cell:e.current.cell??[0,0],range:{x:c+n,y:s.y,width:d-c+1,height:s.height},rangeStack:e.current.rangeStack},columns:e.columns,rows:e.rows}}while(!a);return e}function Du(e){return e.startsWith('"')&&e.endsWith('"')&&(e=e.slice(1,-1).replace(/""/g,'"')),e}function z0(e){let t;(function(l){l[l.None=0]="None",l[l.inString=1]="inString",l[l.inStringPostQuote=2]="inStringPostQuote"})(t||(t={}));const n=[];let r=[],i=0,o=t.None;e=e.replace(/\r\n/g,`
`);let a=0;for(const l of e){switch(o){case t.None:l==="	"||l===`
`?(r.push(e.slice(i,a)),i=a+1,l===`
`&&(n.push(r),r=[])):l==='"'&&(o=t.inString);break;case t.inString:l==='"'&&(o=t.inStringPostQuote);break;case t.inStringPostQuote:l==='"'?o=t.inString:((l==="	"||l===`
`)&&(r.push(Du(e.slice(i,a))),i=a+1,l===`
`&&(n.push(r),r=[])),o=t.None);break}a++}return i<e.length&&r.push(Du(e.slice(i,e.length))),n.push(r),n.map(l=>l.map(s=>({rawValue:s,formatted:s,format:"string"})))}function Ou(e,t,n){var l;const r=A0(e,t),i=s=>{var u;(u=window.navigator.clipboard)==null||u.writeText(s)},o=(s,u)=>{var c;return((c=window.navigator.clipboard)==null?void 0:c.write)===void 0?!1:(window.navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([s],{type:"text/plain"}),"text/html":new Blob([u],{type:"text/html"})})]),!0)},a=(s,u)=>{var c,d;try{if(n===void 0||n.clipboardData===null)throw new Error("No clipboard data");(c=n==null?void 0:n.clipboardData)==null||c.setData("text/plain",s),(d=n==null?void 0:n.clipboardData)==null||d.setData("text/html",u)}catch{o(s,u)||i(s)}};((l=window.navigator.clipboard)==null?void 0:l.write)!==void 0||(n==null?void 0:n.clipboardData)!==void 0?a(r.textPlain,r.textHtml):i(r.textPlain),n==null||n.preventDefault()}function Ad(e){return e!==!0}function Pu(e){return typeof e=="string"?e:`${e}px`}const V0=()=>e=>e.innerWidth,$0=()=>e=>e.innerHeight,N0=mn("div")({name:"Wrapper",class:"gdg-wmyidgi",propsAsIs:!1,vars:{"wmyidgi-0":[V0()],"wmyidgi-1":[$0()]}}),B0=e=>{const{inWidth:t,inHeight:n,children:r,...i}=e;return g.createElement(N0,{innerHeight:Pu(n),innerWidth:Pu(t),...i},r)},W0=2,U0=1300;function q0(e,t,n){const r=ie.useRef(0),[i,o]=e??[0,0];ie.useEffect(()=>{if(i===0&&o===0){r.current=0;return}let a=!1,l=0;const s=u=>{var c;if(!a){if(l===0)l=u;else{const d=u-l;r.current=Math.min(1,r.current+d/U0);const h=r.current**1.618*d*W0;(c=t.current)==null||c.scrollBy(i*h,o*h),l=u,n==null||n()}window.requestAnimationFrame(s)}};return window.requestAnimationFrame(s),()=>{a=!0}},[t,i,o,n])}function Y0({rowHeight:e,headerHeight:t,groupHeaderHeight:n,theme:r,overscrollX:i,overscrollY:o,scaleToRem:a,remSize:l}){const[s,u,c,d,h,f]=ie.useMemo(()=>{if(!a||l===16)return[e,t,n,r,i,o];const m=l/16,p=e,v=Cd();return[typeof p=="number"?p*m:w=>Math.ceil(p(w)*m),Math.ceil(t*m),Math.ceil(n*m),{...r,headerIconSize:((r==null?void 0:r.headerIconSize)??v.headerIconSize)*m,cellHorizontalPadding:((r==null?void 0:r.cellHorizontalPadding)??v.cellHorizontalPadding)*m,cellVerticalPadding:((r==null?void 0:r.cellVerticalPadding)??v.cellVerticalPadding)*m},Math.ceil((i??0)*m),Math.ceil((o??0)*m)]},[n,t,i,o,l,e,a,r]);return{rowHeight:s,headerHeight:u,groupHeaderHeight:c,theme:d,overscrollX:h,overscrollY:f}}const Dr={downFill:!1,rightFill:!1,clear:!0,closeOverlay:!0,acceptOverlayDown:!0,acceptOverlayUp:!0,acceptOverlayLeft:!0,acceptOverlayRight:!0,copy:!0,paste:!0,cut:!0,search:!1,delete:!0,activateCell:!0,scrollToSelectedCell:!0,goToFirstCell:!0,goToFirstColumn:!0,goToFirstRow:!0,goToLastCell:!0,goToLastColumn:!0,goToLastRow:!0,goToNextPage:!0,goToPreviousPage:!0,selectToFirstCell:!0,selectToFirstColumn:!0,selectToFirstRow:!0,selectToLastCell:!0,selectToLastColumn:!0,selectToLastRow:!0,selectAll:!0,selectRow:!0,selectColumn:!0,goUpCell:!0,goRightCell:!0,goDownCell:!0,goLeftCell:!0,goUpCellRetainSelection:!0,goRightCellRetainSelection:!0,goDownCellRetainSelection:!0,goLeftCellRetainSelection:!0,selectGrowUp:!0,selectGrowRight:!0,selectGrowDown:!0,selectGrowLeft:!0};function dt(e,t){return e===!0?t:e===!1?"":e}function _u(e){const t=ua.value;return{activateCell:dt(e.activateCell," |Enter|shift+Enter"),clear:dt(e.clear,"any+Escape"),closeOverlay:dt(e.closeOverlay,"any+Escape"),acceptOverlayDown:dt(e.acceptOverlayDown,"Enter"),acceptOverlayUp:dt(e.acceptOverlayUp,"shift+Enter"),acceptOverlayLeft:dt(e.acceptOverlayLeft,"shift+Tab"),acceptOverlayRight:dt(e.acceptOverlayRight,"Tab"),copy:e.copy,cut:e.cut,delete:dt(e.delete,t?"Backspace|Delete":"Delete"),downFill:dt(e.downFill,"primary+_68"),scrollToSelectedCell:dt(e.scrollToSelectedCell,"primary+Enter"),goDownCell:dt(e.goDownCell,"ArrowDown"),goDownCellRetainSelection:dt(e.goDownCellRetainSelection,"alt+ArrowDown"),goLeftCell:dt(e.goLeftCell,"ArrowLeft|shift+Tab"),goLeftCellRetainSelection:dt(e.goLeftCellRetainSelection,"alt+ArrowLeft"),goRightCell:dt(e.goRightCell,"ArrowRight|Tab"),goRightCellRetainSelection:dt(e.goRightCellRetainSelection,"alt+ArrowRight"),goUpCell:dt(e.goUpCell,"ArrowUp"),goUpCellRetainSelection:dt(e.goUpCellRetainSelection,"alt+ArrowUp"),goToFirstCell:dt(e.goToFirstCell,"primary+Home"),goToFirstColumn:dt(e.goToFirstColumn,"Home|primary+ArrowLeft"),goToFirstRow:dt(e.goToFirstRow,"primary+ArrowUp"),goToLastCell:dt(e.goToLastCell,"primary+End"),goToLastColumn:dt(e.goToLastColumn,"End|primary+ArrowRight"),goToLastRow:dt(e.goToLastRow,"primary+ArrowDown"),goToNextPage:dt(e.goToNextPage,"PageDown"),goToPreviousPage:dt(e.goToPreviousPage,"PageUp"),paste:e.paste,rightFill:dt(e.rightFill,"primary+_82"),search:dt(e.search,"primary+f"),selectAll:dt(e.selectAll,"primary+a"),selectColumn:dt(e.selectColumn,"ctrl+ "),selectGrowDown:dt(e.selectGrowDown,"shift+ArrowDown"),selectGrowLeft:dt(e.selectGrowLeft,"shift+ArrowLeft"),selectGrowRight:dt(e.selectGrowRight,"shift+ArrowRight"),selectGrowUp:dt(e.selectGrowUp,"shift+ArrowUp"),selectRow:dt(e.selectRow,"shift+ "),selectToFirstCell:dt(e.selectToFirstCell,"primary+shift+Home"),selectToFirstColumn:dt(e.selectToFirstColumn,"primary+shift+ArrowLeft"),selectToFirstRow:dt(e.selectToFirstRow,"primary+shift+ArrowUp"),selectToLastCell:dt(e.selectToLastCell,"primary+shift+End"),selectToLastColumn:dt(e.selectToLastColumn,"primary+shift+ArrowRight"),selectToLastRow:dt(e.selectToLastRow,"primary+shift+ArrowDown")}}function X0(e){const t=nm(e);return ie.useMemo(()=>{if(t===void 0)return _u(Dr);const n={...t,goToNextPage:(t==null?void 0:t.goToNextPage)??(t==null?void 0:t.pageDown)??Dr.goToNextPage,goToPreviousPage:(t==null?void 0:t.goToPreviousPage)??(t==null?void 0:t.pageUp)??Dr.goToPreviousPage,goToFirstCell:(t==null?void 0:t.goToFirstCell)??(t==null?void 0:t.first)??Dr.goToFirstCell,goToLastCell:(t==null?void 0:t.goToLastCell)??(t==null?void 0:t.last)??Dr.goToLastCell,selectToFirstCell:(t==null?void 0:t.selectToFirstCell)??(t==null?void 0:t.first)??Dr.selectToFirstCell,selectToLastCell:(t==null?void 0:t.selectToLastCell)??(t==null?void 0:t.last)??Dr.selectToLastCell};return _u({...Dr,...n})},[t])}function G0(e){function t(r,i,o){if(typeof r=="number")return{headerIndex:r,isCollapsed:!1,depth:i,path:o};const a={headerIndex:r.headerIndex,isCollapsed:r.isCollapsed,depth:i,path:o};return r.subGroups!==void 0&&(a.subGroups=r.subGroups.map((l,s)=>t(l,i+1,[...o,s])).sort((l,s)=>l.headerIndex-s.headerIndex)),a}return e.map((r,i)=>t(r,0,[i])).sort((r,i)=>r.headerIndex-i.headerIndex)}function Ks(e,t){const n=[];function r(a,l,s=!1){let u=l!==null?l-a.headerIndex:t-a.headerIndex;if(a.subGroups!==void 0&&(u=a.subGroups[0].headerIndex-a.headerIndex),u--,n.push({headerIndex:a.headerIndex,contentIndex:-1,skip:s,isCollapsed:a.isCollapsed,depth:a.depth,path:a.path,rows:u}),a.subGroups)for(let c=0;c<a.subGroups.length;c++){const d=c<a.subGroups.length-1?a.subGroups[c+1].headerIndex:l;r(a.subGroups[c],d,s||a.isCollapsed)}}const i=G0(e.groups);for(let a=0;a<i.length;a++){const l=a<i.length-1?i[a+1].headerIndex:null;r(i[a],l)}let o=0;for(const a of n)a.contentIndex=o,o+=a.rows;return n.filter(a=>a.skip===!1).map(a=>{const{skip:l,...s}=a;return s})}function da(e,t){if(t===void 0||Ks.length===0)return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1};let n=e;for(const r of t){if(n===0)return{path:[...r.path,-1],originalIndex:r.headerIndex,isGroupHeader:!0,groupIndex:-1,contentIndex:-1,groupRows:r.rows};if(n--,!r.isCollapsed){if(n<r.rows)return{path:[...r.path,n],originalIndex:r.headerIndex+n,isGroupHeader:!1,groupIndex:n,contentIndex:r.contentIndex+n,groupRows:r.rows};n-=r.rows}}return{path:[e],originalIndex:e,isGroupHeader:!1,groupIndex:e,contentIndex:e,groupRows:-1}}function j0(e,t,n,r){const i=ie.useMemo(()=>e===void 0?void 0:Ks(e,t),[e,t]),o=ie.useMemo(()=>i===void 0?t:i.reduce((u,c)=>u+(c.isCollapsed?1:c.rows+1),0),[i,t]),a=ie.useMemo(()=>e===void 0||typeof n=="number"&&e.height===n?n:u=>{const{isGroupHeader:c}=da(u,i);return c?e.height:typeof n=="number"?n:n(u)},[i,e,n]),l=ie.useCallback(u=>{if(i===void 0)return u;let c=u;for(const d of i){if(c===0)return;if(c--,!d.isCollapsed){if(c<d.rows)return d.contentIndex+c;c-=d.rows}}return u},[i]),s=Yr(r??(e==null?void 0:e.themeOverride),ie.useCallback(u=>{if(e===void 0)return r==null?void 0:r(u,u,u);if(r===void 0&&(e==null?void 0:e.themeOverride)===void 0)return;const{isGroupHeader:c,contentIndex:d,groupIndex:h}=da(u,i);return c?e.themeOverride:r==null?void 0:r(u,h,d)},[i,r,e]));return e===void 0?{rowHeight:a,rows:t,rowNumberMapper:l,getRowThemeOverride:s}:{rowHeight:a,rows:o,rowNumberMapper:l,getRowThemeOverride:s}}function K0(e,t){const n=ie.useMemo(()=>e===void 0?void 0:Ks(e,t),[e,t]);return{getRowGroupingForPath:zd,updateRowGroupingByPath:Hd,mapper:ie.useCallback(r=>{if(typeof r=="number")return da(r,n);const i=da(r[1],n);return{...i,originalIndex:[r[0],i.originalIndex]}},[n])}}function Hd(e,t,n){const[r,...i]=t;return i[0]===-1?e.map((o,a)=>a===r?{...o,...n}:o):e.map((o,a)=>a===r?{...o,subGroups:Hd(o.subGroups??[],i,n)}:o)}function zd(e,t){const[n,...r]=t;return r[0]===-1?e[n]:zd(e[n].subGroups??[],r)}function Z0(e,t){const[n]=g.useState(()=>({value:e,callback:t,facade:{get current(){return n.value},set current(r){const i=n.value;i!==r&&(n.value=r,n.callback(r,i))}}}));return n.callback=t,n.facade}function J0(e,t,n,r,i){const[o,a]=g.useMemo(()=>[t!==void 0&&typeof n=="number"?Math.floor(t/n):0,t!==void 0&&typeof n=="number"?-(t%n):0],[t,n]),l=g.useMemo(()=>({x:r.current.x,y:o,width:r.current.width??1,height:r.current.height??1,ty:a}),[r,a,o]),[s,u,c]=tm(l),d=g.useRef(i);d.current=i;const h=Z0(null,p=>{p!==null&&t!==void 0?p.scrollTop=t:p!==null&&e!==void 0&&(p.scrollLeft=e)}),f=(s.height??1)>1;g.useLayoutEffect(()=>{if(t!==void 0&&h.current!==null&&f){if(h.current.scrollTop===t)return;h.current.scrollTop=t,h.current.scrollTop!==t&&c(),d.current()}},[t,f,c,h]);const m=(s.width??1)>1;return g.useLayoutEffect(()=>{if(e!==void 0&&h.current!==null&&m){if(h.current.scrollLeft===e)return;h.current.scrollLeft=e,h.current.scrollLeft!==e&&c(),d.current()}},[e,m,c,h]),{visibleRegion:s,setVisibleRegion:u,scrollRef:h}}const Q0=g.lazy(async()=>await Ls(()=>import("./data-grid-overlay-editor.msYws2Ou.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url));let ev=0;function tv(e){return op(wu(wu(e).filter(t=>t.span!==void 0).map(t=>{var n,r;return lr((((n=t.span)==null?void 0:n[0])??0)+1,(((r=t.span)==null?void 0:r[1])??0)+1)})))}function No(e,t){return e===void 0||t===0||e.columns.length===0&&e.current===void 0?e:{current:e.current===void 0?void 0:{cell:[e.current.cell[0]+t,e.current.cell[1]],range:{...e.current.range,x:e.current.range.x+t},rangeStack:e.current.rangeStack.map(n=>({...n,x:n.x+t}))},rows:e.rows,columns:e.columns.offset(t)}}const Bo={kind:te.Loading,allowOverlay:!1},Wo={columns:pt.empty(),rows:pt.empty(),current:void 0},nv=(e,t)=>{var El,Il,Tl;const[n,r]=g.useState(Wo),[i,o]=g.useState(),a=g.useRef(null),l=g.useRef(null),[s,u]=g.useState(),c=g.useRef(),d=typeof window>"u"?null:window,{imageEditorOverride:h,getRowThemeOverride:f,markdownDivCreateNode:m,width:p,height:v,columns:w,rows:b,getCellContent:M,onCellClicked:O,onCellActivated:S,onFillPattern:R,onFinishedEditing:_,coercePasteValue:E,drawHeader:x,drawCell:L,editorBloom:D,onHeaderClicked:C,onColumnProposeMove:I,rangeSelectionColumnSpanning:T=!0,spanRangeBehavior:k="default",onGroupHeaderClicked:z,onCellContextMenu:N,className:X,onHeaderContextMenu:re,getCellsForSelection:G,onGroupHeaderContextMenu:j,onGroupHeaderRenamed:se,onCellEdited:oe,onCellsEdited:ue,onSearchResultsChanged:he,searchResults:Q,onSearchValueChange:H,searchValue:P,onKeyDown:W,onKeyUp:ce,keybindings:Oe,editOnType:ze=!0,onRowAppended:Ce,onColumnMoved:Ee,validateCell:xe,highlightRegions:It,rangeSelect:yt="rect",columnSelect:it="multi",rowSelect:le="multi",rangeSelectionBlending:tt="exclusive",columnSelectionBlending:ve="exclusive",rowSelectionBlending:ge="exclusive",onDelete:pe,onDragStart:Ve,onMouseMove:_e,onPaste:nt,copyHeaders:be=!1,freezeColumns:de=0,cellActivationBehavior:ot="second-click",rowSelectionMode:He="auto",onHeaderMenuClick:et,onHeaderIndicatorClick:bt,getGroupDetails:gt,rowGrouping:$e,onSearchClose:Ot,onItemHovered:Yt,onSelectionCleared:kt,showSearch:Lt,onVisibleRegionChanged:nn,gridSelection:zt,onGridSelectionChange:ln,minColumnWidth:Tt=50,maxColumnWidth:Ct=500,maxColumnAutoWidth:Dn,provideEditor:yn,trailingRowOptions:Pe,freezeTrailingRows:Ft=0,allowedFillDirections:fn="orthogonal",scrollOffsetX:hn,scrollOffsetY:pn,verticalBorder:st,onDragOverCell:Xt,onDrop:Pt,onColumnResize:rn,onColumnResizeEnd:Dt,onColumnResizeStart:At,customRenderers:We,fillHandle:Bt,experimental:_t,fixedShadowX:Vt,fixedShadowY:$t,headerIcons:Cn,imageWindowLoader:nr,initialSize:Hn,isDraggable:V,onDragLeave:Qe,onRowMoved:Ue,overscrollX:fe,overscrollY:lt,preventDiagonalScrolling:Wt,rightElement:dn,rightElementProps:Zt,trapFocus:jt=!1,smoothScrollX:Sn,smoothScrollY:vn,scaleToRem:Cr=!1,rowHeight:Xn=34,headerHeight:mo=36,groupHeaderHeight:Ea=mo,theme:ei,isOutsideClick:Sr,renderers:Si,resizeIndicator:Ia,scrollToActiveCell:xi=!0,drawFocusRing:ki=!0}=e,Ar=ki==="no-editor"?i===void 0:ki,Qt=typeof e.rowMarkers=="string"?void 0:e.rowMarkers,Rn=(Qt==null?void 0:Qt.kind)??e.rowMarkers??"none",Mi=(Qt==null?void 0:Qt.width)??e.rowMarkerWidth,Ri=(Qt==null?void 0:Qt.startIndex)??e.rowMarkerStartIndex??1,ti=(Qt==null?void 0:Qt.theme)??e.rowMarkerTheme,fr=Qt==null?void 0:Qt.headerTheme,hr=Qt==null?void 0:Qt.headerAlwaysVisible,ni=le!=="multi",rr=(Qt==null?void 0:Qt.checkboxStyle)??"square",ir=Math.max(Tt,20),Hr=Math.max(Ct,ir),po=Math.max(Dn??Hr,ir),Ei=g.useMemo(()=>typeof window>"u"?{fontSize:"16px"}:window.getComputedStyle(document.documentElement),[]),{rows:je,rowNumberMapper:vo,rowHeight:bo,getRowThemeOverride:zn}=j0($e,b,Xn,f),wo=g.useMemo(()=>Number.parseFloat(Ei.fontSize),[Ei]),{rowHeight:Gn,headerHeight:Ii,groupHeaderHeight:yo,theme:Co,overscrollX:Ta,overscrollY:Da}=Y0({groupHeaderHeight:Ea,headerHeight:mo,overscrollX:fe,overscrollY:lt,remSize:wo,rowHeight:bo,scaleToRem:Cr,theme:ei}),Vn=X0(Oe),xr=Mi??(b>1e4?48:b>1e3?44:b>100?36:32),$n=Rn!=="none",q=$n?1:0,Kt=Ce!==void 0,kr=(Pe==null?void 0:Pe.sticky)===!0,[ri,ii]=g.useState(!1),Ti=Lt??ri,Oa=g.useCallback(()=>{Ot!==void 0?Ot():ii(!1)},[Ot]),K=g.useMemo(()=>zt===void 0?void 0:No(zt,q),[zt,q])??n,un=g.useRef();un.current===void 0&&(un.current=new AbortController),g.useEffect(()=>()=>un==null?void 0:un.current.abort(),[]);const[cn,Pa]=D0(G,M,q,un.current,je),$=g.useCallback((y,A,F)=>{if(xe===void 0)return!0;const U=[y[0]-q,y[1]];return xe==null?void 0:xe(U,A,F)},[q,xe]),ae=g.useRef(zt),Se=g.useCallback((y,A)=>{A&&(y=H0(y,cn,q,k,un.current)),ln!==void 0?(ae.current=No(y,-q),ln(ae.current)):r(y)},[ln,cn,q,k]),ke=Yr(rn,g.useCallback((y,A,F,U)=>{rn==null||rn(w[F-q],A,F-q,U)},[rn,q,w])),me=Yr(Dt,g.useCallback((y,A,F,U)=>{Dt==null||Dt(w[F-q],A,F-q,U)},[Dt,q,w])),Re=Yr(At,g.useCallback((y,A,F,U)=>{At==null||At(w[F-q],A,F-q,U)},[At,q,w])),rt=Yr(x,g.useCallback((y,A)=>(x==null?void 0:x({...y,columnIndex:y.columnIndex-q},A))??!1,[x,q])),at=Yr(L,g.useCallback((y,A)=>(L==null?void 0:L({...y,col:y.col-q},A))??!1,[L,q])),Te=g.useCallback(y=>{if(pe!==void 0){const A=pe(No(y,-q));return typeof A=="boolean"?A:No(A,q)}return!0},[pe,q]),[Mt,Me,Ke]=T0(K,Se,tt,ve,ge,yt,T),qe=g.useMemo(()=>vr(Cd(),Co),[Co]),[ht,sn]=g.useState([0,0,0]),En=g.useMemo(()=>{if(Si===void 0)return{};const y={};for(const A of Si)y[A.kind]=A;return y},[Si]),St=g.useCallback(y=>y.kind!==te.Custom?En[y.kind]:We==null?void 0:We.find(A=>A.isMatch(y)),[We,En]);let{sizedColumns:Jt,nonGrowWidth:jn}=Km(w,je,Pa,ht[0]-(q===0?0:xr)-ht[2],ir,po,qe,St,un.current);Rn!=="none"&&(jn+=xr);const xn=g.useMemo(()=>Jt.some(y=>y.group!==void 0),[Jt]),Et=xn?Ii+yo:Ii,In=K.rows.length,On=Rn==="none"?void 0:In===0?!1:In===je?!0:void 0,xt=g.useMemo(()=>Rn==="none"?Jt:[{title:"",width:xr,icon:void 0,hasMenu:!1,style:"normal",themeOverride:ti,rowMarker:rr,rowMarkerChecked:On,headerRowMarkerTheme:fr,headerRowMarkerAlwaysVisible:hr,headerRowMarkerDisabled:ni},...Jt],[Rn,Jt,xr,ti,rr,On,fr,hr,ni]),en=g.useRef({height:1,width:1,x:0,y:0}),Mr=g.useRef(!1),{setVisibleRegion:Rr,visibleRegion:zr,scrollRef:Ut}=J0(hn,pn,Gn,en,()=>Mr.current=!0);en.current=zr;const Lf=zr.x+q,So=zr.y,Tn=g.useRef(null),kn=g.useCallback(y=>{var A;y===!0?(A=Tn.current)==null||A.focus():window.requestAnimationFrame(()=>{var F;(F=Tn.current)==null||F.focus()})},[]),bn=Kt?je+1:je,Nn=g.useCallback(y=>{const A=q===0?y:y.map(U=>({...U,location:[U.location[0]-q,U.location[1]]})),F=ue==null?void 0:ue(A);if(F!==!0)for(const U of A)oe==null||oe(U.location,U.value);return F},[oe,ue,q]),[Vr,_a]=g.useState(),xo=K.current!==void 0&&K.current.range.width*K.current.range.height>1?K.current.range:void 0,oi=Ar?(El=K.current)==null?void 0:El.cell:void 0,ko=oi==null?void 0:oi[0],Mo=oi==null?void 0:oi[1],Ff=g.useMemo(()=>{if((It===void 0||It.length===0)&&(xo??ko??Mo??Vr)===void 0)return;const y=[];if(It!==void 0)for(const A of It){const F=xt.length-A.range.x-q;F>0&&y.push({color:A.color,range:{...A.range,x:A.range.x+q,width:Math.min(F,A.range.width)},style:A.style})}return Vr!==void 0&&y.push({color:Gr(qe.accentColor,0),range:Vr,style:"dashed"}),xo!==void 0&&y.push({color:Gr(qe.accentColor,.5),range:xo,style:"solid-outline"}),ko!==void 0&&Mo!==void 0&&y.push({color:qe.accentColor,range:{x:ko,y:Mo,width:1,height:1},style:"solid-outline"}),y.length>0?y:void 0},[Vr,xo,ko,Mo,It,xt.length,qe.accentColor,q]),hl=g.useRef(xt);hl.current=xt;const Pn=g.useCallback(([y,A],F=!1)=>{var J,Y,Z,ee,ne,we,Le;const U=Kt&&A===bn-1;if(y===0&&$n){if(U)return Bo;const ye=vo(A);return ye===void 0?Bo:{kind:Yn.Marker,allowOverlay:!1,checkboxStyle:rr,checked:(K==null?void 0:K.rows.hasIndex(A))===!0,markerKind:Rn==="clickable-number"?"number":Rn,row:Ri+ye,drawHandle:Ue!==void 0,cursor:Rn==="clickable-number"?"pointer":void 0}}else if(U){const Ae=y===q?(Pe==null?void 0:Pe.hint)??"":"",De=hl.current[y];if(((J=De==null?void 0:De.trailingRowOptions)==null?void 0:J.disabled)===!0)return Bo;{const mt=((Y=De==null?void 0:De.trailingRowOptions)==null?void 0:Y.hint)??Ae,Ye=((Z=De==null?void 0:De.trailingRowOptions)==null?void 0:Z.addIcon)??(Pe==null?void 0:Pe.addIcon);return{kind:Yn.NewRow,hint:mt,allowOverlay:!1,icon:Ye}}}else{const ye=y-q;if(F||(_t==null?void 0:_t.strict)===!0){const De=en.current,mt=De.x>ye||ye>De.x+De.width||De.y>A||A>De.y+De.height||A>=Fa.current,Ye=ye===((ne=(ee=De.extras)==null?void 0:ee.selected)==null?void 0:ne[0])&&A===((we=De.extras)==null?void 0:we.selected[1]);let Fe=!1;if(((Le=De.extras)==null?void 0:Le.freezeRegions)!==void 0){for(const Rt of De.extras.freezeRegions)if(Xr(Rt,ye,A)){Fe=!0;break}}if(mt&&!Ye&&!Fe)return Bo}let Ae=M([ye,A]);return q!==0&&Ae.span!==void 0&&(Ae={...Ae,span:[Ae.span[0]+q,Ae.span[1]+q]}),Ae}},[Kt,bn,$n,vo,rr,K==null?void 0:K.rows,Rn,Ri,Ue,q,Pe==null?void 0:Pe.hint,Pe==null?void 0:Pe.addIcon,_t==null?void 0:_t.strict,M]),La=g.useCallback(y=>{let A=(gt==null?void 0:gt(y))??{name:y};return se!==void 0&&y!==""&&(A={icon:A.icon,name:A.name,overrideTheme:A.overrideTheme,actions:[...A.actions??[],{title:"Rename",icon:"renameIcon",onClick:F=>Ha({group:A.name,bounds:F.bounds})}]}),A},[gt,se]),Ro=g.useCallback(y=>{var Z;const[A,F]=y.cell,U=xt[A],B=(U==null?void 0:U.group)!==void 0?(Z=La(U.group))==null?void 0:Z.overrideTheme:void 0,J=U==null?void 0:U.themeOverride,Y=zn==null?void 0:zn(F);o({...y,theme:vr(qe,B,J,Y,y.content.themeOverride)})},[zn,xt,La,qe]),ai=g.useCallback((y,A,F)=>{var Y;if(K.current===void 0)return;const[U,B]=K.current.cell,J=Pn([U,B]);if(J.kind!==te.Boolean&&J.allowOverlay){let Z=J;if(F!==void 0)switch(Z.kind){case te.Number:{const ee=Mg(()=>F==="-"?-0:Number.parseFloat(F),0);Z={...Z,data:Number.isNaN(ee)?0:ee};break}case te.Text:case te.Markdown:case te.Uri:Z={...Z,data:F};break}Ro({target:y,content:Z,initialValue:F,cell:[U,B],highlight:F===void 0,forceEditMode:F!==void 0})}else J.kind===te.Boolean&&A&&J.readonly!==!0&&(Nn([{location:K.current.cell,value:{...J,data:Ad(J.data)}}]),(Y=Tn.current)==null||Y.damage([{cell:K.current.cell}]))},[Pn,K,Nn,Ro]),gl=g.useCallback((y,A)=>{var B;const F=(B=Tn.current)==null?void 0:B.getBounds(y,A);if(F===void 0||Ut.current===null)return;const U=Pn([y,A]);U.allowOverlay&&Ro({target:F,content:U,initialValue:void 0,highlight:!0,cell:[y,A],forceEditMode:!0})},[Pn,Ut,Ro]),on=g.useCallback((y,A,F="both",U=0,B=0,J=void 0)=>{if(Ut.current!==null){const Y=Tn.current,Z=l.current,ee=typeof y!="number"?y.unit==="cell"?y.amount:void 0:y,ne=typeof A!="number"?A.unit==="cell"?A.amount:void 0:A,we=typeof y!="number"&&y.unit==="px"?y.amount:void 0,Le=typeof A!="number"&&A.unit==="px"?A.amount:void 0;if(Y!==null&&Z!==null){let ye={x:0,y:0,width:0,height:0},Ae=0,De=0;if((ee!==void 0||ne!==void 0)&&(ye=Y.getBounds((ee??0)+q,ne??0)??ye,ye.width===0||ye.height===0))return;const mt=Z.getBoundingClientRect(),Ye=mt.width/Z.offsetWidth;if(we!==void 0&&(ye={...ye,x:we-mt.left-Ut.current.scrollLeft,width:1}),Le!==void 0&&(ye={...ye,y:Le+mt.top-Ut.current.scrollTop,height:1}),ye!==void 0){const Fe={x:ye.x-U,y:ye.y-B,width:ye.width+2*U,height:ye.height+2*B};let Rt=0;for(let Ua=0;Ua<de;Ua++)Rt+=Jt[Ua].width;let Gt=0;const an=Ft+(kr?1:0);an>0&&(Gt=Jr(bn,an,Gn));let Jn=Rt*Ye+mt.left+q*xr*Ye,ar=mt.right,Qn=mt.top+Et*Ye,sr=mt.bottom-Gt*Ye;const _o=ye.width+U*2;switch(J==null?void 0:J.hAlign){case"start":ar=Jn+_o;break;case"end":Jn=ar-_o;break;case"center":Jn=Math.floor((Jn+ar)/2)-_o/2,ar=Jn+_o;break}const Lo=ye.height+B*2;switch(J==null?void 0:J.vAlign){case"start":sr=Qn+Lo;break;case"end":Qn=sr-Lo;break;case"center":Qn=Math.floor((Qn+sr)/2)-Lo/2,sr=Qn+Lo;break}Jn>Fe.x?Ae=Fe.x-Jn:ar<Fe.x+Fe.width&&(Ae=Fe.x+Fe.width-ar),Qn>Fe.y?De=Fe.y-Qn:sr<Fe.y+Fe.height&&(De=Fe.y+Fe.height-sr),F==="vertical"||typeof y=="number"&&y<de?Ae=0:(F==="horizontal"||typeof A=="number"&&A>=bn-an)&&(De=0),(Ae!==0||De!==0)&&(Ye!==1&&(Ae/=Ye,De/=Ye),Ut.current.scrollTo(Ae+Ut.current.scrollLeft,De+Ut.current.scrollTop))}}}},[q,Ft,xr,Ut,Et,de,Jt,bn,kr,Gn]),ml=g.useRef(gl),pl=g.useRef(M),Fa=g.useRef(je);ml.current=gl,pl.current=M,Fa.current=je;const si=g.useCallback(async(y,A=!0)=>{var ee;const F=xt[y];if(((ee=F==null?void 0:F.trailingRowOptions)==null?void 0:ee.disabled)===!0)return;const U=Ce==null?void 0:Ce();let B,J=!0;U!==void 0&&(B=await U,B==="top"&&(J=!1),typeof B=="number"&&(J=!1));let Y=0;const Z=()=>{if(Fa.current<=je){Y<500&&window.setTimeout(Z,Y),Y=50+Y*2;return}const ne=typeof B=="number"?B:J?je:0;Po.current(y-q,ne),Mt({cell:[y,ne],range:{x:y,y:ne,width:1,height:1}},!1,!1,"edit");const we=pl.current([y-q,ne]);we.allowOverlay&&Zi(we)&&we.readonly!==!0&&A&&window.setTimeout(()=>{ml.current(y,ne)},0)};Z()},[xt,Ce,q,je,Mt]),Eo=g.useCallback(y=>{var F,U;const A=((U=(F=Jt[y])==null?void 0:F.trailingRowOptions)==null?void 0:U.targetColumn)??(Pe==null?void 0:Pe.targetColumn);if(typeof A=="number")return A+($n?1:0);if(typeof A=="object"){const B=w.indexOf(A);if(B>=0)return B+($n?1:0)}},[Jt,w,$n,Pe==null?void 0:Pe.targetColumn]),Er=g.useRef(),li=g.useRef(),Oi=g.useCallback((y,A)=>{var B;const[F,U]=A;return vr(qe,(B=xt[F])==null?void 0:B.themeOverride,zn==null?void 0:zn(U),y.themeOverride)},[zn,xt,qe]),{mapper:$r}=K0($e,b),Kn=$e==null?void 0:$e.navigationBehavior,Pi=g.useCallback(y=>{var we,Le,ye;const A=ua.value?y.metaKey:y.ctrlKey,F=A&&le==="multi",U=A&&it==="multi",[B,J]=y.location,Y=K.columns,Z=K.rows,[ee,ne]=((we=K.current)==null?void 0:we.cell)??[];if(y.kind==="cell"){if(li.current=void 0,Nr.current=[B,J],B===0&&$n){if(Kt===!0&&J===je||Rn==="number"||le==="none")return;const Ae=Pn(y.location);if(Ae.kind!==Yn.Marker)return;if(Ue!==void 0){const Ye=St(Ae);Fn((Ye==null?void 0:Ye.kind)===Yn.Marker);const Fe=(Le=Ye==null?void 0:Ye.onClick)==null?void 0:Le.call(Ye,{...y,cell:Ae,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,theme:Oi(Ae,y.location),preventDefault:()=>{}});if(Fe===void 0||Fe.checked===Ae.checked)return}o(void 0),kn();const De=Z.hasIndex(J),mt=Er.current;if(le==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&mt!==void 0&&Z.hasIndex(mt)){const Ye=[Math.min(mt,J),Math.max(mt,J)+1];F||He==="multi"?Me(void 0,Ye,!0):Me(pt.fromSingleSelection(Ye),void 0,F)}else le==="multi"&&(F||y.isTouch||He==="multi")?De?Me(Z.remove(J),void 0,!0):(Me(void 0,J,!0),Er.current=J):De&&Z.length===1?Me(pt.empty(),void 0,A):(Me(pt.fromSingleSelection(J),void 0,A),Er.current=J)}else if(B>=q&&Kt&&J===je){const Ae=Eo(B);si(Ae??B)}else if(ee!==B||ne!==J){const Ae=Pn(y.location),De=St(Ae);if((De==null?void 0:De.onSelect)!==void 0){let Fe=!1;if(De.onSelect({...y,cell:Ae,posX:y.localEventX,posY:y.localEventY,bounds:y.bounds,preventDefault:()=>Fe=!0,theme:Oi(Ae,y.location)}),Fe)return}if(Kn==="block"&&$r(J).isGroupHeader)return;const mt=kr&&J===je,Ye=kr&&K!==void 0&&((ye=K.current)==null?void 0:ye.cell[1])===je;if((y.shiftKey||y.isLongTouch===!0)&&ee!==void 0&&ne!==void 0&&K.current!==void 0&&!Ye){if(mt)return;const Fe=Math.min(B,ee),Rt=Math.max(B,ee),Gt=Math.min(J,ne),an=Math.max(J,ne);Mt({...K.current,range:{x:Fe,y:Gt,width:Rt-Fe+1,height:an-Gt+1}},!0,A,"click"),Er.current=void 0,kn()}else Mt({cell:[B,J],range:{x:B,y:J,width:1,height:1}},!0,A,"click"),Er.current=void 0,o(void 0),kn()}}else if(y.kind==="header")if(Nr.current=[B,J],o(void 0),$n&&B===0)Er.current=void 0,li.current=void 0,le==="multi"&&(Z.length!==je?Me(pt.fromSingleSelection([0,je]),void 0,A):Me(pt.empty(),void 0,A),kn());else{const Ae=li.current;if(it==="multi"&&(y.shiftKey||y.isLongTouch===!0)&&Ae!==void 0&&Y.hasIndex(Ae)){const De=[Math.min(Ae,B),Math.max(Ae,B)+1];U?Ke(void 0,De,A):Ke(pt.fromSingleSelection(De),void 0,A)}else U?(Y.hasIndex(B)?Ke(Y.remove(B),void 0,A):Ke(void 0,B,A),li.current=B):it!=="none"&&(Ke(pt.fromSingleSelection(B),void 0,A),li.current=B);Er.current=void 0,kn()}else y.kind===qn?Nr.current=[B,J]:y.kind===ca&&!y.isMaybeScrollbar&&(Se(Wo,!1),o(void 0),kn(),kt==null||kt(),Er.current=void 0,li.current=void 0)},[le,it,K,$n,q,Kt,je,Rn,Pn,Ue,kn,He,St,Oi,Me,Eo,si,Kn,$r,kr,Mt,Ke,Se,kt]),_i=g.useRef(!1),Nr=g.useRef(),vl=g.useRef(zr),Bn=g.useRef(),Af=g.useCallback(y=>{if(ui.current=!1,vl.current=en.current,y.button!==0&&y.button!==1){Bn.current=void 0;return}const A=performance.now();Bn.current={button:y.button,time:A,location:y.location},(y==null?void 0:y.kind)==="header"&&(_i.current=!0);const F=y.kind==="cell"&&y.isFillHandle;!F&&y.kind!=="cell"&&y.isEdge||(u({previousSelection:K,fillHandle:F}),Nr.current=void 0,!y.isTouch&&y.button===0&&!F?Pi(y):!y.isTouch&&y.button===1&&(Nr.current=y.location))},[K,Pi]),[Aa,Ha]=g.useState(),bl=g.useCallback(y=>{if(y.kind!==qn||it!=="multi")return;const A=ua.value?y.metaKey:y.ctrlKey,[F]=y.location,U=K.columns;if(F<q)return;const B=xt[F];let J=F,Y=F;for(let Z=F-1;Z>=q&&oo(B.group,xt[Z].group);Z--)J--;for(let Z=F+1;Z<xt.length&&oo(B.group,xt[Z].group);Z++)Y++;if(kn(),A)if(U.hasAll([J,Y+1])){let Z=U;for(let ee=J;ee<=Y;ee++)Z=Z.remove(ee);Ke(Z,void 0,A)}else Ke(void 0,[J,Y+1],A);else Ke(pt.fromSingleSelection([J,Y+1]),void 0,A)},[it,kn,K.columns,xt,q,Ke]),ui=g.useRef(!1),Io=g.useCallback(async y=>{if(cn!==void 0&&ke!==void 0){const A=en.current.y,F=en.current.height;let U=cn({x:y,y:A,width:1,height:Math.min(F,je-A)},un.current.signal);typeof U!="object"&&(U=await U());const B=Jt[y-q],Y=document.createElement("canvas").getContext("2d",{alpha:!1});if(Y!==null){Y.font=qe.baseFontFull;const Z=xd(Y,qe,B,0,U,ir,Hr,!1,St);ke==null||ke(B,Z.width,y,Z.width)}}},[Jt,cn,Hr,qe,ir,ke,q,je,St]),[Hf,za]=g.useState(),ci=g.useCallback(async(y,A)=>{var Z,ee;const F=(Z=y.current)==null?void 0:Z.range;if(F===void 0||cn===void 0||A.current===void 0)return;const U=A.current.range;if(R!==void 0){let ne=!1;if(R({fillDestination:{...U,x:U.x-q},patternSource:{...F,x:F.x-q},preventDefault:()=>ne=!0}),ne)return}let B=cn(F,un.current.signal);typeof B!="object"&&(B=await B());const J=B,Y=[];for(let ne=0;ne<U.width;ne++)for(let we=0;we<U.height;we++){const Le=[U.x+ne,U.y+we];if(dd(Le,F))continue;const ye=J[we%F.height][ne%F.width];mi(ye)||!Zi(ye)||Y.push({location:Le,value:{...ye}})}Nn(Y),(ee=Tn.current)==null||ee.damage(Y.map(ne=>({cell:ne.location})))},[cn,Nn,R,q]),wl=g.useCallback(()=>{if(K.current===void 0||K.current.range.width<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,width:1}}};ci(y,K)},[ci,K]),yl=g.useCallback(()=>{if(K.current===void 0||K.current.range.height<=1)return;const y={...K,current:{...K.current,range:{...K.current.range,height:1}}};ci(y,K)},[ci,K]),zf=g.useCallback((y,A)=>{var we,Le;const F=s;if(u(void 0),_a(void 0),za(void 0),_i.current=!1,A)return;if((F==null?void 0:F.fillHandle)===!0&&K.current!==void 0&&((we=F.previousSelection)==null?void 0:we.current)!==void 0){if(Vr===void 0)return;const ye={...K,current:{...K.current,range:Id(F.previousSelection.current.range,Vr)}};ci(F.previousSelection,ye),Se(ye,!0);return}const[U,B]=y.location,[J,Y]=Nr.current??[],Z=()=>{ui.current=!0},ee=ye=>{var De,mt,Ye;const Ae=ye.isTouch||J===U&&Y===B;if(Ae&&(O==null||O([U-q,B],{...ye,preventDefault:Z})),ye.button===1)return!ui.current;if(!ui.current){const Fe=Pn(y.location),Rt=St(Fe);if(Rt!==void 0&&Rt.onClick!==void 0&&Ae){const an=Rt.onClick({...ye,cell:Fe,posX:ye.localEventX,posY:ye.localEventY,bounds:ye.bounds,theme:Oi(Fe,y.location),preventDefault:Z});an!==void 0&&!mi(an)&&hi(an)&&(Nn([{location:ye.location,value:an}]),(De=Tn.current)==null||De.damage([{cell:ye.location}]))}if(ui.current||K.current===void 0)return!1;let Gt=!1;switch(Fe.activationBehaviorOverride??ot){case"double-click":case"second-click":{if(((Ye=(mt=F==null?void 0:F.previousSelection)==null?void 0:mt.current)==null?void 0:Ye.cell)===void 0)break;const[an,Jn]=K.current.cell,[ar,Qn]=F.previousSelection.current.cell;Gt=U===an&&U===ar&&B===Jn&&B===Qn&&(ye.isDoubleClick===!0||ot==="second-click");break}case"single-click":{Gt=!0;break}}if(Gt)return S==null||S([U-q,B]),ai(ye.bounds,!1),!0}return!1},ne=y.location[0]-q;if(y.isTouch){const ye=en.current,Ae=vl.current;if(ye.x!==Ae.x||ye.y!==Ae.y)return;if(y.isLongTouch===!0){if(y.kind==="cell"&&eo((Le=K.current)==null?void 0:Le.cell,y.location)){N==null||N([ne,y.location[1]],{...y,preventDefault:Z});return}else if(y.kind==="header"&&K.columns.hasIndex(U)){re==null||re(ne,{...y,preventDefault:Z});return}else if(y.kind===qn){if(ne<0)return;j==null||j(ne,{...y,preventDefault:Z});return}}y.kind==="cell"?ee(y)||Pi(y):y.kind===qn?z==null||z(ne,{...y,preventDefault:Z}):(y.kind===Or&&(C==null||C(ne,{...y,preventDefault:Z})),Pi(y));return}if(y.kind==="header"){if(ne<0)return;y.isEdge?y.isDoubleClick===!0&&Io(U):y.button===0&&U===J&&B===Y&&(C==null||C(ne,{...y,preventDefault:Z}))}if(y.kind===qn){if(ne<0)return;y.button===0&&U===J&&B===Y&&(z==null||z(ne,{...y,preventDefault:Z}),ui.current||bl(y))}y.kind==="cell"&&(y.button===0||y.button===1)&&ee(y),Nr.current=void 0},[s,K,q,Vr,ci,Se,O,Pn,St,ot,Oi,Nn,S,ai,N,re,j,Pi,z,C,Io,bl]),Vf=g.useCallback(y=>{const A={...y,location:[y.location[0]-q,y.location[1]]};_e==null||_e(A),s!==void 0&&y.buttons===0&&(u(void 0),_a(void 0),za(void 0),_i.current=!1),za(F=>{var U;return _i.current?[y.scrollEdge[0],0]:y.scrollEdge[0]===(F==null?void 0:F[0])&&y.scrollEdge[1]===F[1]?F:s===void 0||(((U=Bn.current)==null?void 0:U.location[0])??0)<q?void 0:y.scrollEdge})},[s,_e,q]),$f=g.useCallback((y,A)=>{et==null||et(y-q,A)},[et,q]),Nf=g.useCallback((y,A)=>{bt==null||bt(y-q,A)},[bt,q]),or=(Il=K==null?void 0:K.current)==null?void 0:Il.cell,Bf=g.useCallback((y,A,F,U,B,J)=>{Mr.current=!1;let Y=or;Y!==void 0&&(Y=[Y[0]-q,Y[1]]);const Z=de===0?void 0:{x:0,y:y.y,width:de,height:y.height},ee=[];Z!==void 0&&ee.push(Z),Ft>0&&(ee.push({x:y.x-q,y:je-Ft,width:y.width,height:Ft}),de>0&&ee.push({x:0,y:je-Ft,width:de,height:Ft}));const ne={x:y.x-q,y:y.y,width:y.width,height:Kt&&y.y+y.height>=je?y.height-1:y.height,tx:B,ty:J,extras:{selected:Y,freezeRegion:Z,freezeRegions:ee}};en.current=ne,Rr(ne),sn([A,F,U]),nn==null||nn(ne,ne.tx,ne.ty,ne.extras)},[or,q,Kt,je,de,Ft,Rr,nn]),Wf=Yr(Ee,g.useCallback((y,A)=>{Ee==null||Ee(y-q,A-q),it!=="none"&&Ke(pt.fromSingleSelection(A),void 0,!0)},[it,Ee,q,Ke])),Va=g.useRef(!1),Uf=g.useCallback(y=>{if(y.location[0]===0&&q>0){y.preventDefault();return}Ve==null||Ve({...y,location:[y.location[0]-q,y.location[1]]}),y.defaultPrevented()||(Va.current=!0),u(void 0)},[Ve,q]),qf=g.useCallback(()=>{Va.current=!1},[]),Cl=$e==null?void 0:$e.selectionBehavior,To=g.useCallback(y=>{if(Cl!=="block-spanning")return;const{isGroupHeader:A,path:F,groupRows:U}=$r(y);if(A)return[y,y];const B=F[F.length-1],J=y-B,Y=y+U-B-1;return[J,Y]},[$r,Cl]),$a=g.useRef(),Na=g.useCallback(y=>{var A,F,U;if(!Ld(y,$a.current)&&($a.current=y,!(((A=Bn==null?void 0:Bn.current)==null?void 0:A.button)!==void 0&&Bn.current.button>=1))){if(y.buttons!==0&&s!==void 0&&((F=Bn.current)==null?void 0:F.location[0])===0&&y.location[0]===0&&q===1&&le==="multi"&&s.previousSelection&&!s.previousSelection.rows.hasIndex(Bn.current.location[1])&&K.rows.hasIndex(Bn.current.location[1])){const B=Math.min(Bn.current.location[1],y.location[1]),J=Math.max(Bn.current.location[1],y.location[1])+1;Me(pt.fromSingleSelection([B,J]),void 0,!1)}if(y.buttons!==0&&s!==void 0&&K.current!==void 0&&!Va.current&&!_i.current&&(yt==="rect"||yt==="multi-rect")){const[B,J]=K.current.cell;let[Y,Z]=y.location;if(Z<0&&(Z=en.current.y),s.fillHandle===!0&&((U=s.previousSelection)==null?void 0:U.current)!==void 0){const ee=s.previousSelection.current.range;Z=Math.min(Z,Kt?je-1:je);const ne=zp(ee,Y,Z,fn);_a(ne)}else{if(Kt&&J===je)return;if(Kt&&Z===je)if(y.kind===ca)Z--;else return;Y=Math.max(Y,q);const we=To(J);Z=we===void 0?Z:Un(Z,we[0],we[1]);const Le=Y-B,ye=Z-J,Ae={x:Le>=0?B:Y,y:ye>=0?J:Z,width:Math.abs(Le)+1,height:Math.abs(ye)+1};Mt({...K.current,range:Ae},!0,!1,"drag")}}Yt==null||Yt({...y,location:[y.location[0]-q,y.location[1]]})}},[s,q,le,K,yt,Yt,Me,Kt,je,fn,To,Mt]),Yf=g.useCallback(()=>{var Y,Z;const y=$a.current;if(y===void 0)return;const[A,F]=y.scrollEdge;let[U,B]=y.location;const J=en.current;A===-1?U=((Z=(Y=J.extras)==null?void 0:Y.freezeRegion)==null?void 0:Z.x)??J.x:A===1&&(U=J.x+J.width),F===-1?B=Math.max(0,J.y):F===1&&(B=Math.min(je-1,J.y+J.height)),U=Un(U,0,xt.length-1),B=Un(B,0,je-1),Na({...y,location:[U,B]})},[xt.length,Na,je]);q0(Hf,Ut,Yf);const Zn=g.useCallback(y=>{if(K.current===void 0)return;const[A,F]=y,[U,B]=K.current.cell,J=K.current.range;let Y=J.x,Z=J.x+J.width,ee=J.y,ne=J.y+J.height;const[we,Le]=To(B)??[0,je-1],ye=Le+1;if(F!==0)switch(F){case 2:{ne=ye,ee=B,on(0,ne,"vertical");break}case-2:{ee=we,ne=B+1,on(0,ee,"vertical");break}case 1:{ee<B?(ee++,on(0,ee,"vertical")):(ne=Math.min(ye,ne+1),on(0,ne,"vertical"));break}case-1:{ne>B+1?(ne--,on(0,ne,"vertical")):(ee=Math.max(we,ee-1),on(0,ee,"vertical"));break}default:io()}if(A!==0)if(A===2)Z=xt.length,Y=U,on(Z-1-q,0,"horizontal");else if(A===-2)Y=q,Z=U+1,on(Y-q,0,"horizontal");else{let Ae=[];if(cn!==void 0){const De=cn({x:Y,y:ee,width:Z-Y-q,height:ne-ee},un.current.signal);typeof De=="object"&&(Ae=tv(De))}if(A===1){let De=!1;if(Y<U){if(Ae.length>0){const mt=lr(Y+1,U+1).find(Ye=>!Ae.includes(Ye-q));mt!==void 0&&(Y=mt,De=!0)}else Y++,De=!0;De&&on(Y,0,"horizontal")}De||(Z=Math.min(xt.length,Z+1),on(Z-1-q,0,"horizontal"))}else if(A===-1){let De=!1;if(Z>U+1){if(Ae.length>0){const mt=lr(Z-1,U,-1).find(Ye=>!Ae.includes(Ye-q));mt!==void 0&&(Z=mt,De=!0)}else Z--,De=!0;De&&on(Z-q,0,"horizontal")}De||(Y=Math.max(q,Y-1),on(Y-q,0,"horizontal"))}else io()}Mt({cell:K.current.cell,range:{x:Y,y:ee,width:Z-Y,height:ne-ee}},!0,!1,"keyboard-select")},[cn,To,K,xt.length,q,je,on,Mt]),Ba=g.useRef(xi);Ba.current=xi;const Ir=g.useCallback((y,A,F,U)=>{const B=bn-(F?0:1);y=Un(y,q,Jt.length-1+q),A=Un(A,0,B);const J=or==null?void 0:or[0],Y=or==null?void 0:or[1];if(y===J&&A===Y)return!1;if(U&&K.current!==void 0){const Z=[...K.current.rangeStack];(K.current.range.width>1||K.current.range.height>1)&&Z.push(K.current.range),Se({...K,current:{cell:[y,A],range:{x:y,y:A,width:1,height:1},rangeStack:Z}},!0)}else Mt({cell:[y,A],range:{x:y,y:A,width:1,height:1}},!0,!1,"keyboard-nav");return c.current!==void 0&&c.current[0]===y&&c.current[1]===A&&(c.current=void 0),Ba.current&&on(y-q,A),!0},[bn,q,Jt.length,or,K,on,Se,Mt]),Xf=g.useCallback((y,A)=>{(i==null?void 0:i.cell)!==void 0&&y!==void 0&&hi(y)&&(Nn([{location:i.cell,value:y}]),window.requestAnimationFrame(()=>{var B;(B=Tn.current)==null||B.damage([{cell:i.cell}])})),kn(!0),o(void 0);const[F,U]=A;if(K.current!==void 0&&(F!==0||U!==0)){const B=K.current.cell[1]===bn-1&&y!==void 0;Ir(Un(K.current.cell[0]+F,0,xt.length-1),Un(K.current.cell[1]+U,0,bn-1),B,!1)}_==null||_(y,A)},[i==null?void 0:i.cell,kn,K,_,Nn,bn,Ir,xt.length]),Gf=g.useMemo(()=>`gdg-overlay-${ev++}`,[]),Br=g.useCallback(y=>{var F,U,B,J;kn();const A=[];for(let Y=y.x;Y<y.x+y.width;Y++)for(let Z=y.y;Z<y.y+y.height;Z++){const ee=M([Y-q,Z]);if(!ee.allowOverlay&&ee.kind!==te.Boolean)continue;let ne;if(ee.kind===te.Custom){const we=St(ee),Le=(F=we==null?void 0:we.provideEditor)==null?void 0:F.call(we,ee);(we==null?void 0:we.onDelete)!==void 0?ne=we.onDelete(ee):Dg(Le)&&(ne=(U=Le==null?void 0:Le.deletedValue)==null?void 0:U.call(Le,ee))}else if(hi(ee)&&ee.allowOverlay||ee.kind===te.Boolean){const we=St(ee);ne=(B=we==null?void 0:we.onDelete)==null?void 0:B.call(we,ee)}ne!==void 0&&!mi(ne)&&hi(ne)&&A.push({location:[Y,Z],value:ne})}Nn(A),(J=Tn.current)==null||J.damage(A.map(Y=>({cell:Y.location})))},[kn,M,St,Nn,q]),Li=i!==void 0,Sl=g.useCallback(y=>{var mt,Ye;const A=()=>{y.stopPropagation(),y.preventDefault()},F={didMatch:!1},{bounds:U}=y,B=K.columns,J=K.rows,Y=Vn;if(!Li&&ct(Y.clear,y,F))Se(Wo,!1),kt==null||kt();else if(!Li&&ct(Y.selectAll,y,F))Se({columns:pt.empty(),rows:pt.empty(),current:{cell:((mt=K.current)==null?void 0:mt.cell)??[q,0],range:{x:q,y:0,width:w.length,height:je},rangeStack:[]}},!1);else if(ct(Y.search,y,F))(Ye=a==null?void 0:a.current)==null||Ye.focus({preventScroll:!0}),ii(!0);else if(ct(Y.delete,y,F)){const Fe=(Te==null?void 0:Te(K))??!0;if(Fe!==!1){const Rt=Fe===!0?K:Fe;if(Rt.current!==void 0){Br(Rt.current.range);for(const Gt of Rt.current.rangeStack)Br(Gt)}for(const Gt of Rt.rows)Br({x:q,y:Gt,width:w.length,height:1});for(const Gt of Rt.columns)Br({x:Gt,y:0,width:1,height:je})}}if(F.didMatch)return A(),!0;if(K.current===void 0)return!1;let[Z,ee]=K.current.cell;const[,ne]=K.current.cell;let we=!1,Le=!1;if(ct(Y.scrollToSelectedCell,y,F)?Po.current(Z-q,ee):it!=="none"&&ct(Y.selectColumn,y,F)?B.hasIndex(Z)?Ke(B.remove(Z),void 0,!0):it==="single"?Ke(pt.fromSingleSelection(Z),void 0,!0):Ke(void 0,Z,!0):le!=="none"&&ct(Y.selectRow,y,F)?J.hasIndex(ee)?Me(J.remove(ee),void 0,!0):le==="single"?Me(pt.fromSingleSelection(ee),void 0,!0):Me(void 0,ee,!0):!Li&&U!==void 0&&ct(Y.activateCell,y,F)?ee===je&&Kt?window.setTimeout(()=>{const Fe=Eo(Z);si(Fe??Z)},0):(S==null||S([Z-q,ee]),ai(U,!0)):K.current.range.height>1&&ct(Y.downFill,y,F)?yl():K.current.range.width>1&&ct(Y.rightFill,y,F)?wl():ct(Y.goToNextPage,y,F)?ee+=Math.max(1,en.current.height-4):ct(Y.goToPreviousPage,y,F)?ee-=Math.max(1,en.current.height-4):ct(Y.goToFirstCell,y,F)?(o(void 0),ee=0,Z=0):ct(Y.goToLastCell,y,F)?(o(void 0),ee=Number.MAX_SAFE_INTEGER,Z=Number.MAX_SAFE_INTEGER):ct(Y.selectToFirstCell,y,F)?(o(void 0),Zn([-2,-2])):ct(Y.selectToLastCell,y,F)?(o(void 0),Zn([2,2])):Li?(ct(Y.closeOverlay,y,F)&&o(void 0),ct(Y.acceptOverlayDown,y,F)&&(o(void 0),ee++),ct(Y.acceptOverlayUp,y,F)&&(o(void 0),ee--),ct(Y.acceptOverlayLeft,y,F)&&(o(void 0),Z--),ct(Y.acceptOverlayRight,y,F)&&(o(void 0),Z++)):(ct(Y.goDownCell,y,F)?ee+=1:ct(Y.goUpCell,y,F)?ee-=1:ct(Y.goRightCell,y,F)?Z+=1:ct(Y.goLeftCell,y,F)?Z-=1:ct(Y.goDownCellRetainSelection,y,F)?(ee+=1,we=!0):ct(Y.goUpCellRetainSelection,y,F)?(ee-=1,we=!0):ct(Y.goRightCellRetainSelection,y,F)?(Z+=1,we=!0):ct(Y.goLeftCellRetainSelection,y,F)?(Z-=1,we=!0):ct(Y.goToLastRow,y,F)?ee=je-1:ct(Y.goToFirstRow,y,F)?ee=Number.MIN_SAFE_INTEGER:ct(Y.goToLastColumn,y,F)?Z=Number.MAX_SAFE_INTEGER:ct(Y.goToFirstColumn,y,F)?Z=Number.MIN_SAFE_INTEGER:(yt==="rect"||yt==="multi-rect")&&(ct(Y.selectGrowDown,y,F)?Zn([0,1]):ct(Y.selectGrowUp,y,F)?Zn([0,-1]):ct(Y.selectGrowRight,y,F)?Zn([1,0]):ct(Y.selectGrowLeft,y,F)?Zn([-1,0]):ct(Y.selectToLastRow,y,F)?Zn([0,2]):ct(Y.selectToFirstRow,y,F)?Zn([0,-2]):ct(Y.selectToLastColumn,y,F)?Zn([2,0]):ct(Y.selectToFirstColumn,y,F)&&Zn([-2,0])),Le=F.didMatch),Kn!==void 0&&Kn!=="normal"&&ee!==ne){const Fe=Kn==="skip-up"||Kn==="skip"||Kn==="block",Rt=Kn==="skip-down"||Kn==="skip"||Kn==="block",Gt=ee<ne;if(Gt&&Fe){for(;ee>=0&&$r(ee).isGroupHeader;)ee--;ee<0&&(ee=ne)}else if(!Gt&&Rt){for(;ee<je&&$r(ee).isGroupHeader;)ee++;ee>=je&&(ee=ne)}}const Ae=Ir(Z,ee,!1,we),De=F.didMatch;return De&&(Ae||!Le||jt)&&A(),De},[Kn,Li,K,Vn,it,le,yt,q,$r,je,Ir,Se,kt,w.length,Te,jt,Br,Ke,Me,Kt,Eo,si,S,ai,yl,wl,Zn]),Fi=g.useCallback(y=>{let A=!1;if(W!==void 0&&W({...y,cancel:()=>{A=!0}}),A||Sl(y)||K.current===void 0)return;const[F,U]=K.current.cell,B=en.current;if(ze&&!y.metaKey&&!y.ctrlKey&&K.current!==void 0&&y.key.length===1&&/[ -~]/g.test(y.key)&&y.bounds!==void 0&&Zi(M([F-q,Math.max(0,Math.min(U,je-1))]))){if((!Kt||U!==je)&&(B.y>U||U>B.y+B.height||B.x>F||F>B.x+B.width))return;ai(y.bounds,!0,y.key),y.stopPropagation(),y.preventDefault()}},[ze,W,Sl,K,M,q,je,Kt,ai]),jf=g.useCallback((y,A)=>{const F=y.location[0]-q;if(y.kind==="header"&&(re==null||re(F,{...y,preventDefault:A})),y.kind===qn){if(F<0)return;j==null||j(F,{...y,preventDefault:A})}if(y.kind==="cell"){const[U,B]=y.location;N==null||N([F,B],{...y,preventDefault:A}),_m(K,y.location)||Ir(U,B,!1,!1)}},[K,N,j,re,q,Ir]),Wa=g.useCallback(async y=>{var Y,Z,ee;if(!Vn.paste)return;function A(ne,we,Le,ye){var De,mt;const Ae=typeof Le=="object"?(Le==null?void 0:Le.join(`
`))??"":(Le==null?void 0:Le.toString())??"";if(!mi(ne)&&Zi(ne)&&ne.readonly!==!0){const Ye=E==null?void 0:E(Ae,ne);if(Ye!==void 0&&hi(Ye))return{location:we,value:Ye};const Fe=St(ne);if(Fe===void 0)return;if(Fe.kind===te.Custom){Fn(ne.kind===te.Custom);const Rt=(De=Fe.onPaste)==null?void 0:De.call(Fe,Ae,ne.data);return Rt===void 0?void 0:{location:we,value:{...ne,data:Rt}}}else{const Rt=(mt=Fe.onPaste)==null?void 0:mt.call(Fe,Ae,ne,{formatted:ye,formattedString:typeof ye=="string"?ye:ye==null?void 0:ye.join(`
`),rawValue:Le});return Rt===void 0?void 0:(Fn(Rt.kind===ne.kind),{location:we,value:Rt})}}}const F=K.columns,U=K.rows,B=((Y=Ut.current)==null?void 0:Y.contains(document.activeElement))===!0||((Z=l.current)==null?void 0:Z.contains(document.activeElement))===!0;let J;if(K.current!==void 0?J=[K.current.range.x,K.current.range.y]:F.length===1?J=[F.first()??0,0]:U.length===1&&(J=[q,U.first()??0]),B&&J!==void 0){let ne,we;const Le="text/plain",ye="text/html";if(navigator.clipboard.read!==void 0){const Ye=await navigator.clipboard.read();for(const Fe of Ye){if(Fe.types.includes(ye)){const Gt=await(await Fe.getType(ye)).text(),an=Tu(Gt);if(an!==void 0){ne=an;break}}Fe.types.includes(Le)&&(we=await(await Fe.getType(Le)).text())}}else if(navigator.clipboard.readText!==void 0)we=await navigator.clipboard.readText();else if(y!==void 0&&(y==null?void 0:y.clipboardData)!==null){if(y.clipboardData.types.includes(ye)){const Ye=y.clipboardData.getData(ye);ne=Tu(Ye)}ne===void 0&&y.clipboardData.types.includes(Le)&&(we=y.clipboardData.getData(Le))}else return;const[Ae,De]=J,mt=[];do{if(nt===void 0){const Ye=Pn(J),Fe=we??(ne==null?void 0:ne.map(Gt=>Gt.map(an=>an.rawValue).join("	")).join("	"))??"",Rt=A(Ye,J,Fe,void 0);Rt!==void 0&&mt.push(Rt);break}if(ne===void 0){if(we===void 0)return;ne=z0(we)}if(nt===!1||typeof nt=="function"&&(nt==null?void 0:nt([J[0]-q,J[1]],ne.map(Ye=>Ye.map(Fe=>{var Rt;return((Rt=Fe.rawValue)==null?void 0:Rt.toString())??""}))))!==!0)return;for(const[Ye,Fe]of ne.entries()){if(Ye+De>=je)break;for(const[Rt,Gt]of Fe.entries()){const an=[Rt+Ae,Ye+De],[Jn,ar]=an;if(Jn>=xt.length||ar>=bn)continue;const Qn=Pn(an),sr=A(Qn,an,Gt.rawValue,Gt.formatted);sr!==void 0&&mt.push(sr)}}}while(!1);Nn(mt),(ee=Tn.current)==null||ee.damage(mt.map(Ye=>({cell:Ye.location})))}},[E,St,Pn,K,Vn.paste,Ut,xt.length,Nn,bn,nt,q,je]);gn("paste",Wa,d,!1,!0);const Ai=g.useCallback(async(y,A)=>{var Y,Z;if(!Vn.copy)return;const F=A===!0||((Y=Ut.current)==null?void 0:Y.contains(document.activeElement))===!0||((Z=l.current)==null?void 0:Z.contains(document.activeElement))===!0,U=K.columns,B=K.rows,J=(ee,ne)=>{if(!be)Ou(ee,ne,y);else{const we=ne.map(Le=>({kind:te.Text,data:w[Le].title,displayData:w[Le].title,allowOverlay:!1}));Ou([we,...ee],ne,y)}};if(F&&cn!==void 0){if(K.current!==void 0){let ee=cn(K.current.range,un.current.signal);typeof ee!="object"&&(ee=await ee()),J(ee,lr(K.current.range.x-q,K.current.range.x+K.current.range.width-q))}else if(B!==void 0&&B.length>0){const ne=[...B].map(we=>{const Le=cn({x:q,y:we,width:w.length,height:1},un.current.signal);return typeof Le=="object"?Le[0]:Le().then(ye=>ye[0])});if(ne.some(we=>we instanceof Promise)){const we=await Promise.all(ne);J(we,lr(w.length))}else J(ne,lr(w.length))}else if(U.length>0){const ee=[],ne=[];for(const we of U){let Le=cn({x:we,y:0,width:1,height:je},un.current.signal);typeof Le!="object"&&(Le=await Le()),ee.push(Le),ne.push(we-q)}if(ee.length===1)J(ee[0],ne);else{const we=ee.reduce((Le,ye)=>Le.map((Ae,De)=>[...Ae,...ye[De]]));J(we,ne)}}}},[w,cn,K,Vn.copy,q,Ut,je,be]);gn("copy",Ai,d,!1,!1);const Kf=g.useCallback(async y=>{var F,U;if(!(!Vn.cut||!(((F=Ut.current)==null?void 0:F.contains(document.activeElement))===!0||((U=l.current)==null?void 0:U.contains(document.activeElement))===!0))&&(await Ai(y),K.current!==void 0)){let B={current:{cell:K.current.cell,range:K.current.range,rangeStack:[]},rows:pt.empty(),columns:pt.empty()};const J=Te==null?void 0:Te(B);if(J===!1||(B=J===!0?B:J,B.current===void 0))return;Br(B.current.range)}},[Br,K,Vn.cut,Ai,Ut,Te]);gn("cut",Kf,d,!1,!1);const Zf=g.useCallback((y,A)=>{if(he!==void 0){q!==0&&(y=y.map(B=>[B[0]-q,B[1]])),he(y,A);return}if(y.length===0||A===-1)return;const[F,U]=y[A];c.current!==void 0&&c.current[0]===F&&c.current[1]===U||(c.current=[F,U],Ir(F,U,!1,!1))},[he,q,Ir]),[Do,Oo]=((Tl=zt==null?void 0:zt.current)==null?void 0:Tl.cell)??[],Po=g.useRef(on);Po.current=on,g.useLayoutEffect(()=>{var y,A,F,U;Ba.current&&!Mr.current&&Do!==void 0&&Oo!==void 0&&(Do!==((A=(y=ae.current)==null?void 0:y.current)==null?void 0:A.cell[0])||Oo!==((U=(F=ae.current)==null?void 0:F.current)==null?void 0:U.cell[1]))&&Po.current(Do,Oo),Mr.current=!1},[Do,Oo]);const xl=K.current!==void 0&&(K.current.cell[0]>=xt.length||K.current.cell[1]>=bn);g.useLayoutEffect(()=>{xl&&Se(Wo,!1)},[xl,Se]);const Jf=g.useMemo(()=>Kt===!0&&(Pe==null?void 0:Pe.tint)===!0?pt.fromSingleSelection(bn-1):pt.empty(),[bn,Kt,Pe==null?void 0:Pe.tint]),Qf=g.useCallback(y=>typeof st=="boolean"?st:(st==null?void 0:st(y-q))??!0,[q,st]),eh=g.useMemo(()=>{if(Aa===void 0||l.current===null)return null;const{bounds:y,group:A}=Aa,F=l.current.getBoundingClientRect();return g.createElement(E0,{bounds:y,group:A,canvasBounds:F,onClose:()=>Ha(void 0),onFinish:U=>{Ha(void 0),se==null||se(A,U)}})},[se,Aa]),th=Math.min(xt.length,de+($n?1:0));g.useImperativeHandle(t,()=>({appendRow:(y,A)=>si(y+q,A),updateCells:y=>{var A;return q!==0&&(y=y.map(F=>({cell:[F.cell[0]+q,F.cell[1]]}))),(A=Tn.current)==null?void 0:A.damage(y)},getBounds:(y,A)=>{var F;if(!((l==null?void 0:l.current)===null||(Ut==null?void 0:Ut.current)===null)){if(y===void 0&&A===void 0){const U=l.current.getBoundingClientRect(),B=U.width/Ut.current.clientWidth;return{x:U.x-Ut.current.scrollLeft*B,y:U.y-Ut.current.scrollTop*B,width:Ut.current.scrollWidth*B,height:Ut.current.scrollHeight*B}}return(F=Tn.current)==null?void 0:F.getBounds((y??0)+q,A)}},focus:()=>{var y;return(y=Tn.current)==null?void 0:y.focus()},emit:async y=>{switch(y){case"delete":Fi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!1,key:"Delete",keyCode:46,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-right":Fi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"r",keyCode:82,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"fill-down":Fi({bounds:void 0,cancel:()=>{},stopPropagation:()=>{},preventDefault:()=>{},ctrlKey:!0,key:"d",keyCode:68,metaKey:!1,shiftKey:!1,altKey:!1,rawEvent:void 0,location:void 0});break;case"copy":await Ai(void 0,!0);break;case"paste":await Wa();break}},scrollTo:on,remeasureColumns:y=>{for(const A of y)Io(A+q)}}),[si,Io,Ut,Ai,Fi,Wa,q,on]);const[kl,Ml]=or??[],nh=g.useCallback(y=>{const[A,F]=y;if(F===-1){it!=="none"&&(Ke(pt.fromSingleSelection(A),void 0,!1),kn());return}kl===A&&Ml===F||(Mt({cell:y,range:{x:A,y:F,width:1,height:1}},!0,!1,"keyboard-nav"),on(A,F))},[it,kn,on,kl,Ml,Mt,Ke]),[rh,ih]=g.useState(!1),Rl=g.useRef(Jc(y=>{ih(y)},5)),oh=g.useCallback(()=>{Rl.current(!0),K.current===void 0&&K.columns.length===0&&K.rows.length===0&&s===void 0&&Mt({cell:[q,So],range:{x:q,y:So,width:1,height:1}},!0,!1,"keyboard-select")},[So,K,s,q,Mt]),ah=g.useCallback(()=>{Rl.current(!1)},[]),[sh,lh]=g.useMemo(()=>{let y;const A=(_t==null?void 0:_t.scrollbarWidthOverride)??Ss(),F=je+(Kt?1:0);if(typeof Gn=="number")y=Et+F*Gn;else{let B=0;const J=Math.min(F,10);for(let Y=0;Y<J;Y++)B+=Gn(Y);B=Math.floor(B/J),y=Et+F*B}y+=A;const U=xt.reduce((B,J)=>J.width+B,0)+A;return[`${Math.min(1e5,U)}px`,`${Math.min(1e5,y)}px`]},[xt,_t==null?void 0:_t.scrollbarWidthOverride,Gn,je,Kt,Et]),uh=g.useMemo(()=>Xm(qe),[qe]);return g.createElement(Sd.Provider,{value:qe},g.createElement(B0,{style:uh,className:X,inWidth:p??sh,inHeight:v??lh},g.createElement(x0,{fillHandle:Bt,drawFocusRing:Ar,experimental:_t,fixedShadowX:Vt,fixedShadowY:$t,getRowThemeOverride:zn,headerIcons:Cn,imageWindowLoader:nr,initialSize:Hn,isDraggable:V,onDragLeave:Qe,onRowMoved:Ue,overscrollX:Ta,overscrollY:Da,preventDiagonalScrolling:Wt,rightElement:dn,rightElementProps:Zt,smoothScrollX:Sn,smoothScrollY:vn,className:X,enableGroups:xn,onCanvasFocused:oh,onCanvasBlur:ah,canvasRef:l,onContextMenu:jf,theme:qe,cellXOffset:Lf,cellYOffset:So,accessibilityHeight:zr.height,onDragEnd:qf,columns:xt,nonGrowWidth:jn,drawHeader:rt,onColumnProposeMove:I,drawCell:at,disabledRows:Jf,freezeColumns:th,lockColumns:q,firstColAccessible:q===0,getCellContent:Pn,minColumnWidth:ir,maxColumnWidth:Hr,searchInputRef:a,showSearch:Ti,onSearchClose:Oa,highlightRegions:Ff,getCellsForSelection:cn,getGroupDetails:La,headerHeight:Ii,isFocused:rh,groupHeaderHeight:xn?yo:0,freezeTrailingRows:Ft+(Kt&&(Pe==null?void 0:Pe.sticky)===!0?1:0),hasAppendRow:Kt,onColumnResize:ke,onColumnResizeEnd:me,onColumnResizeStart:Re,onCellFocused:nh,onColumnMoved:Wf,onDragStart:Uf,onHeaderMenuClick:$f,onHeaderIndicatorClick:Nf,onItemHovered:Na,isFilling:(s==null?void 0:s.fillHandle)===!0,onMouseMove:Vf,onKeyDown:Fi,onKeyUp:ce,onMouseDown:Af,onMouseUp:zf,onDragOverCell:Xt,onDrop:Pt,onSearchResultsChanged:Zf,onVisibleRegionChanged:Bf,clientSize:ht,rowHeight:Gn,searchResults:Q,searchValue:P,onSearchValueChange:H,rows:bn,scrollRef:Ut,selection:K,translateX:zr.tx,translateY:zr.ty,verticalBorder:Qf,gridRef:Tn,getCellRenderer:St,resizeIndicator:Ia}),eh,i!==void 0&&g.createElement(g.Suspense,{fallback:null},g.createElement(Q0,{...i,validateCell:$,bloom:D,id:Gf,getCellRenderer:St,className:(_t==null?void 0:_t.isSubGrid)===!0?"click-outside-ignore":void 0,provideEditor:yn,imageEditorOverride:h,onFinishEditing:Xf,markdownDivCreateNode:m,isOutsideClick:Sr,customEventTarget:_t==null?void 0:_t.eventTarget}))))},rv=g.forwardRef(nv),Vd=20;function Lu(e){const{cell:t,posX:n,posY:r,bounds:i,theme:o}=e,{width:a,height:l,x:s,y:u}=i,c=t.maxSize??Vd,d=Math.floor(i.y+l/2),h=td(c,l,o.cellVerticalPadding),f=ed(t.contentAlign??"center",s,a,o.cellHorizontalPadding,h),m=Qc(f,d,h),p=nd(s+n,u+r,m);return zs(t)&&p}const iv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??"false"},kind:te.Boolean,needsHover:!0,useLabel:!1,needsHoverPosition:!0,measure:()=>50,draw:e=>ov(e,e.cell.data,zs(e.cell),e.cell.maxSize??Vd,e.cell.hoverEffectIntensity??.35),onDelete:e=>({...e,data:!1}),onSelect:e=>{Lu(e)&&e.preventDefault()},onClick:e=>{if(Lu(e))return{...e.cell,data:Ad(e.cell.data)}},onPaste:(e,t)=>{let n=ra;return e.toLowerCase()==="true"?n=!0:e.toLowerCase()==="false"?n=!1:e.toLowerCase()==="indeterminate"&&(n=Hs),n===t.data?void 0:{...t,data:n}}};function ov(e,t,n,r,i){if(!n&&t===ra)return;const{ctx:o,hoverAmount:a,theme:l,rect:s,highlighted:u,hoverX:c,hoverY:d,cell:{contentAlign:h}}=e,{x:f,y:m,width:p,height:v}=s;let w=!1;if(i>0){let b=n?1-i+i*a:.4;if(t===ra&&(b*=a),b===0)return;b<1&&(w=!0,o.globalAlpha=b)}Gs(o,l,t,f,m,p,v,u,c,d,r,h),w&&(o.globalAlpha=1)}const av=mn("div")({name:"BubblesOverlayEditorStyle",class:"gdg-b1ygi5by",propsAsIs:!1}),sv=e=>{const{bubbles:t}=e;return g.createElement(av,null,t.map((n,r)=>g.createElement("div",{key:r,className:"boe-bubble"},n)),g.createElement("textarea",{className:"gdg-input",autoFocus:!0}))},lv={getAccessibilityString:e=>rd(e.data),kind:te.Bubble,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce((r,i)=>e.measureText(i).width+r+20,0)+2*n.cellHorizontalPadding-4,draw:e=>cv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return g.createElement(sv,{bubbles:t.data})},onPaste:()=>{}},uv=4;function cv(e,t){const{rect:n,theme:r,ctx:i,highlighted:o}=e,{x:a,y:l,width:s,height:u}=n,c=20,d=8,h=uv;let f=a+r.cellHorizontalPadding;const m=[];for(const p of t){if(f>a+s)break;const v=Qr(p,i,r.baseFontFull).width;m.push({x:f,width:v}),f+=v+d*2+h}i.beginPath();for(const p of m)cr(i,p.x,l+(u-c)/2,p.width+d*2,c,r.roundingRadius??c/2);i.fillStyle=o?r.bgBubbleSelected:r.bgBubble,i.fill();for(const[p,v]of m.entries())i.beginPath(),i.fillStyle=r.textBubble,i.fillText(t[p],v.x+d,l+u/2+dr(i,r))}const dv=mn("div")({name:"DrilldownOverlayEditorStyle",class:"gdg-d4zsq0x",propsAsIs:!1}),fv=e=>{const{drilldowns:t}=e;return g.createElement(dv,null,t.map((n,r)=>g.createElement("div",{key:r,className:"doe-bubble"},n.img!==void 0&&g.createElement("img",{src:n.img}),g.createElement("div",null,n.text))))},hv={getAccessibilityString:e=>rd(e.data.map(t=>t.text)),kind:te.Drilldown,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:(e,t,n)=>t.data.reduce((r,i)=>e.measureText(i.text).width+r+20+(i.img!==void 0?18:0),0)+2*n.cellHorizontalPadding-4,draw:e=>pv(e,e.cell.data),provideEditor:()=>e=>{const{value:t}=e;return g.createElement(fv,{drilldowns:t.data})},onPaste:()=>{}},gv=4,us={};function mv(e,t,n,r){const i=Math.ceil(window.devicePixelRatio),o=5,a=n-o*2,l=4,s=n*i,u=r+o,c=r*3,d=(c+o*2)*i,h=`${e},${t},${i},${n}`;if(us[h]!==void 0)return{el:us[h],height:s,width:d,middleWidth:l*i,sideWidth:u*i,padding:o*i,dpr:i};const f=document.createElement("canvas"),m=f.getContext("2d");return m===null?null:(f.width=d,f.height=s,m.scale(i,i),us[h]=f,m.beginPath(),cr(m,o,o,c,a,r),m.shadowColor="rgba(24, 25, 34, 0.4)",m.shadowBlur=1,m.fillStyle=e,m.fill(),m.shadowColor="rgba(24, 25, 34, 0.3)",m.shadowOffsetY=1,m.shadowBlur=5,m.fillStyle=e,m.fill(),m.shadowOffsetY=0,m.shadowBlur=0,m.shadowBlur=0,m.beginPath(),cr(m,o+.5,o+.5,c,a,r),m.strokeStyle=t,m.lineWidth=1,m.stroke(),{el:f,height:s,width:d,sideWidth:u*i,middleWidth:r*i,padding:o*i,dpr:i})}function pv(e,t){const{rect:n,theme:r,ctx:i,imageLoader:o,col:a,row:l}=e,{x:s,width:u}=n,c=r.baseFontFull,d=vd(i,c),h=Math.min(n.height,Math.max(16,Math.ceil(d*r.lineHeight)*2)),f=Math.floor(n.y+(n.height-h)/2),m=h-10,p=8,v=gv;let w=s+r.cellHorizontalPadding;const b=r.roundingRadius??6,M=mv(r.bgCell,r.drilldownBorder,h,b),O=[];for(const S of t){if(w>s+u)break;const _=Qr(S.text,i,c).width;let E=0;S.img!==void 0&&o.loadOrGetImage(S.img,a,l)!==void 0&&(E=m-8+4);const x=_+E+p*2;O.push({x:w,width:x}),w+=x+v}if(M!==null){const{el:S,height:R,middleWidth:_,sideWidth:E,width:x,dpr:L,padding:D}=M,C=E/L,I=D/L;for(const T of O){const k=Math.floor(T.x),z=Math.floor(T.width),N=z-(C-I)*2;i.imageSmoothingEnabled=!1,i.drawImage(S,0,0,E,R,k-I,f,C,h),N>0&&i.drawImage(S,E,0,_,R,k+(C-I),f,N,h),i.drawImage(S,x-E,0,E,R,k+z-(C-I),f,C,h),i.imageSmoothingEnabled=!0}}i.beginPath();for(const[S,R]of O.entries()){const _=t[S];let E=R.x+p;if(_.img!==void 0){const x=o.loadOrGetImage(_.img,a,l);if(x!==void 0){const L=m-8;let D=0,C=0,I=x.width,T=x.height;I>T?(D+=(I-T)/2,I=T):T>I&&(C+=(T-I)/2,T=I),i.beginPath(),cr(i,E,f+h/2-L/2,L,L,r.roundingRadius??3),i.save(),i.clip(),i.drawImage(x,D,C,I,T,E,f+h/2-L/2,L,L),i.restore(),E+=L+4}}i.beginPath(),i.fillStyle=r.textBubble,i.fillText(_.text,E,f+h/2+dr(i,r))}}const vv={getAccessibilityString:e=>e.data.join(", "),kind:te.Image,needsHover:!1,useLabel:!1,needsHoverPosition:!1,draw:e=>bv(e,e.cell.displayData??e.cell.data,e.cell.rounding??e.theme.roundingRadius??4,e.cell.contentAlign),measure:(e,t)=>t.data.length*50,onDelete:e=>({...e,data:[]}),provideEditor:()=>e=>{const{value:t,onFinishedEditing:n,imageEditorOverride:r}=e,i=r??rm;return g.createElement(i,{urls:t.data,canWrite:t.readonly!==!0,onCancel:n,onChange:o=>{n({...t,data:[o]})}})},onPaste:(e,t)=>{e=e.trim();const r=e.split(",").map(i=>{try{return new URL(i),i}catch{return}}).filter(i=>i!==void 0);if(!(r.length===t.data.length&&r.every((i,o)=>i===t.data[o])))return{...t,data:r}}},cs=4;function bv(e,t,n,r){const{rect:i,col:o,row:a,theme:l,ctx:s,imageLoader:u}=e,{x:c,y:d,height:h,width:f}=i,m=h-l.cellVerticalPadding*2,p=[];let v=0;for(let b=0;b<t.length;b++){const M=t[b];if(M.length===0)continue;const O=u.loadOrGetImage(M,o,a);if(O!==void 0){p[b]=O;const S=O.width*(m/O.height);v+=S+cs}}if(v===0)return;v-=cs;let w=c+l.cellHorizontalPadding;r==="right"?w=Math.floor(c+f-l.cellHorizontalPadding-v):r==="center"&&(w=Math.floor(c+f/2-v/2));for(const b of p){if(b===void 0)continue;const M=b.width*(m/b.height);n>0&&(s.beginPath(),cr(s,w,d+l.cellVerticalPadding,M,m,n),s.save(),s.clip()),s.drawImage(b,w,d+l.cellVerticalPadding,M,m),n>0&&s.restore(),w+=M+cs}}function wv(e,t){let n=e*49632+t*325176;return n^=n<<13,n^=n>>17,n^=n<<5,n/4294967295*2}const yv={getAccessibilityString:()=>"",kind:te.Loading,needsHover:!1,useLabel:!1,needsHoverPosition:!1,measure:()=>120,draw:e=>{const{cell:t,col:n,row:r,ctx:i,rect:o,theme:a}=e;if(t.skeletonWidth===void 0||t.skeletonWidth===0)return;let l=t.skeletonWidth;t.skeletonWidthVariability!==void 0&&t.skeletonWidthVariability>0&&(l+=Math.round(wv(n,r)*t.skeletonWidthVariability));const s=a.cellHorizontalPadding;l+s*2>=o.width&&(l=o.width-s*2-1);const u=t.skeletonHeight??Math.min(18,o.height-2*a.cellVerticalPadding);cr(i,o.x+s,o.y+(o.height-u)/2,l,u,a.roundingRadius??3),i.fillStyle=Gr(a.textDark,.1),i.fill()},onPaste:()=>{}},Cv=()=>e=>e.targetWidth,Fu=mn("div")({name:"MarkdownOverlayEditorStyle",class:"gdg-m1pnx84e",propsAsIs:!1,vars:{"m1pnx84e-0":[Cv(),"px"]}}),Sv=e=>{const{value:t,onChange:n,forceEditMode:r,createNode:i,targetRect:o,onFinish:a,validatedSelection:l}=e,s=t.data,u=t.readonly===!0,[c,d]=g.useState(s===""||r),h=g.useCallback(()=>{d(m=>!m)},[]),f=s?"gdg-ml-6":"";return c?g.createElement(Fu,{targetWidth:o.width-20},g.createElement(Zr,{autoFocus:!0,highlight:!1,validatedSelection:l,value:s,onKeyDown:m=>{m.key==="Enter"&&m.stopPropagation()},onChange:n}),g.createElement("div",{className:`gdg-edit-icon gdg-checkmark-hover ${f}`,onClick:()=>a(t)},g.createElement(Kg,null))):g.createElement(Fu,{targetWidth:o.width},g.createElement(Sm,{contents:s,createNode:i}),!u&&g.createElement(g.Fragment,null,g.createElement("div",{className:"spacer"}),g.createElement("div",{className:`gdg-edit-icon gdg-edit-hover ${f}`,onClick:h},g.createElement($s,null))),g.createElement("textarea",{className:"gdg-md-edit-textarea gdg-input",autoFocus:!0}))},xv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Markdown,needsHover:!1,needsHoverPosition:!1,drawPrep:ho,measure:(e,t,n)=>{const r=t.data.split(`
`)[0];return e.measureText(r).width+2*n.cellHorizontalPadding},draw:e=>ur(e,e.cell.data,e.cell.contentAlign),onDelete:e=>({...e,data:""}),provideEditor:()=>e=>{const{onChange:t,value:n,target:r,onFinishedEditing:i,markdownDivCreateNode:o,forceEditMode:a,validatedSelection:l}=e;return g.createElement(Sv,{onFinish:i,targetRect:r,value:n,validatedSelection:l,onChange:s=>t({...n,data:s.target.value}),forceEditMode:a,createNode:o})},onPaste:(e,t)=>e===t.data?void 0:{...t,data:e}},kv={getAccessibilityString:e=>e.row.toString(),kind:Yn.Marker,needsHover:!0,needsHoverPosition:!1,drawPrep:Mv,measure:()=>44,draw:e=>Ev(e,e.cell.row,e.cell.checked,e.cell.markerKind,e.cell.drawHandle,e.cell.checkboxStyle),onClick:e=>{const{bounds:t,cell:n,posX:r,posY:i}=e,{width:o,height:a}=t,l=n.drawHandle?7+(o-7)/2:o/2,s=a/2;if(Math.abs(r-l)<=10&&Math.abs(i-s)<=10)return{...n,checked:!n.checked}},onPaste:()=>{}};function Mv(e,t){const{ctx:n,theme:r}=e,i=r.markerFontFull,o=t??{};return(o==null?void 0:o.font)!==i&&(n.font=i,o.font=i),o.deprep=Rv,n.textAlign="center",o}function Rv(e){const{ctx:t}=e;t.textAlign="start"}function Ev(e,t,n,r,i,o){const{ctx:a,rect:l,hoverAmount:s,theme:u}=e,{x:c,y:d,width:h,height:f}=l,m=n?1:r==="checkbox-visible"?.6+.4*s:s;if(r!=="number"&&m>0){a.globalAlpha=m;const p=7*(n?s:1);if(Gs(a,u,n,i?c+p:c,d,i?h-p:h,f,!0,void 0,void 0,18,"center",o),i){a.globalAlpha=s,a.beginPath();for(const v of[3,6])for(const w of[-5,-1,3])a.rect(c+v,d+f/2+w,2,2);a.fillStyle=u.textLight,a.fill(),a.beginPath()}a.globalAlpha=1}if(r==="number"||r==="both"&&!n){const p=t.toString(),v=u.markerFontFull,w=c+h/2;r==="both"&&s!==0&&(a.globalAlpha=1-s),a.fillStyle=u.textLight,a.font=v,a.fillText(p,w,d+f/2+dr(a,v)),s!==0&&(a.globalAlpha=1)}}const Iv={getAccessibilityString:()=>"",kind:Yn.NewRow,needsHover:!0,needsHoverPosition:!1,measure:()=>200,draw:e=>Tv(e,e.cell.hint,e.cell.icon),onPaste:()=>{}};function Tv(e,t,n){const{ctx:r,rect:i,hoverAmount:o,theme:a,spriteManager:l}=e,{x:s,y:u,width:c,height:d}=i;r.beginPath(),r.globalAlpha=o,r.rect(s+1,u+1,c,d-2),r.fillStyle=a.bgHeaderHovered,r.fill(),r.globalAlpha=1,r.beginPath();const h=t!=="";let f=0;if(n!==void 0){const p=d-8,v=s+8/2,w=u+8/2;l.drawSprite(n,"normal",r,v,w,p,a,h?1:o),f=p}else{f=24;const m=12,p=h?m:o*m,v=h?0:(1-o)*m*.5,w=a.cellHorizontalPadding+4;p>0&&(r.moveTo(s+w+v,u+d/2),r.lineTo(s+w+v+p,u+d/2),r.moveTo(s+w+v+p*.5,u+d/2-p*.5),r.lineTo(s+w+v+p*.5,u+d/2+p*.5),r.lineWidth=2,r.strokeStyle=a.bgIconHeader,r.lineCap="round",r.stroke())}r.fillStyle=a.textMedium,r.fillText(t,f+s+a.cellHorizontalPadding+.5,u+d/2+dr(r,a)),r.beginPath()}function $d(e,t,n,r,i,o,a){e.textBaseline="alphabetic";const l=Dv(e,i,r,t,(n==null?void 0:n.fullSize)??!1);e.beginPath(),cr(e,l.x,l.y,l.width,l.height,t.roundingRadius??4),e.globalAlpha=o,e.fillStyle=(n==null?void 0:n.bgColor)??Gr(t.textDark,.1),e.fill(),e.globalAlpha=1,e.fillStyle=t.textDark,e.textBaseline="middle",a==null||a("text")}function Dv(e,t,n,r,i){const o=r.cellHorizontalPadding,a=r.cellVerticalPadding;if(i)return{x:t.x+o/2,y:t.y+a/2+1,width:t.width-o,height:t.height-a-1};const l=Qr(n,e,r.baseFontFull,"alphabetic"),s=t.height-a,u=Math.min(s,l.actualBoundingBoxAscent*2.5);return{x:t.x+o/2,y:t.y+(t.height-u)/2+1,width:l.width+o*3,height:u-1}}const Ov=g.lazy(async()=>await Ls(()=>import("./number-overlay-editor.CSeVhHRU.js"),__vite__mapDeps([14,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)),Pv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Number,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,useLabel:!0,drawPrep:ho,draw:e=>{const{hoverAmount:t,cell:n,ctx:r,theme:i,rect:o,overrideCursor:a}=e,{hoverEffect:l,displayData:s,hoverEffectTheme:u}=n;l===!0&&t>0&&$d(r,i,u,s,o,t,a),ur(e,e.cell.displayData,e.cell.contentAlign)},measure:(e,t,n)=>e.measureText(t.displayData).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:void 0}),provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return g.createElement(g.Suspense,{fallback:null},g.createElement(Ov,{highlight:t,disabled:r.readonly===!0,value:r.data,fixedDecimals:r.fixedDecimals,allowNegative:r.allowNegative,thousandSeparator:r.thousandSeparator,decimalSeparator:r.decimalSeparator,validatedSelection:i,onChange:o=>n({...r,data:Number.isNaN(o.floatValue??0)?0:o.floatValue})}))},onPaste:(e,t,n)=>{const r=typeof n.rawValue=="number"?n.rawValue:Number.parseFloat(typeof n.rawValue=="string"?n.rawValue:e);if(!(Number.isNaN(r)||t.data===r))return{...t,data:r,displayData:n.formattedString??t.displayData}}},_v={getAccessibilityString:()=>"",measure:()=>108,kind:te.Protected,needsHover:!1,needsHoverPosition:!1,draw:Lv,onPaste:()=>{}};function Lv(e){const{ctx:t,theme:n,rect:r}=e,{x:i,y:o,height:a}=r;t.beginPath();const l=2.5;let s=i+n.cellHorizontalPadding+l;const u=o+a/2,c=Math.cos(eu(30))*l,d=Math.sin(eu(30))*l;for(let h=0;h<12;h++)t.moveTo(s,u-l),t.lineTo(s,u+l),t.moveTo(s+c,u-d),t.lineTo(s-c,u+d),t.moveTo(s-c,u-d),t.lineTo(s+c,u+d),s+=8;t.lineWidth=1.1,t.lineCap="square",t.strokeStyle=n.textLight,t.stroke()}const Fv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.RowID,needsHover:!1,needsHoverPosition:!1,drawPrep:(e,t)=>ho(e,t,e.theme.textLight),draw:e=>ur(e,e.cell.data,e.cell.contentAlign),measure:(e,t,n)=>e.measureText(t.data).width+n.cellHorizontalPadding*2,provideEditor:()=>e=>{const{isHighlighted:t,onChange:n,value:r,validatedSelection:i}=e;return ie.createElement(Zr,{highlight:t,autoFocus:r.readonly!==!0,disabled:r.readonly!==!1,value:r.data,validatedSelection:i,onChange:o=>n({...r,data:o.target.value})})},onPaste:()=>{}},Av={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Text,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!1,drawPrep:ho,useLabel:!0,draw:e=>{const{cell:t,hoverAmount:n,hyperWrapping:r,ctx:i,rect:o,theme:a,overrideCursor:l}=e,{displayData:s,contentAlign:u,hoverEffect:c,allowWrapping:d,hoverEffectTheme:h}=t;c===!0&&n>0&&$d(i,a,h,s,o,n,l),ur(e,s,u,d,r)},measure:(e,t,n)=>{const r=t.displayData.split(`
`,t.allowWrapping===!0?void 0:1);let i=0;for(const o of r)i=Math.max(i,e.measureText(o).width);return i+2*n.cellHorizontalPadding},onDelete:e=>({...e,data:""}),provideEditor:e=>({disablePadding:e.allowWrapping===!0,editor:t=>{const{isHighlighted:n,onChange:r,value:i,validatedSelection:o}=t;return g.createElement(Zr,{style:e.allowWrapping===!0?{padding:"3px 8.5px"}:void 0,highlight:n,autoFocus:i.readonly!==!0,disabled:i.readonly===!0,altNewline:!0,value:i.data,validatedSelection:o,onChange:a=>r({...i,data:a.target.value})})}}),onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},Hv=mn("div")({name:"UriOverlayEditorStyle",class:"gdg-u1rrojo",propsAsIs:!1}),zv=e=>{const{uri:t,onChange:n,forceEditMode:r,readonly:i,validatedSelection:o,preview:a}=e,[l,s]=g.useState(!i&&(t===""||r)),u=g.useCallback(()=>{s(!0)},[]);return l?g.createElement(Zr,{validatedSelection:o,highlight:!0,autoFocus:!0,value:t,onChange:n}):g.createElement(Hv,null,g.createElement("a",{className:"gdg-link-area",href:t,target:"_blank",rel:"noopener noreferrer"},a),!i&&g.createElement("div",{className:"gdg-edit-icon",onClick:u},g.createElement($s,null)),g.createElement("textarea",{className:"gdg-input",autoFocus:!0}))};function Nd(e,t,n,r){let i=n.cellHorizontalPadding;const o=t.height/2-e.actualBoundingBoxAscent/2,a=e.width,l=e.actualBoundingBoxAscent;return r==="right"?i=t.width-a-n.cellHorizontalPadding:r==="center"&&(i=t.width/2-a/2),{x:i,y:o,width:a,height:l}}function Au(e){const{cell:t,bounds:n,posX:r,posY:i,theme:o}=e,a=t.displayData??t.data;if(t.hoverEffect!==!0||t.onClickUri===void 0)return!1;const l=md(a,o.baseFontFull);if(l===void 0)return!1;const s=Nd(l,n,o,t.contentAlign);return Xr({x:s.x-4,y:s.y-4,width:s.width+8,height:s.height+8},r,i)}const Vv={getAccessibilityString:e=>{var t;return((t=e.data)==null?void 0:t.toString())??""},kind:te.Uri,needsHover:e=>e.hoverEffect===!0,needsHoverPosition:!0,useLabel:!0,drawPrep:ho,draw:e=>{const{cell:t,theme:n,overrideCursor:r,hoverX:i,hoverY:o,rect:a,ctx:l}=e,s=t.displayData??t.data,u=t.hoverEffect===!0;if(r!==void 0&&u&&i!==void 0&&o!==void 0){const c=Qr(s,l,n.baseFontFull),d=Nd(c,a,n,t.contentAlign),{x:h,y:f,width:m,height:p}=d;if(i>=h-4&&i<=h-4+m+8&&o>=f-4&&o<=f-4+p+8){const v=dr(l,n.baseFontFull);r("pointer");const w=5,b=f-v;l.beginPath(),l.moveTo(a.x+h,Math.floor(a.y+b+p+w)+.5),l.lineTo(a.x+h+m,Math.floor(a.y+b+p+w)+.5),l.strokeStyle=n.linkColor,l.stroke(),l.save(),l.fillStyle=e.cellFillColor,ur({...e,rect:{...a,x:a.x-1}},s,t.contentAlign),ur({...e,rect:{...a,x:a.x-2}},s,t.contentAlign),ur({...e,rect:{...a,x:a.x+1}},s,t.contentAlign),ur({...e,rect:{...a,x:a.x+2}},s,t.contentAlign),l.restore()}}l.fillStyle=u?n.linkColor:n.textDark,ur(e,s,t.contentAlign)},onSelect:e=>{Au(e)&&e.preventDefault()},onClick:e=>{var r;const{cell:t}=e;Au(e)&&((r=t.onClickUri)==null||r.call(t,e))},measure:(e,t,n)=>e.measureText(t.displayData??t.data).width+n.cellHorizontalPadding*2,onDelete:e=>({...e,data:""}),provideEditor:e=>t=>{const{onChange:n,value:r,forceEditMode:i,validatedSelection:o}=t;return g.createElement(zv,{forceEditMode:r.readonly!==!0&&(i||e.hoverEffect===!0&&e.onClickUri!==void 0),uri:r.data,preview:r.displayData??r.data,validatedSelection:o,readonly:r.readonly===!0,onChange:a=>n({...r,data:a.target.value})})},onPaste:(e,t,n)=>e===t.data?void 0:{...t,data:e,displayData:n.formattedString??t.displayData}},$v=[kv,Iv,iv,lv,hv,vv,yv,xv,Pv,_v,Fv,Av,Vv];var ds,Hu;function Nv(){if(Hu)return ds;Hu=1;var e=Zc(),t=Ic(),n="Expected a function";function r(i,o,a){var l=!0,s=!0;if(typeof i!="function")throw new TypeError(n);return t(a)&&(l="leading"in a?!!a.leading:l,s="trailing"in a?!!a.trailing:s),e(i,o,{leading:l,maxWait:o,trailing:s})}return ds=r,ds}var Bv=Nv();const Wv=wr(Bv),fs=[];class Uv extends wd{constructor(){super(...arguments);ft(this,"imageLoaded",()=>{});ft(this,"loadedLocations",[]);ft(this,"cache",{});ft(this,"sendLoaded",Wv(()=>{this.imageLoaded(new no(this.loadedLocations)),this.loadedLocations=[]},20));ft(this,"clearOutOfWindow",()=>{const n=Object.keys(this.cache);for(const r of n){const i=this.cache[r];let o=!1;for(let a=0;a<i.cells.length;a++){const l=i.cells[a];if(this.isInWindow(l)){o=!0;break}}o?i.cells=i.cells.filter(this.isInWindow):(i.cancel(),delete this.cache[r])}})}setCallback(n){this.imageLoaded=n}loadImage(n,r,i,o){let a=!1;const l=fs.pop()??new Image;let s=!1;const u={img:void 0,cells:[tr(r,i)],url:n,cancel:()=>{s||(s=!0,fs.length<12?fs.unshift(l):a||(l.src=""))}},c=new Promise(d=>l.addEventListener("load",()=>d(null)));requestAnimationFrame(async()=>{try{l.src=n,await c,await l.decode();const d=this.cache[o];if(d!==void 0&&!s){d.img=l;for(const h of d.cells)this.loadedLocations.push(Xs(h));a=!0,this.sendLoaded()}}catch{u.cancel()}}),this.cache[o]=u}loadOrGetImage(n,r,i){const o=n,a=this.cache[o];if(a!==void 0){const l=tr(r,i);return a.cells.includes(l)||a.cells.push(l),a.img}else this.loadImage(n,r,i,o)}}const qv=(e,t)=>{const n=g.useMemo(()=>({...Fp,...e.headerIcons}),[e.headerIcons]),r=g.useMemo(()=>e.imageWindowLoader??new Uv,[e.imageWindowLoader]);return g.createElement(rv,{...e,renderers:$v,headerIcons:n,ref:t,imageWindowLoader:r})},Yv=g.forwardRef(qv);function zu(e,t){const n=g.useRef(null),r=g.useRef(),i=g.useCallback(()=>{n.current&&(clearTimeout(n.current),n.current=null)},[]);return g.useEffect(()=>i,[i]),{debouncedCallback:g.useCallback((...a)=>{r.current=a,i(),n.current=setTimeout(()=>{r.current&&(e(...r.current),r.current=void 0)},t)},[e,t,i]),cancel:i}}const Bd=Ci("div",{target:"ee4nolw0"})(({theme:e})=>({paddingTop:e.spacing.xs,paddingBottom:e.spacing.xs})),Pr=Ci("div",{target:"ee4nolw1"})(({theme:e,isActive:t,hasSubmenu:n})=>({display:"flex",alignItems:"center",justifyContent:"flex-start",gap:e.spacing.sm,paddingLeft:e.spacing.sm,paddingRight:e.spacing.sm,paddingTop:e.spacing.twoXS,paddingBottom:e.spacing.twoXS,cursor:"pointer",backgroundColor:t?e.colors.darkenedBgMix15:void 0,"&:hover":{backgroundColor:e.colors.darkenedBgMix15},minWidth:e.sizes.minMenuWidth,...n&&{justifyContent:"space-between","& > :first-of-type":{display:"flex",alignItems:"center",gap:e.spacing.sm}}})),Xv=Ci("div",{target:"ee4nolw2"})(({theme:e})=>({height:e.sizes.borderWidth,backgroundColor:e.colors.borderColor,marginTop:e.spacing.xs,marginBottom:e.spacing.xs})),Vu=[{format:"",label:"Automatic",icon:":material/123:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"plain",label:"Plain",icon:":material/speed_1_75:"},{format:"compact",label:"Compact",icon:":material/1k:"},{format:"dollar",label:"Dollar",icon:":material/attach_money:"},{format:"euro",label:"Euro",icon:":material/euro:"},{format:"percent",label:"Percent",icon:":material/percent:"},{format:"scientific",label:"Scientific",icon:":material/experiment:"},{format:"accounting",label:"Accounting",icon:":material/finance_chip:"}],Gv={number:Vu,progress:Vu,datetime:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"},{format:"calendar",label:"Calendar",icon:":material/today:"}],date:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"},{format:"distance",label:"Distance",icon:":material/search_activity:"}],time:[{format:"",label:"Automatic",icon:":material/schedule:"},{format:"localized",label:"Localized",icon:":material/translate:"}]};function jv({columnKind:e,isOpen:t,onMouseEnter:n,onMouseLeave:r,onChangeFormat:i,onCloseMenu:o,children:a}){const l=jr(),{colors:s,fontSizes:u,radii:c,fontWeights:d}=l,h=Gv[e]||[];return h.length===0?ut(Oc,{}):ut(Sa,{triggerType:Pc.hover,returnFocus:!0,autoFocus:!0,focusLock:!0,isOpen:t,onMouseEnter:n,onMouseLeave:r,ignoreBoundary:!0,content:ut(Bd,{role:"menu",children:h.map(f=>Ln(Pr,{onClick:()=>{i(f.format),o()},role:"menuitem",children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:f.icon}),f.label]},f.format))}),placement:ya.right,showArrow:!1,popoverMargin:2,overrides:{Body:{props:{"data-testid":"stDataFrameColumnFormattingMenu"},style:{borderTopLeftRadius:c.default,borderTopRightRadius:c.default,borderBottomLeftRadius:c.default,borderBottomRightRadius:c.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${l.sizes.borderWidth} solid ${l.colors.borderColor}`}},Inner:{style:{backgroundColor:Ca(l)?s.bgColor:s.secondaryBg,color:s.bodyText,fontSize:u.sm,fontWeight:d.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:a})}const Kv=g.memo(jv);function Zv({top:e,left:t,isColumnPinned:n,onPinColumn:r,onUnpinColumn:i,onCloseMenu:o,onSortColumn:a,onHideColumn:l,columnKind:s,onChangeFormat:u,onAutosize:c}){const d=jr(),[h,f]=g.useState(!1),{colors:m,fontSizes:p,radii:v,fontWeights:w}=d;g.useEffect(()=>{function M(O){O.preventDefault()}return document.addEventListener("wheel",M,{passive:!1}),document.addEventListener("touchmove",M,{passive:!1}),()=>{document.removeEventListener("wheel",M),document.removeEventListener("touchmove",M)}},[]);const b=ie.useCallback(()=>{o()},[o]);return ut(Sa,{autoFocus:!0,"aria-label":"Dataframe column menu",content:Ln(Bd,{children:[a&&Ln(Oc,{children:[Ln(Pr,{onClick:()=>{a("asc"),b()},role:"menuitem",children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_upward:"}),"Sort ascending"]}),Ln(Pr,{onClick:()=>{a("desc"),b()},role:"menuitem",children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrow_downward:"}),"Sort descending"]}),ut(Xv,{})]}),u&&ut(Kv,{columnKind:s,isOpen:h,onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),onChangeFormat:u,onCloseMenu:b,children:Ln(Pr,{onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),isActive:h,hasSubmenu:!0,children:[Ln("div",{children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/format_list_numbered:"}),"Format"]}),ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/chevron_right:"})]})}),c&&Ln(Pr,{onClick:()=>{c(),b()},children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/arrows_outward:"}),"Autosize"]}),n&&Ln(Pr,{onClick:()=>{i(),b()},children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep_off:"}),"Unpin column"]}),!n&&Ln(Pr,{onClick:()=>{r(),b()},children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/keep:"}),"Pin column"]}),l&&Ln(Pr,{onClick:()=>{l(),b()},children:[ut(gr,{size:"base",margin:"0",color:"inherit",iconValue:":material/visibility_off:"}),"Hide column"]})]}),placement:ya.bottomRight,accessibilityType:_c.menu,showArrow:!1,popoverMargin:Wn("0.375rem"),onClickOutside:h?void 0:b,onEsc:b,overrides:{Body:{props:{"data-testid":"stDataFrameColumnMenu"},style:{paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{border:`${d.sizes.borderWidth} solid ${d.colors.borderColor}`,backgroundColor:Ca(d)?m.bgColor:m.secondaryBg,color:m.bodyText,fontSize:p.sm,fontWeight:w.normal,borderTopLeftRadius:v.default,borderTopRightRadius:v.default,borderBottomLeftRadius:v.default,borderBottomRightRadius:v.default,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:!0,children:ut("div",{"data-testid":"stDataFrameColumnMenuTarget",style:{position:"fixed",top:e,left:t,visibility:"hidden",transform:"unset"}})})}const Jv=g.memo(Zv),Qv="(index)",e1=({label:e,initialValue:t,onChange:n})=>{const r=jr();return ut(gg,{checked:t,onChange:i=>{n(i.target.checked)},"aria-label":e,checkmarkType:hg.default,labelPlacement:fg.right,overrides:{Root:{style:({$isFocusVisible:i})=>({marginBottom:r.spacing.none,marginTop:r.spacing.none,paddingLeft:r.spacing.md,paddingRight:r.spacing.md,paddingTop:r.spacing.twoXS,paddingBottom:r.spacing.twoXS,backgroundColor:i?r.colors.darkenedBgMix25:"",display:"flex",alignItems:"start"})},Checkmark:{style:({$isFocusVisible:i,$checked:o})=>{const a=o?r.colors.primary:r.colors.fadedText40;return{outline:0,width:r.sizes.checkbox,height:r.sizes.checkbox,marginTop:r.spacing.twoXS,marginLeft:0,marginBottom:0,boxShadow:i&&o?`0 0 0 0.2rem ${Zo(r.colors.primary,.5)}`:"",borderLeftWidth:r.sizes.borderWidth,borderRightWidth:r.sizes.borderWidth,borderTopWidth:r.sizes.borderWidth,borderBottomWidth:r.sizes.borderWidth,borderLeftColor:a,borderRightColor:a,borderTopColor:a,borderBottomColor:a}}},Label:{style:{lineHeight:r.lineHeights.small,paddingLeft:r.spacing.sm,position:"relative",color:r.colors.bodyText,fontSize:r.fontSizes.sm,fontWeight:r.fontWeights.normal}}},children:e})},t1=({columns:e,columnOrder:t,setColumnOrder:n,hideColumn:r,showColumn:i,children:o,isOpen:a,onClose:l})=>{const s=jr();return ut(Sa,{triggerType:Pc.click,placement:ya.bottomRight,autoFocus:!0,focusLock:!0,content:()=>ut("div",{style:{paddingTop:s.spacing.sm,paddingBottom:s.spacing.sm},children:e.map(u=>{const c=t.length&&!u.isIndex?!t.includes(u.id)&&!t.includes(u.name):!1;return ut(e1,{label:!u.title&&u.isIndex?Qv:u.title,initialValue:!(u.isHidden===!0||c),onChange:d=>{d?(i(u.id),c&&n(h=>[...h,u.id])):r(u.id)}},u.id)})}),isOpen:a,onClickOutside:l,onClick:()=>a?l():void 0,onEsc:l,ignoreBoundary:!1,overrides:{Body:{props:{"data-testid":"stDataFrameColumnVisibilityMenu"},style:{borderTopLeftRadius:s.radii.default,borderTopRightRadius:s.radii.default,borderBottomLeftRadius:s.radii.default,borderBottomRightRadius:s.radii.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent",border:`${s.sizes.borderWidth} solid ${s.colors.borderColor}`}},Inner:{style:{backgroundColor:Ca(s)?s.colors.bgColor:s.colors.secondaryBg,color:s.colors.bodyText,fontSize:s.fontSizes.sm,fontWeight:s.fontWeights.normal,minWidth:s.sizes.minMenuWidth,maxWidth:`calc(${s.sizes.minMenuWidth} * 2)`,maxHeight:s.sizes.maxDropdownHeight,overflow:"auto",paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},children:ut("div",{children:o})})},n1=g.memo(t1);var r1=xh();const i1=wr(r1);var ea={exports:{}};/*! Moment Duration Format v2.2.2
 *  https://github.com/jsmreese/moment-duration-format
 *  Date: 2018-02-16
 *
 *  Duration format plugin function for the Moment.js library
 *  http://momentjs.com/
 *
 *  Copyright 2018 John Madhavan-Reese
 *  Released under the MIT license
 */var o1=ea.exports,$u;function a1(){return $u||($u=1,function(e,t){(function(n,r){try{e.exports=r(kh)}catch{e.exports=r}n&&(n.momentDurationFormatSetup=n.moment?r(n.moment):r)})(o1,function(n){var r=!1,i=!1,o=!1,a=!1,l="escape years months weeks days hours minutes seconds milliseconds general".split(" "),s=[{type:"seconds",targets:[{type:"minutes",value:60},{type:"hours",value:3600},{type:"days",value:86400},{type:"weeks",value:604800},{type:"months",value:2678400},{type:"years",value:31536e3}]},{type:"minutes",targets:[{type:"hours",value:60},{type:"days",value:1440},{type:"weeks",value:10080},{type:"months",value:44640},{type:"years",value:525600}]},{type:"hours",targets:[{type:"days",value:24},{type:"weeks",value:168},{type:"months",value:744},{type:"years",value:8760}]},{type:"days",targets:[{type:"weeks",value:7},{type:"months",value:31},{type:"years",value:365}]},{type:"months",targets:[{type:"years",value:12}]}];function u(H,P){return P.length>H.length?!1:H.indexOf(P)!==-1}function c(H){for(var P="";H;)P+="0",H-=1;return P}function d(H){for(var P=H.split("").reverse(),W=0,ce=!0;ce&&W<P.length;)W?P[W]==="9"?P[W]="0":(P[W]=(parseInt(P[W],10)+1).toString(),ce=!1):(parseInt(P[W],10)<5&&(ce=!1),P[W]="0"),W+=1;return ce&&P.push("1"),P.reverse().join("")}function h(H,P){var W=_(k(P).sort(),function(Oe){return Oe+":"+P[Oe]}).join(","),ce=H+"+"+W;return h.cache[ce]||(h.cache[ce]=Intl.NumberFormat(H,P)),h.cache[ce]}h.cache={};function f(H,P,W){var ce=P.useToLocaleString,Oe=P.useGrouping,ze=Oe&&P.grouping.slice(),Ce=P.maximumSignificantDigits,Ee=P.minimumIntegerDigits||1,xe=P.fractionDigits||0,It=P.groupingSeparator,yt=P.decimalSeparator;if(ce&&W){var it={minimumIntegerDigits:Ee,useGrouping:Oe};if(xe&&(it.maximumFractionDigits=xe,it.minimumFractionDigits=xe),Ce&&H>0&&(it.maximumSignificantDigits=Ce),o){if(!a){var le=T({},P);le.useGrouping=!1,le.decimalSeparator=".",H=parseFloat(f(H,le),10)}return h(W,it).format(H)}else{if(!i){var le=T({},P);le.useGrouping=!1,le.decimalSeparator=".",H=parseFloat(f(H,le),10)}return H.toLocaleString(W,it)}}var tt;Ce?tt=H.toPrecision(Ce+1):tt=H.toFixed(xe+1);var ve,ge,pe,Ve=tt.split("e");pe=Ve[1]||"",Ve=Ve[0].split("."),ge=Ve[1]||"",ve=Ve[0]||"";var _e=ve.length,nt=ge.length,be=_e+nt,de=ve+ge;(Ce&&be===Ce+1||!Ce&&nt===xe+1)&&(de=d(de),de.length===be+1&&(_e=_e+1),nt&&(de=de.slice(0,-1)),ve=de.slice(0,_e),ge=de.slice(_e)),Ce&&(ge=ge.replace(/0*$/,""));var ot=parseInt(pe,10);ot>0?ge.length<=ot?(ge=ge+c(ot-ge.length),ve=ve+ge,ge=""):(ve=ve+ge.slice(0,ot),ge=ge.slice(ot)):ot<0&&(ge=c(Math.abs(ot)-ve.length)+ve+ge,ve="0"),Ce||(ge=ge.slice(0,xe),ge.length<xe&&(ge=ge+c(xe-ge.length)),ve.length<Ee&&(ve=c(Ee-ve.length)+ve));var He="";if(Oe){Ve=ve;for(var et;Ve.length;)ze.length&&(et=ze.shift()),He&&(He=It+He),He=Ve.slice(-et)+He,Ve=Ve.slice(0,-et)}else He=ve;return ge&&(He=He+yt+ge),He}function m(H,P){return H.label.length>P.label.length?-1:H.label.length<P.label.length?1:0}function p(H,P){var W=[];return R(k(P),function(ce){if(ce.slice(0,15)==="_durationLabels"){var Oe=ce.slice(15).toLowerCase();R(k(P[ce]),function(ze){ze.slice(0,1)===H&&W.push({type:Oe,key:ze,label:P[ce][ze]})})}}),W}function v(H,P,W){return P===1&&W===null?H:H+H}var w={durationLabelsStandard:{S:"millisecond",SS:"milliseconds",s:"second",ss:"seconds",m:"minute",mm:"minutes",h:"hour",hh:"hours",d:"day",dd:"days",w:"week",ww:"weeks",M:"month",MM:"months",y:"year",yy:"years"},durationLabelsShort:{S:"msec",SS:"msecs",s:"sec",ss:"secs",m:"min",mm:"mins",h:"hr",hh:"hrs",d:"dy",dd:"dys",w:"wk",ww:"wks",M:"mo",MM:"mos",y:"yr",yy:"yrs"},durationTimeTemplates:{HMS:"h:mm:ss",HM:"h:mm",MS:"m:ss"},durationLabelTypes:[{type:"standard",string:"__"},{type:"short",string:"_"}],durationPluralKey:v};function b(H){return Object.prototype.toString.call(H)==="[object Array]"}function M(H){return Object.prototype.toString.call(H)==="[object Object]"}function O(H,P){for(var W=H.length;W-=1;)if(P(H[W]))return H[W]}function S(H,P){var W=0,ce=H&&H.length||0,Oe;for(typeof P!="function"&&(Oe=P,P=function(ze){return ze===Oe});W<ce;){if(P(H[W]))return H[W];W+=1}}function R(H,P){var W=0,ce=H.length;if(!(!H||!ce))for(;W<ce;){if(P(H[W],W)===!1)return;W+=1}}function _(H,P){var W=0,ce=H.length,Oe=[];if(!H||!ce)return Oe;for(;W<ce;)Oe[W]=P(H[W],W),W+=1;return Oe}function E(H,P){return _(H,function(W){return W[P]})}function x(H){var P=[];return R(H,function(W){W&&P.push(W)}),P}function L(H){var P=[];return R(H,function(W){S(P,W)||P.push(W)}),P}function D(H,P){var W=[];return R(H,function(ce){R(P,function(Oe){ce===Oe&&W.push(ce)})}),L(W)}function C(H,P){var W=[];return R(H,function(ce,Oe){if(!P(ce))return W=H.slice(Oe),!1}),W}function I(H,P){var W=H.slice().reverse();return C(W,P).reverse()}function T(H,P){for(var W in P)P.hasOwnProperty(W)&&(H[W]=P[W]);return H}function k(H){var P=[];for(var W in H)H.hasOwnProperty(W)&&P.push(W);return P}function z(H,P){var W=0,ce=H.length;if(!H||!ce)return!1;for(;W<ce;){if(P(H[W],W)===!0)return!0;W+=1}return!1}function N(H){var P=[];return R(H,function(W){P=P.concat(W)}),P}function X(){var H=0;try{H.toLocaleString("i")}catch(P){return P.name==="RangeError"}return!1}function re(H){return H(3.55,"en",{useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:1,maximumFractionDigits:1})==="3.6"}function G(H){var P=!0;return P=P&&H(1,"en",{minimumIntegerDigits:1})==="1",P=P&&H(1,"en",{minimumIntegerDigits:2})==="01",P=P&&H(1,"en",{minimumIntegerDigits:3})==="001",!(!P||(P=P&&H(99.99,"en",{maximumFractionDigits:0,minimumFractionDigits:0})==="100",P=P&&H(99.99,"en",{maximumFractionDigits:1,minimumFractionDigits:1})==="100.0",P=P&&H(99.99,"en",{maximumFractionDigits:2,minimumFractionDigits:2})==="99.99",P=P&&H(99.99,"en",{maximumFractionDigits:3,minimumFractionDigits:3})==="99.990",!P)||(P=P&&H(99.99,"en",{maximumSignificantDigits:1})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:2})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:3})==="100",P=P&&H(99.99,"en",{maximumSignificantDigits:4})==="99.99",P=P&&H(99.99,"en",{maximumSignificantDigits:5})==="99.99",!P)||(P=P&&H(1e3,"en",{useGrouping:!0})==="1,000",P=P&&H(1e3,"en",{useGrouping:!1})==="1000",!P))}function j(){var H=[].slice.call(arguments),P={},W;if(R(H,function(Ce,Ee){if(!Ee){if(!b(Ce))throw"Expected array as the first argument to durationsFormat.";W=Ce}if(typeof Ce=="string"||typeof Ce=="function"){P.template=Ce;return}if(typeof Ce=="number"){P.precision=Ce;return}M(Ce)&&T(P,Ce)}),!W||!W.length)return[];P.returnMomentTypes=!0;var ce=_(W,function(Ce){return Ce.format(P)}),Oe=D(l,L(E(N(ce),"type"))),ze=P.largest;return ze&&(Oe=Oe.slice(0,ze)),P.returnMomentTypes=!1,P.outputTypes=Oe,_(W,function(Ce){return Ce.format(P)})}function se(){var H=[].slice.call(arguments),P=T({},this.format.defaults),W=this.asMilliseconds(),ce=this.asMonths();typeof this.isValid=="function"&&this.isValid()===!1&&(W=0,ce=0);var Oe=W<0,ze=n.duration(Math.abs(W),"milliseconds"),Ce=n.duration(Math.abs(ce),"months");R(H,function(V){if(typeof V=="string"||typeof V=="function"){P.template=V;return}if(typeof V=="number"){P.precision=V;return}M(V)&&T(P,V)});var Ee={years:"y",months:"M",weeks:"w",days:"d",hours:"h",minutes:"m",seconds:"s",milliseconds:"S"},xe={escape:/\[(.+?)\]/,years:/\*?[Yy]+/,months:/\*?M+/,weeks:/\*?[Ww]+/,days:/\*?[Dd]+/,hours:/\*?[Hh]+/,minutes:/\*?m+/,seconds:/\*?s+/,milliseconds:/\*?S+/,general:/.+?/};P.types=l;var It=function(V){return S(l,function(Qe){return xe[Qe].test(V)})},yt=new RegExp(_(l,function(V){return xe[V].source}).join("|"),"g");P.duration=this;var it=typeof P.template=="function"?P.template.apply(P):P.template,le=P.outputTypes,tt=P.returnMomentTypes,ve=P.largest,ge=[];le||(b(P.stopTrim)&&(P.stopTrim=P.stopTrim.join("")),P.stopTrim&&R(P.stopTrim.match(yt),function(V){var Qe=It(V);Qe==="escape"||Qe==="general"||ge.push(Qe)}));var pe=n.localeData();pe||(pe={}),R(k(w),function(V){if(typeof w[V]=="function"){pe[V]||(pe[V]=w[V]);return}pe["_"+V]||(pe["_"+V]=w[V])}),R(k(pe._durationTimeTemplates),function(V){it=it.replace("_"+V+"_",pe._durationTimeTemplates[V])});var Ve=P.userLocale||n.locale(),_e=P.useLeftUnits,nt=P.usePlural,be=P.precision,de=P.forceLength,ot=P.useGrouping,He=P.trunc,et=P.useSignificantDigits&&be>0,bt=et?P.precision:0,gt=bt,$e=P.minValue,Ot=!1,Yt=P.maxValue,kt=!1,Lt=P.useToLocaleString,nn=P.groupingSeparator,zt=P.decimalSeparator,ln=P.grouping;Lt=Lt&&(r||o);var Tt=P.trim;b(Tt)&&(Tt=Tt.join(" ")),Tt===null&&(ve||Yt||et)&&(Tt="all"),(Tt===null||Tt===!0||Tt==="left"||Tt==="right")&&(Tt="large"),Tt===!1&&(Tt="");var Ct=function(V){return V.test(Tt)},Dn=/large/,yn=/small/,Pe=/both/,Ft=/mid/,fn=/^all|[^sm]all/,hn=/final/,pn=ve>0||z([Dn,Pe,fn],Ct),st=z([yn,Pe,fn],Ct),Xt=z([Ft,fn],Ct),Pt=z([hn,fn],Ct),rn=_(it.match(yt),function(V,Qe){var Ue=It(V);return V.slice(0,1)==="*"&&(V=V.slice(1),Ue!=="escape"&&Ue!=="general"&&ge.push(Ue)),{index:Qe,length:V.length,text:"",token:Ue==="escape"?V.replace(xe.escape,"$1"):V,type:Ue==="escape"||Ue==="general"?null:Ue}}),Dt={index:0,length:0,token:"",text:"",type:null},At=[];_e&&rn.reverse(),R(rn,function(V){if(V.type){(Dt.type||Dt.text)&&At.push(Dt),Dt=V;return}_e?Dt.text=V.token+Dt.text:Dt.text+=V.token}),(Dt.type||Dt.text)&&At.push(Dt),_e&&At.reverse();var We=D(l,L(x(E(At,"type"))));if(!We.length)return E(At,"text").join("");We=_(We,function(V,Qe){var Ue=Qe+1===We.length,fe=!Qe,lt;V==="years"||V==="months"?lt=Ce.as(V):lt=ze.as(V);var Wt=Math.floor(lt),dn=lt-Wt,Zt=S(At,function(jt){return V===jt.type});return fe&&Yt&&lt>Yt&&(kt=!0),Ue&&$e&&Math.abs(P.duration.as(V))<$e&&(Ot=!0),fe&&de===null&&Zt.length>1&&(de=!0),ze.subtract(Wt,V),Ce.subtract(Wt,V),{rawValue:lt,wholeValue:Wt,decimalValue:Ue?dn:0,isSmallest:Ue,isLargest:fe,type:V,tokenLength:Zt.length}});var Bt=He?Math.floor:Math.round,_t=function(V,Qe){var Ue=Math.pow(10,Qe);return Bt(V*Ue)/Ue},Vt=!1,$t=!1,Cn=function(V,Qe){var Ue={useGrouping:ot,groupingSeparator:nn,decimalSeparator:zt,grouping:ln,useToLocaleString:Lt};return et&&(bt<=0?(V.rawValue=0,V.wholeValue=0,V.decimalValue=0):(Ue.maximumSignificantDigits=bt,V.significantDigits=bt)),kt&&!$t&&(V.isLargest?(V.wholeValue=Yt,V.decimalValue=0):(V.wholeValue=0,V.decimalValue=0)),Ot&&!$t&&(V.isSmallest?(V.wholeValue=$e,V.decimalValue=0):(V.wholeValue=0,V.decimalValue=0)),V.isSmallest||V.significantDigits&&V.significantDigits-V.wholeValue.toString().length<=0?be<0?V.value=_t(V.wholeValue,be):be===0?V.value=Bt(V.wholeValue+V.decimalValue):et?(He?V.value=_t(V.rawValue,bt-V.wholeValue.toString().length):V.value=V.rawValue,V.wholeValue&&(bt-=V.wholeValue.toString().length)):(Ue.fractionDigits=be,He?V.value=V.wholeValue+_t(V.decimalValue,be):V.value=V.wholeValue+V.decimalValue):et&&V.wholeValue?(V.value=Math.round(_t(V.wholeValue,V.significantDigits-V.wholeValue.toString().length)),bt-=V.wholeValue.toString().length):V.value=V.wholeValue,V.tokenLength>1&&(de||Vt)&&(Ue.minimumIntegerDigits=V.tokenLength,$t&&Ue.maximumSignificantDigits<V.tokenLength&&delete Ue.maximumSignificantDigits),!Vt&&(V.value>0||Tt===""||S(ge,V.type)||S(le,V.type))&&(Vt=!0),V.formattedValue=f(V.value,Ue,Ve),Ue.useGrouping=!1,Ue.decimalSeparator=".",V.formattedValueEn=f(V.value,Ue,"en"),V.tokenLength===2&&V.type==="milliseconds"&&(V.formattedValueMS=f(V.value,{minimumIntegerDigits:3,useGrouping:!1},"en").slice(0,2)),V};if(We=_(We,Cn),We=x(We),We.length>1){var nr=function(V){return S(We,function(Qe){return Qe.type===V})},Hn=function(V){var Qe=nr(V.type);Qe&&R(V.targets,function(Ue){var fe=nr(Ue.type);fe&&parseInt(Qe.formattedValueEn,10)===Ue.value&&(Qe.rawValue=0,Qe.wholeValue=0,Qe.decimalValue=0,fe.rawValue+=1,fe.wholeValue+=1,fe.decimalValue=0,fe.formattedValueEn=fe.wholeValue.toString(),$t=!0)})};R(s,Hn)}return $t&&(Vt=!1,bt=gt,We=_(We,Cn),We=x(We)),le&&!(kt&&!P.trim)?(We=_(We,function(V){return S(le,function(Qe){return V.type===Qe})?V:null}),We=x(We)):(pn&&(We=C(We,function(V){return!V.isSmallest&&!V.wholeValue&&!S(ge,V.type)})),ve&&We.length&&(We=We.slice(0,ve)),st&&We.length>1&&(We=I(We,function(V){return!V.wholeValue&&!S(ge,V.type)&&!V.isLargest})),Xt&&(We=_(We,function(V,Qe){return Qe>0&&Qe<We.length-1&&!V.wholeValue?null:V}),We=x(We)),Pt&&We.length===1&&!We[0].wholeValue&&!(!He&&We[0].isSmallest&&We[0].rawValue<$e)&&(We=[])),tt?We:(R(At,function(V){var Qe=Ee[V.type],Ue=S(We,function(jt){return jt.type===V.type});if(!(!Qe||!Ue)){var fe=Ue.formattedValueEn.split(".");fe[0]=parseInt(fe[0],10),fe[1]?fe[1]=parseFloat("0."+fe[1],10):fe[1]=null;var lt=pe.durationPluralKey(Qe,fe[0],fe[1]),Wt=p(Qe,pe),dn=!1,Zt={};R(pe._durationLabelTypes,function(jt){var Sn=S(Wt,function(vn){return vn.type===jt.type&&vn.key===lt});Sn&&(Zt[Sn.type]=Sn.label,u(V.text,jt.string)&&(V.text=V.text.replace(jt.string,Sn.label),dn=!0))}),nt&&!dn&&(Wt.sort(m),R(Wt,function(jt){if(Zt[jt.type]===jt.label)return u(V.text,jt.label)?!1:void 0;if(u(V.text,jt.label))return V.text=V.text.replace(jt.label,Zt[jt.type]),!1}))}}),At=_(At,function(V){if(!V.type)return V.text;var Qe=S(We,function(fe){return fe.type===V.type});if(!Qe)return"";var Ue="";return _e&&(Ue+=V.text),(Oe&&kt||!Oe&&Ot)&&(Ue+="< ",kt=!1,Ot=!1),(Oe&&Ot||!Oe&&kt)&&(Ue+="> ",kt=!1,Ot=!1),Oe&&(Qe.value>0||Tt===""||S(ge,Qe.type)||S(le,Qe.type))&&(Ue+="-",Oe=!1),V.type==="milliseconds"&&Qe.formattedValueMS?Ue+=Qe.formattedValueMS:Ue+=Qe.formattedValue,_e||(Ue+=V.text),Ue}),At.join("").replace(/(,| |:|\.)*$/,"").replace(/^(,| |:|\.)*/,""))}function oe(){var H=this.duration,P=function(ze){return H._data[ze]},W=S(this.types,P),ce=O(this.types,P);switch(W){case"milliseconds":return"S __";case"seconds":case"minutes":return"*_MS_";case"hours":return"_HMS_";case"days":if(W===ce)return"d __";case"weeks":return W===ce?"w __":(this.trim===null&&(this.trim="both"),"w __, d __, h __");case"months":if(W===ce)return"M __";case"years":return W===ce?"y __":(this.trim===null&&(this.trim="both"),"y __, M __, d __");default:return this.trim===null&&(this.trim="both"),"y __, d __, h __, m __, s __"}}function ue(H){if(!H)throw"Moment Duration Format init cannot find moment instance.";H.duration.format=j,H.duration.fn.format=se,H.duration.fn.format.defaults={trim:null,stopTrim:null,largest:null,maxValue:null,minValue:null,precision:0,trunc:!1,forceLength:null,userLocale:null,usePlural:!0,useLeftUnits:!1,useGrouping:!0,useSignificantDigits:!1,template:oe,useToLocaleString:!0,groupingSeparator:",",decimalSeparator:".",grouping:[3]},H.updateLocale("en",w)}var he=function(H,P,W){return H.toLocaleString(P,W)};r=X()&&G(he),i=r&&re(he);var Q=function(H,P,W){if(typeof window<"u"&&window&&window.Intl&&window.Intl.NumberFormat)return window.Intl.NumberFormat(P,W).format(H)};return o=G(Q),a=o&&re(Q),ue(n),ue})}(ea)),ea.exports}a1();const s1=["true","t","yes","y","on","1"],l1=["false","f","no","n","off","0"];function Ht(e,t=""){return{kind:te.Text,readonly:!0,allowOverlay:!0,data:e,displayData:e,errorDetails:t,isError:!0,style:"faded"}}function bi(e){return e.hasOwnProperty("isError")&&e.isError}function u1(e){return e.hasOwnProperty("tooltip")&&e.tooltip!==""}function ka(e){return e.hasOwnProperty("isMissingValue")&&e.isMissingValue}function Is(e=!1){return e?{kind:te.Loading,allowOverlay:!1,isMissingValue:!0}:{kind:te.Loading,allowOverlay:!1}}function c1(e,t){const n=t?"faded":"normal";return{kind:te.Text,data:"",displayData:"",allowOverlay:!0,readonly:e,style:n}}function Ts(e){return{id:e.id,title:e.title,hasMenu:!1,menuIcon:"dots",themeOverride:e.themeOverride,icon:e.icon,group:e.group,...e.isStretched&&!e.isPinned&&{grow:1},...e.width&&{width:e.width}}}function go(e,t){return Be(e)?t||{}:Be(t)?e||{}:Lc(e,t)}function Wd(e){if(Be(e))return[];if(typeof e=="number"||typeof e=="boolean")return[e];if(typeof e=="string"){if(e==="")return[];if(e.trim().startsWith("[")&&e.trim().endsWith("]"))try{return JSON.parse(e)}catch{return[e]}else return e.split(",")}try{const t=JSON.parse(JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r));return Array.isArray(t)?t.map(n=>["string","number","boolean","null"].includes(typeof n)?n:wt(n)):[wt(t)]}catch{return[wt(e)]}}function d1(e){return e&&e.startsWith("{")&&e.endsWith("}")}function wt(e){try{try{return i1(e)}catch{return JSON.stringify(e,(n,r)=>typeof r=="bigint"?Number(r):r)}}catch{return`[${typeof e}]`}}function Ud(e){if(Be(e))return null;if(typeof e=="boolean")return e;const t=wt(e).toLowerCase().trim();if(t==="")return null;if(s1.includes(t))return!0;if(l1.includes(t))return!1}function so(e){if(Be(e))return null;if(Array.isArray(e))return NaN;if(typeof e=="string"){if(e.trim().length===0)return null;try{const t=ji.unformat(e.trim());if(vt(t))return t}catch{}}else if(e instanceof Int32Array)return Number(e[0]);return Number(e)}function fa(e){if(Be(e))return"";if(typeof e=="string")return e;try{return JSON.stringify(e,(t,n)=>typeof n=="bigint"?Number(n):n)}catch{return wt(e)}}function f1(e){if(e===0||Math.abs(e)>=1e-4)return 4;const n=e.toExponential().split("e");return Math.abs(parseInt(n[1],10))}function ha(e,t,n){return Number.isNaN(e)||!Number.isFinite(e)?"":Be(t)||t===""?vt(n)?(n===0&&(e=Math.round(e)),ji(e).format({thousandSeparated:!1,mantissa:n,trimMantissa:!1})):ji(e).format({thousandSeparated:!1,mantissa:f1(e),trimMantissa:!0}):t==="plain"?ji(e).format({thousandSeparated:!1,mantissa:20,trimMantissa:!0}):t==="localized"?new Intl.NumberFormat().format(e):t==="percent"?new Intl.NumberFormat(void 0,{style:"percent",minimumFractionDigits:0,maximumFractionDigits:2}).format(e):t==="dollar"?new Intl.NumberFormat(void 0,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol",maximumFractionDigits:2}).format(e):t==="euro"?new Intl.NumberFormat(void 0,{style:"currency",currency:"EUR",maximumFractionDigits:2}).format(e):["compact","scientific","engineering"].includes(t)?new Intl.NumberFormat(void 0,{notation:t}).format(e):t==="accounting"?ji(e).format({thousandSeparated:!0,negative:"parenthesis",mantissa:2,trimMantissa:!1}):pg.sprintf(t,e)}function Nu(e,t,n="datetime"){return t==="localized"?new Intl.DateTimeFormat(void 0,{dateStyle:n==="time"?void 0:"medium",timeStyle:n==="date"?void 0:"medium"}).format(e.toDate()):t==="distance"?e.fromNow():t==="calendar"?e.calendar():t==="iso8601"?n==="date"?e.format("YYYY-MM-DD"):n==="time"?e.format("HH:mm:ss.SSS[Z]"):e.toISOString():e.format(t)}function Uo(e){if(Be(e))return null;if(e instanceof Date)return isNaN(e.getTime())?void 0:e;if(typeof e=="string"&&e.trim().length===0)return null;try{const t=Number(e);if(!isNaN(t)){let n=t;t>=10**18?n=t/1e3**3:t>=10**15?n=t/1e3**2:t>=10**12&&(n=t/1e3);const r=qr.unix(n).utc();if(r.isValid())return r.toDate()}if(typeof e=="string"){const n=qr.utc(e);if(n.isValid())return n.toDate();const r=qr.utc(e,[qr.HTML5_FMT.TIME_MS,qr.HTML5_FMT.TIME_SECONDS,qr.HTML5_FMT.TIME]);if(r.isValid())return r.toDate()}}catch{return}}function qd(e){if(e%1===0)return 0;let t=e.toString();return t.indexOf("e")!==-1&&(t=e.toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20})),t.indexOf(".")===-1?0:t.split(".")[1].length}function h1(e,t){return t===0?Math.trunc(e):Math.trunc(e*10**t)/10**t}const g1=new RegExp(/(\r\n|\n|\r)/gm);function lo(e){return e.indexOf(`
`)!==-1?e.replace(g1," "):e}function m1(e,t){if(Be(t))return"";try{const n=t.match(e);return n&&n[1]!==void 0?decodeURIComponent(n[1].replace(/\+/g,"%20")):t}catch{return t}}const p1=Ci("div",{target:"e1ayynp0"})(({theme:e})=>({overflowY:"auto",padding:e.spacing.sm,".react-json-view .copy-icon svg":{fontSize:"0.9em !important",marginRight:`${e.spacing.threeXS} !important`,verticalAlign:"middle !important"}})),Yd=({jsonValue:e,theme:t})=>{let n;if(e)try{n=typeof e=="string"?Ya.parse(e):Ya.parse(Ya.stringify(e))}catch{n=void 0}return Be(n)?ut(Zr,{highlight:!0,autoFocus:!1,disabled:!0,value:fa(e)??"",onChange:()=>{}}):ut(p1,{"data-testid":"stJsonColumnViewer",children:ut(Mh,{src:n,collapsed:2,theme:Rh(t.bgCell)>.5?"rjv-default":"monokai",displayDataTypes:!1,displayObjectSize:!1,name:!1,enableClipboard:!0,style:{fontFamily:t.fontFamily,fontSize:t.baseFontStyle,backgroundColor:t.bgCell,whiteSpace:"pre-wrap"}})})},v1=e=>{const t=e.theme,n=e.value.data;return ut(Yd,{jsonValue:n.value||n.displayValue,theme:t})},b1=e=>{const t=e.theme,n=e.value;return ut(Yd,{jsonValue:n.data,theme:t})},w1={kind:te.Custom,isMatch:e=>e.data.kind==="json-cell",draw:(e,t)=>{const{value:n,displayValue:r}=t.data;return Us(e,r??fa(n)??"",t.contentAlign),!0},measure:(e,t,n)=>{const{value:r,displayValue:i}=t.data,o=i??fa(r)??"";return(o?e.measureText(o).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:v1})},y1="line_chart",C1="area_chart",S1="bar_chart";function Zs(e,t,n){const r=go({y_min:0,y_max:1},t.columnTypeOptions),i={kind:te.Custom,allowOverlay:!1,copyData:"",contentAlign:t.contentAlignment,data:{kind:"sparkline-cell",values:[],displayValues:[],graphKind:n,yAxis:[r.y_min,r.y_max]}};return{...t,kind:e,sortMode:"default",isEditable:!1,getCell(o){if(Be(r.y_min)||Be(r.y_max)||Number.isNaN(r.y_min)||Number.isNaN(r.y_max)||r.y_min>=r.y_max)return Ht("Invalid min/max y-axis configuration",`The y_min (${r.y_min}) and y_max (${r.y_max}) configuration options must be valid numbers.`);if(Be(o))return Is();const a=Wd(o),l=[];let s=[];if(a.length===0)return Is();let u=Number.MIN_SAFE_INTEGER,c=Number.MAX_SAFE_INTEGER;for(let d=0;d<a.length;d++){const h=so(a[d]);if(Number.isNaN(h)||Be(h))return Ht(wt(a),`The value cannot be interpreted as a numeric array. ${wt(h)} is not a number.`);h>u&&(u=h),h<c&&(c=h),l.push(h)}return l.length>0&&(u>r.y_max||c<r.y_min)?s=l.map(d=>u-c===0?u>(r.y_max||1)?r.y_max||1:r.y_min||0:((r.y_max||1)-(r.y_min||0))*((d-c)/(u-c))+(r.y_min||0)):s=l,{...i,copyData:l.join(","),data:{...i.data,values:s,displayValues:l.map(d=>ha(d))},isMissingValue:Be(o)}},getCellValue(o){var a,l;return o.kind===te.Loading||((a=o.data)==null?void 0:a.values)===void 0?null:(l=o.data)==null?void 0:l.values}}}function Xd(e){return Zs(y1,e,"line")}Xd.isEditableType=!1;function Gd(e){return Zs(S1,e,"bar")}Gd.isEditableType=!1;function jd(e){return Zs(C1,e,"area")}jd.isEditableType=!1;function Js(e){const t={kind:te.Boolean,data:!1,allowOverlay:!1,contentAlign:e.contentAlignment,readonly:!e.isEditable,style:"normal"};return{...e,kind:"checkbox",sortMode:"default",getCell(n){let r=null;return r=Ud(n),r===void 0?Ht(wt(n),"The value cannot be interpreted as boolean."):{...t,data:r,isMissingValue:Be(r)}},getCellValue(n){return n.data===void 0?null:n.data}}}Js.isEditableType=!0;function Bu(e,t){return t.startsWith("+")||t.startsWith("-")?e=e.utcOffset(t,!1):e=e.tz(t),e}function Qs(e,t,n,r,i,o,a){var f,m;const l=go({format:n,step:r,timezone:a},t.columnTypeOptions);let s;if(vt(l.timezone))try{s=((f=Bu(Dl(),l.timezone))==null?void 0:f.utcOffset())||void 0}catch{}let u;vt(l.min_value)&&(u=Uo(l.min_value)||void 0);let c;vt(l.max_value)&&(c=Uo(l.max_value)||void 0);const d={kind:te.Custom,allowOverlay:!0,copyData:"",readonly:!t.isEditable,contentAlign:t.contentAlignment,style:t.isPinned?"faded":"normal",data:{kind:"date-picker-cell",date:void 0,displayDate:"",step:((m=l.step)==null?void 0:m.toString())||"1",format:i,min:u,max:c}},h=p=>{const v=Uo(p);return v===null?!t.isRequired:!(v===void 0||vt(u)&&o(v)<o(u)||vt(c)&&o(v)>o(c))};return{...t,kind:e,sortMode:"default",validateInput:h,getCell(p,v){if(v===!0){const S=h(p);if(S===!1)return Ht(wt(p),"Invalid input.");S instanceof Date&&(p=S)}const w=Uo(p);let b="",M="",O=s;if(w===void 0)return Ht(wt(p),"The value cannot be interpreted as a datetime object.");if(w!==null){let S=Dl.utc(w);if(!S.isValid())return Ht(wt(w),`Invalid moment date. This should never happen. Please report this bug. 
Error: ${S.toString()}`);if(l.timezone){try{S=Bu(S,l.timezone)}catch(R){return Ht(S.toISOString(),`Failed to adjust to the provided timezone: ${l.timezone}. 
Error: ${R}`)}O=S.utcOffset()}try{M=Nu(S,l.format||n,e)}catch(R){return Ht(S.toISOString(),`Failed to format the date for rendering with: ${l.format}. 
Error: ${R}`)}b=Nu(S,n,e)}return{...d,copyData:b,isMissingValue:Be(w),data:{...d.data,date:w,displayDate:M,timezoneOffset:O}}},getCellValue(p){var v;return Be((v=p==null?void 0:p.data)==null?void 0:v.date)?null:o(p.data.date)}}}function el(e){var i,o,a;let t="YYYY-MM-DD HH:mm:ss";((i=e.columnTypeOptions)==null?void 0:i.step)>=60?t="YYYY-MM-DD HH:mm":((o=e.columnTypeOptions)==null?void 0:o.step)<1&&(t="YYYY-MM-DD HH:mm:ss.SSS");const n=Eh(e.arrowType),r=vt(n)||vt((a=e==null?void 0:e.columnTypeOptions)==null?void 0:a.timezone);return Qs("datetime",e,r?t+"Z":t,1,"datetime-local",l=>r?l.toISOString():l.toISOString().replace("Z",""),n)}el.isEditableType=!0;function tl(e){var n,r;let t="HH:mm:ss";return((n=e.columnTypeOptions)==null?void 0:n.step)>=60?t="HH:mm":((r=e.columnTypeOptions)==null?void 0:r.step)<1&&(t="HH:mm:ss.SSS"),Qs("time",e,t,1,"time",i=>i.toISOString().split("T")[1].replace("Z",""))}tl.isEditableType=!0;function nl(e){return Qs("date",e,"YYYY-MM-DD",1,"date",t=>t.toISOString().split("T")[0])}nl.isEditableType=!0;function Kd(e){const t={kind:te.Image,data:[],displayData:[],readonly:!0,allowOverlay:!0,contentAlign:e.contentAlignment||"center",style:"normal"};return{...e,kind:"image",sortMode:"default",isEditable:!1,getCell(n){const r=vt(n)?[wt(n)]:[];return{...t,data:r,isMissingValue:!vt(n),displayData:r}},getCellValue(n){return n.data===void 0||n.data.length===0?null:n.data[0]}}}Kd.isEditableType=!1;function Zd(e){const t={kind:te.Custom,allowOverlay:!0,contentAlignment:e.contentAlignment,readonly:!0,style:e.isPinned?"faded":"normal",copyData:"",data:{kind:"json-cell",value:""}};return{...e,kind:"json",sortMode:"default",isEditable:!1,getCell(n){try{const r=vt(n)?lo(fa(n)):"";return{...t,copyData:r,isMissingValue:Be(n),data:{...t.data,value:n,displayValue:r}}}catch(r){return Ht(wt(n),`The value cannot be interpreted as a JSON string. Error: ${r}`)}},getCellValue(n){var r;return((r=n.data)==null?void 0:r.value)??null}}}Zd.isEditableType=!1;function Jd(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(a){n=`Invalid validate regex: ${t.validate}.
Error: ${a}`}let r;if(!Be(t.display_text)&&t.display_text.includes("(")&&t.display_text.includes(")"))try{r=new RegExp(t.display_text,"us")}catch{r=void 0}const i={kind:te.Uri,readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment,style:"normal",hoverEffect:!0,data:"",displayData:"",copyData:""},o=a=>{if(Be(a))return!e.isRequired;const l=wt(a);return!(t.max_chars&&l.length>t.max_chars||n instanceof RegExp&&n.test(l)===!1)};return{...e,kind:"link",sortMode:"default",validateInput:o,getCell(a,l){if(Be(a))return{...i,data:null,isMissingValue:!0,onClickUri:()=>{}};const s=a;if(typeof n=="string")return Ht(wt(s),n);if(l&&o(s)===!1)return Ht(wt(s),"Invalid input.");let u="";return s&&(r!==void 0?u=m1(r,s):u=t.display_text||s),{...i,data:s,displayData:u,isMissingValue:Be(s),onClickUri:c=>{window.open(s.startsWith("www.")?`https://${s}`:s,"_blank","noopener,noreferrer"),c.preventDefault()},copyData:s}},getCellValue(a){return Be(a.data)?null:a.data}}}Jd.isEditableType=!0;function rl(e){const t={kind:te.Bubble,data:[],allowOverlay:!0,contentAlign:e.contentAlignment,style:"normal"};return{...e,kind:"list",sortMode:"default",isEditable:!1,getCell(n){const r=Be(n)?[]:Wd(n);return{...t,data:r,isMissingValue:Be(n),copyData:Be(n)?"":wt(r.map(i=>typeof i=="string"&&i.includes(",")?i.replace(/,/g," "):i))}},getCellValue(n){return Be(n.data)||ka(n)?null:n.data}}}rl.isEditableType=!1;function il(e){const t=go({step:Fc(e.arrowType)?1:void 0,min_value:Ih(e.arrowType)?0:void 0},e.columnTypeOptions),n=!t.format&&(Th(e.arrowType)||Dh(e.arrowType)),r=Be(t.min_value)||t.min_value<0,i=vt(t.step)&&!Number.isNaN(t.step)?qd(t.step):void 0,o={kind:te.Number,data:void 0,displayData:"",readonly:!e.isEditable,allowOverlay:!0,contentAlign:e.contentAlignment||n?"left":"right",style:e.isPinned?"faded":"normal",allowNegative:r,fixedDecimals:i,thousandSeparator:""},a=l=>{let s=so(l);if(Be(s))return!e.isRequired;if(Number.isNaN(s))return!1;let u=!1;return vt(t.max_value)&&s>t.max_value&&(s=t.max_value,u=!0),vt(t.min_value)&&s<t.min_value?!1:u?s:!0};return{...e,kind:"number",sortMode:"smart",validateInput:a,getCell(l,s){if(s===!0){const d=a(l);if(d===!1)return Ht(wt(l),"Invalid input.");typeof d=="number"&&(l=d)}let u=so(l),c="";if(vt(u)){if(Number.isNaN(u))return Ht(wt(l),"The value cannot be interpreted as a number.");if(vt(i)&&(u=h1(u,i)),Number.isInteger(u)&&!Number.isSafeInteger(u))return Ht(wt(l),"The value is larger than the maximum supported integer values in number columns (2^53).");try{n?c=Cs(u,e.arrowType):c=ha(u,t.format,i)}catch(d){return Ht(wt(u),vt(t.format)?`Failed to format the number based on the provided format configuration: (${t.format}). Error: ${d}`:`Failed to format the number. Error: ${d}`)}}return{...o,data:u,displayData:c,isMissingValue:Be(u),copyData:Be(u)?"":wt(u)}},getCellValue(l){return l.data===void 0?null:l.data}}}il.isEditableType=!0;function uo(e){const t={kind:te.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!0,style:e.isPinned?"faded":"normal"};return{...e,kind:"object",sortMode:"default",isEditable:!1,getCell(n){try{const r=vt(n)?wt(n):null,i=vt(r)?lo(r):"";return{...t,data:r,displayData:i,isMissingValue:Be(n)}}catch(r){return Ht(wt(n),`The value cannot be interpreted as a string. Error: ${r}`)}},getCellValue(n){return n.data===void 0?null:n.data}}}uo.isEditableType=!1;function Qd(e){const t=Fc(e.arrowType),n=go({min_value:0,max_value:t?100:1,step:t?1:.01,format:t?"%3d%%":"percent"},e.columnTypeOptions);let r;try{r=ha(n.max_value,n.format)}catch{r=wt(n.max_value)}const i=Be(n.step)||Number.isNaN(n.step)?void 0:qd(n.step),o={kind:te.Custom,allowOverlay:!1,copyData:"",contentAlign:e.contentAlignment,data:{kind:"range-cell",min:n.min_value,max:n.max_value,step:n.step,value:n.min_value,label:String(n.min_value),measureLabel:r,readonly:!0}};return{...e,kind:"progress",sortMode:"smart",isEditable:!1,getCell(a){if(Be(a))return Is();if(Be(n.min_value)||Be(n.max_value)||Number.isNaN(n.min_value)||Number.isNaN(n.max_value)||n.min_value>=n.max_value)return Ht("Invalid min/max parameters",`The min_value (${n.min_value}) and max_value (${n.max_value}) parameters must be valid numbers.`);if(Be(n.step)||Number.isNaN(n.step))return Ht("Invalid step parameter",`The step parameter (${n.step}) must be a valid number.`);const l=so(a);if(Number.isNaN(l)||Be(l))return Ht(wt(a),"The value cannot be interpreted as a number.");if(Number.isInteger(l)&&!Number.isSafeInteger(l))return Ht(wt(a),"The value is larger than the maximum supported integer values in number columns (2^53).");let s="";try{s=ha(l,n.format,i)}catch(c){return Ht(wt(l),vt(n.format)?`Failed to format the number based on the provided format configuration: (${n.format}). Error: ${c}`:`Failed to format the number. Error: ${c}`)}const u=Math.min(n.max_value,Math.max(n.min_value,l));return{...o,isMissingValue:Be(a),copyData:String(l),data:{...o.data,value:u,label:s}}},getCellValue(a){var l,s;return a.kind===te.Loading||((l=a.data)==null?void 0:l.value)===void 0?null:(s=a.data)==null?void 0:s.value}}}Qd.isEditableType=!1;function ol(e){let t="string";const n=go({options:Ac(e.arrowType)?[!0,!1]:e.arrowType.categoricalOptions??[]},e.columnTypeOptions),r=new Set(n.options.map(o=>typeof o));r.size===1&&(r.has("number")||r.has("bigint")?t="number":r.has("boolean")&&(t="boolean"));const i={kind:te.Custom,allowOverlay:!0,copyData:"",contentAlign:e.contentAlignment,readonly:!e.isEditable,style:e.isPinned?"faded":"normal",data:{kind:"dropdown-cell",allowedValues:[...e.isRequired!==!0?[null]:[],...n.options.filter(o=>o!==null&&o!=="").map(o=>wt(o))],value:""}};return{...e,kind:"selectbox",sortMode:"default",getCell(o,a){let l=null;return vt(o)&&o!==""&&(l=wt(o)),a&&!i.data.allowedValues.includes(l)?Ht(wt(l),"The value is not part of the allowed options."):{...i,isMissingValue:l===null,copyData:l||"",data:{...i.data,value:l}}},getCellValue(o){var a,l,s,u,c;return Be((a=o.data)==null?void 0:a.value)||((l=o.data)==null?void 0:l.value)===""?null:t==="number"?so((s=o.data)==null?void 0:s.value)??null:t==="boolean"?Ud((u=o.data)==null?void 0:u.value)??null:(c=o.data)==null?void 0:c.value}}}ol.isEditableType=!0;function al(e){const t=e.columnTypeOptions||{};let n;if(t.validate)try{n=new RegExp(t.validate,"us")}catch(o){n=`Invalid validate regex: ${t.validate}.
Error: ${o}`}const r={kind:te.Text,data:"",displayData:"",allowOverlay:!0,contentAlignment:e.contentAlignment,allowWrapping:e.isWrappingAllowed,readonly:!e.isEditable,style:e.isPinned?"faded":"normal"},i=o=>{if(Be(o))return!e.isRequired;let a=wt(o),l=!1;return t.max_chars&&a.length>t.max_chars&&(a=a.slice(0,t.max_chars),l=!0),n instanceof RegExp&&n.test(a)===!1?!1:l?a:!0};return{...e,kind:"text",sortMode:"default",validateInput:i,getCell(o,a){if(typeof n=="string")return Ht(wt(o),n);if(a){const l=i(o);if(l===!1)return Ht(wt(o),"Invalid input.");typeof l=="string"&&(o=l)}try{const l=vt(o)?wt(o):null,s=vt(l)?lo(l):"";return{...r,isMissingValue:Be(l),data:l,displayData:s}}catch(l){return Ht("Incompatible value",`The value cannot be interpreted as string. Error: ${l}`)}},getCellValue(o){return o.data===void 0?null:o.data}}}al.isEditableType=!0;const Wu=Ci("img",{target:"e17fx5ar0"})({maxWidth:"100%",maxHeight:"37.5rem",objectFit:"scale-down"}),x1=({urls:e})=>{const t=e&&e.length>0?e[0]:"";return t.startsWith("http")?ut("a",{href:t,target:"_blank",rel:"noreferrer noopener",children:ut(Wu,{src:t})}):ut(Wu,{src:t})},Uu=new Map(Object.entries({object:uo,text:al,checkbox:Js,selectbox:ol,list:rl,number:il,link:Jd,datetime:el,date:nl,time:tl,line_chart:Xd,bar_chart:Gd,area_chart:jd,image:Kd,progress:Qd,json:Zd})),k1=[w1];var M1=Hc();const R1=wr(M1);var hs,qu;function E1(){if(qu)return hs;qu=1;var e=Oh(),t=Ph(),n=_h(),r=Hc(),i=Lh(),o=Fh(),a=Ah(),l=Hh(),s="[object Map]",u="[object Set]",c=Object.prototype,d=c.hasOwnProperty;function h(f){if(f==null)return!0;if(i(f)&&(r(f)||typeof f=="string"||typeof f.splice=="function"||o(f)||l(f)||n(f)))return!f.length;var m=t(f);if(m==s||m==u)return!f.size;if(a(f))return!e(f).length;for(var p in f)if(d.call(f,p))return!1;return!0}return hs=h,hs}var I1=E1();const T1=wr(I1);function Yu(e,t,n){const r=new RegExp(`${e}[,\\s].*{(?:[^}]*[\\s;]{1})?${t}:\\s*([^;}]+)[;]?.*}`,"gm");n=n.replace(/{/g," {");const i=r.exec(n);if(i)return i[1].trim()}function D1(e,t,n){const r={},i=Yu(t,"color",n);i&&(r.textDark=i);const o=Yu(t,"background-color",n);return o&&(r.bgCell=o),o==="yellow"&&i===void 0&&(r.textDark="#31333F"),r?{...e,themeOverride:r}:e}function O1(e){return Uh(e)||qh(e)?al:Yh(e)?el:zc(e)?tl:Xh(e)?nl:Gh(e)||jh(e)?uo:Ac(e)?Js:Kh(e)?il:Zh(e)?ol:Jh(e)?rl:uo}function ef(e){const t=e.length>0?e[e.length-1]:"",n=e.length>1?e.slice(0,-1).filter(r=>r!=="").join(" / "):void 0;return{title:t,group:n}}function sl(e){return{group:void 0,isEditable:!1,isIndex:!1,isPinned:!1,isHidden:!1,isStretched:!1,...e}}function P1(e,t){const n=e.columnNames.map(l=>l[t]),{title:r,group:i}=ef(n),o=e.columnTypes[t];let a=!0;return Wh(o)&&(a=!1),sl({id:`_index-${t}`,indexNumber:t,name:r,title:r,group:i,isEditable:a,arrowType:o,isIndex:!0,isPinned:!0})}function _1(e,t){const n=e.columnNames.map(a=>a[t]),{title:r,group:i}=ef(n),o=e.columnTypes[t];return sl({id:`_column-${r}-${t}`,indexNumber:t,name:r,isEditable:!0,title:r,arrowType:o,group:i})}function tf(){return sl({id:"_empty-index",indexNumber:0,title:"",name:"",isEditable:!1,isIndex:!0,isPinned:!0,arrowType:{type:$h.INDEX,arrowField:new zh("",new Vh,!0),pandasType:void 0}})}function Xu(e){const t=[],{dimensions:n}=e,r=n.numIndexColumns,i=n.numDataColumns;if(r===0&&i===0)return t.push(tf()),t;for(let o=0;o<r;o++)t.push(P1(e,o));for(let o=0;o<i;o++)t.push(_1(e,o+r));return t}function L1(e,t,n,r=void 0){var o,a,l,s,u,c;let i;if(e.kind==="object"||e.kind==="json")i=e.getCell(vt(t.content)?lo(Cs(t.content,t.contentType)):null);else if(["time","date","datetime"].includes(e.kind)&&vt(t.content)&&(typeof t.content=="number"||typeof t.content=="bigint")){let d;zc(t.contentType)&&vt((a=(o=t.field)==null?void 0:o.type)==null?void 0:a.unit)?d=Nh(t.content,t.field):d=qr.utc(Number(t.content)).toDate(),i=e.getCell(d)}else if(Bh(t.contentType)){const d=Be(t.content)?null:Cs(t.content,t.contentType);i=e.getCell(d)}else i=e.getCell(t.content);if(bi(i))return i;if(!e.isEditable){if(n&&vt(n==null?void 0:n.displayContent)){const d=lo(n.displayContent);i.kind===te.Text?i={...i,displayData:d}:i.kind===te.Number&&Be((l=e.columnTypeOptions)==null?void 0:l.format)?i={...i,displayData:d}:i.kind===te.Uri&&Be((s=e.columnTypeOptions)==null?void 0:s.display_text)?i={...i,displayData:d}:i.kind===te.Custom&&((u=i.data)==null?void 0:u.kind)==="date-picker-cell"&&Be((c=e.columnTypeOptions)==null?void 0:c.format)&&(i={...i,data:{...i.data,displayDate:d}})}r&&(n!=null&&n.cssId)&&(i=D1(i,n.cssId,r))}return i}const ta="_index",Gu="_pos:",ju={small:75,medium:200,large:400},nf=Fs.getLogger("useColumnLoader");function F1(e){if(!Be(e)){if(typeof e=="number")return e;if(e in ju)return ju[e]}}const qo=(e,t)=>mg(e,t,(r,i)=>{if(R1(i))return i});function Ku(e,t){if(!t)return e;let n={};return e.isIndex&&t.has(ta)&&(n=qo(n,t.get(ta)??{})),t.has(`${Gu}${e.indexNumber}`)&&(n=qo(n,t.get(`${Gu}${e.indexNumber}`)??{})),t.has(e.name)&&e.name!==ta&&(n=qo(n,t.get(e.name)??{})),t.has(e.id)&&(n=qo(n,t.get(e.id)??{})),T1(n)?e:Lc({...e},{title:n.label,width:F1(n.width),isEditable:vt(n.disabled)?!n.disabled:void 0,isHidden:n.hidden,isPinned:n.pinned,isRequired:n.required,columnTypeOptions:n.type_config,contentAlignment:n.alignment,defaultValue:n.default,help:n.help})}function A1(e){if(!e)return new Map;try{return new Map(Object.entries(JSON.parse(e)))}catch(t){return nf.error(t),new Map}}function Zu(e){var r;const t=(r=e.columnTypeOptions)==null?void 0:r.type;let n;return vt(t)&&(Uu.has(t)?n=Uu.get(t):nf.warn(`Unknown column type configured in column configuration: ${t}`)),Be(n)&&(n=O1(e.arrowType)),n}function H1(e,t,n,r){const i=jr(),o=ie.useMemo(()=>A1(e.columns),[e.columns]),[a,l]=ie.useState(o);ie.useEffect(()=>{l(o)},[o]);const s=e.useContainerWidth||vt(e.width)&&e.width>0,u=vt(e.rowHeight)&&e.rowHeight>Wn("4rem"),c=ie.useMemo(()=>Xu(t).map(h=>{let f={...h,...Ku(h,a),isStretched:s};const m=Zu(f);return(e.editingMode===Mn.EditingMode.READ_ONLY||n||m.isEditableType===!1)&&(f={...f,isEditable:!1}),e.editingMode!==Mn.EditingMode.READ_ONLY&&f.isEditable==!0&&(f={...f,icon:"editable"},f.isRequired&&e.editingMode===Mn.EditingMode.DYNAMIC&&(f={...f,isHidden:!1})),m(f,i)}),[t,a,s,e.editingMode,n,i]);return{columns:ie.useMemo(()=>{const h=Xu(t).map(v=>{let w={...v,...Ku(v,a),isStretched:s,isWrappingAllowed:u};const b=Zu(w);return(e.editingMode===Mn.EditingMode.READ_ONLY||n||b.isEditableType===!1)&&(w={...w,isEditable:!1}),e.editingMode!==Mn.EditingMode.READ_ONLY&&w.isEditable==!0&&(w={...w,icon:"editable"},w.isRequired&&e.editingMode===Mn.EditingMode.DYNAMIC&&(w={...w,isHidden:!1})),b(w,i)}).filter(v=>!v.isHidden),f=[],m=[];r!=null&&r.length?(h.forEach(v=>{v.isIndex&&!r.includes(v.name)&&!r.includes(v.id)&&v.isPinned!==!1&&f.push(v)}),r.forEach(v=>{const w=h.find(b=>b.name===v||b.id===v);w&&(w.isPinned?f.push(w):m.push(w))})):h.forEach(v=>{v.isPinned?f.push(v):m.push(v)});const p=[...f,...m];return p.length>0?p:[uo(tf())]},[t,a,u,s,n,e.editingMode,r,i]),allColumns:c,setColumnConfigMapping:l}}function ro(e){return e.isIndex?ta:Be(e.name)?"":e.name}class Yo{constructor(t){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[],this.numRows=0,this.numRows=t}toJson(t){const n=new Map;t.forEach(o=>{n.set(o.indexNumber,o)});const r={edited_rows:{},added_rows:[],deleted_rows:[]};return this.editedCells.forEach((o,a,l)=>{const s={};o.forEach((u,c,d)=>{const h=n.get(c);h&&(s[ro(h)]=h.getCellValue(u))}),r.edited_rows[a]=s}),this.addedRows.forEach(o=>{const a={};let l=!1;o.forEach((s,u,c)=>{const d=n.get(u);if(d){const h=d.getCellValue(s);d.isRequired&&d.isEditable&&ka(s)&&(l=!0),vt(h)&&(a[ro(d)]=h)}}),l||r.added_rows.push(a)}),r.deleted_rows=this.deletedRows,JSON.stringify(r,(o,a)=>a===void 0?null:a)}fromJson(t,n){this.editedCells=new Map,this.addedRows=[],this.deletedRows=[];const r=JSON.parse(t),i=new Map;n.forEach(a=>{i.set(a.indexNumber,a)});const o=new Map;n.forEach(a=>{o.set(ro(a),a)}),Object.keys(r.edited_rows).forEach(a=>{const l=Number(a),s=r.edited_rows[a];Object.keys(s).forEach(u=>{var h;const c=s[u],d=o.get(u);if(d){const f=d.getCell(c);f&&(this.editedCells.has(l)||this.editedCells.set(l,new Map),(h=this.editedCells.get(l))==null||h.set(d.indexNumber,f))}})}),r.added_rows.forEach(a=>{const l=new Map;n.forEach(s=>{l.set(s.indexNumber,s.getCell(null))}),Object.keys(a).forEach(s=>{const u=a[s],c=o.get(s);if(c){const d=c.getCell(u);d&&l.set(c.indexNumber,d)}}),this.addedRows.push(l)}),this.deletedRows=r.deleted_rows}isAddedRow(t){return t>=this.numRows}getCell(t,n){if(this.isAddedRow(n))return this.addedRows[n-this.numRows].get(t);const r=this.editedCells.get(n);if(r!==void 0)return r.get(t)}setCell(t,n,r){if(this.isAddedRow(n)){if(n-this.numRows>=this.addedRows.length)return;this.addedRows[n-this.numRows].set(t,r)}else this.editedCells.get(n)===void 0&&this.editedCells.set(n,new Map),this.editedCells.get(n).set(t,r)}addRow(t){this.addedRows.push(t)}deleteRows(t){t.sort((n,r)=>r-n).forEach(n=>{this.deleteRow(n)})}deleteRow(t){if(!(Be(t)||t<0)){if(this.isAddedRow(t)){this.addedRows.splice(t-this.numRows,1);return}this.deletedRows.includes(t)||(this.deletedRows.push(t),this.deletedRows=this.deletedRows.sort((n,r)=>n-r)),this.editedCells.delete(t)}}getOriginalRowIndex(t){let n=t;for(let r=0;r<this.deletedRows.length&&!(this.deletedRows[r]>n);r++)n+=1;return n}getNumRows(){return this.numRows+this.addedRows.length-this.deletedRows.length}}const co=({columnId:e,columnConfigMapping:t,updatedProps:n})=>{const r=new Map(t),i=r.get(e),o={...i||{},...n||{}};return(i!=null&&i.type_config||n!=null&&n.type_config)&&(o.type_config={...(i==null?void 0:i.type_config)||{},...(n==null?void 0:n.type_config)||{}}),r.set(e,o),r};function z1(e){return{changeColumnFormat:ie.useCallback((n,r)=>{e(i=>co({columnId:n,columnConfigMapping:i,updatedProps:{type_config:{format:r}}}))},[e])}}function V1(e,t,n,r,i,o){const a=ie.useMemo(()=>e.filter(c=>c.isPinned).reduce((c,d)=>c+(d.width??r*2),0)>n*.6,[e,n,r]),l=t||a?0:e.filter(c=>c.isPinned).length,s=ie.useCallback(c=>{o(d=>co({columnId:c,columnConfigMapping:d,updatedProps:{pinned:!1}})),i(!0,!1)},[i,o]);return{pinColumn:ie.useCallback(c=>{o(d=>co({columnId:c,columnConfigMapping:d,updatedProps:{pinned:!0}})),i(!0,!1)},[i,o]),unpinColumn:s,freezeColumns:l}}function $1(e,t,n,r,i){return{onColumnMoved:ie.useCallback((a,l)=>{const s=[...e],[u]=s.splice(a,1);s.splice(l,0,u),l<t&&!u.isPinned?n(u.id):l>=t&&u.isPinned&&r(u.id),i(s.map(c=>c.id))},[e,t,n,r,i])}}function N1(e){const[t,n]=g.useState(()=>new Map),r=ie.useCallback((o,a,l,s)=>{o.id&&n(new Map(t).set(o.id,s))},[t]);return{columns:ie.useMemo(()=>e.map(o=>o.id&&t.has(o.id)&&t.get(o.id)!==void 0?{...o,width:t.get(o.id),grow:0}:o),[e,t]),onColumnResize:r}}function B1(e){var t,n;switch(e.kind){case te.Number:return((t=e.data)==null?void 0:t.toString())??"";case te.Boolean:return((n=e.data)==null?void 0:n.toString())??"";case te.Markdown:case te.RowID:case te.Text:case te.Uri:return e.data??"";case te.Bubble:case te.Image:return e.data.join("");case te.Drilldown:return e.data.map(r=>r.text).join("");case te.Protected:case te.Loading:return"";case te.Custom:return e.copyData}}function Ju(e){if(typeof e=="number")return e;if(e.length>0){const t=Number(e);isNaN(t)||(e=t)}return e}function W1(e,t){return e=Ju(e),t=Ju(t),typeof e=="string"&&typeof t=="string"?e.localeCompare(t):typeof e=="number"&&typeof t=="number"?e===t?0:e>t?1:-1:e==t?0:e>t?1:-1}function U1(e,t){return e>t?1:e===t?0:-1}function q1(e){const{sort:t,rows:n,getCellContent:r}=e;let i=t===void 0?void 0:e.columns.findIndex(u=>t.column===u||u.id!==void 0&&t.column.id===u.id);i===-1&&(i=void 0);const o=(t==null?void 0:t.direction)??"asc",a=g.useMemo(()=>{if(i===void 0)return;const u=new Array(n),c=[i,0];for(let h=0;h<n;h++)c[1]=h,u[h]=B1(r(c));let d;return(t==null?void 0:t.mode)==="raw"?d=lr(n).sort((h,f)=>U1(u[h],u[f])):(t==null?void 0:t.mode)==="smart"?d=lr(n).sort((h,f)=>W1(u[h],u[f])):d=lr(n).sort((h,f)=>u[h].localeCompare(u[f])),o==="desc"&&d.reverse(),d},[r,n,t==null?void 0:t.mode,o,i]),l=g.useCallback(u=>a===void 0?u:a[u],[a]),s=g.useCallback(([u,c])=>a===void 0?r([u,c]):(c=a[c],r([u,c])),[r,a]);return a===void 0?{getCellContent:e.getCellContent,getOriginalIndex:l}:{getOriginalIndex:l,getCellContent:s}}function Y1(e,t){return t===void 0?e:e.map(n=>n.id===t.column.id?{...n,title:t.direction==="asc"?`↑ ${n.title}`:`↓ ${n.title}`}:n)}function X1(e,t,n){const[r,i]=ie.useState(),{getCellContent:o,getOriginalIndex:a}=q1({columns:t.map(u=>Ts(u)),getCellContent:n,rows:e,sort:r}),l=ie.useMemo(()=>Y1(t,r),[t,r]),s=ie.useCallback((u,c,d)=>{const h=l[u];let f;c==="auto"?(f="asc",r&&r.column.id===h.id&&(r.direction==="asc"?f="desc":f=void 0)):f=c,f===void 0||d&&f===(r==null?void 0:r.direction)?i(void 0):i({column:Ts(h),direction:f,mode:h.sortMode})},[r,l]);return{columns:l,sortColumn:s,getOriginalIndex:a,getCellContent:o}}function G1(e,t){const n=ie.useCallback(i=>{t(o=>co({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!0}})),e(!0,!1)},[e,t]),r=ie.useCallback(i=>{t(o=>co({columnId:i,columnConfigMapping:o,updatedProps:{hidden:!1}})),e(!0,!1)},[e,t]);return{hideColumn:n,showColumn:r}}function j1(){return{provideEditor:g.useCallback(t=>{if(t.kind===te.Text&&t.readonly&&d1(t.data))return{editor:b1}},[])}}const K1={kind:te.Custom,isMatch:e=>e.data.kind==="sparkline-cell",needsHover:!0,needsHoverPosition:!0,draw:(e,t)=>{const{ctx:n,theme:r,rect:i,hoverAmount:o,hoverX:a}=e;let{values:l,yAxis:s,color:u,graphKind:c="area",displayValues:d,hideAxis:h}=t.data;const[f,m]=s;if(l.length===0)return!0;l=l.map(R=>Math.min(1,Math.max(0,(R-f)/(m-f))));const p=r.cellHorizontalPadding,v=p+i.x,w=i.y+3,b=i.height-6,M=i.width-p*2,O=m-f,S=m<=0?w:f>=0?w+b:w+b*(m/O);if(!h&&f<=0&&m>=0&&(n.beginPath(),n.moveTo(v,S),n.lineTo(v+M,S),n.globalAlpha=.4,n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.globalAlpha=1),c==="bar"){n.beginPath();const R=2,_=(l.length-1)*R,E=(M-_)/l.length;let x=v;for(const L of l){const D=w+b-L*b;n.moveTo(x,S),n.lineTo(x+E,S),n.lineTo(x+E,D),n.lineTo(x,D),x+=E+R}n.fillStyle=t.data.color??r.accentColor,n.fill()}else{l.length===1&&(l=[l[0],l[0]],d&&(d=[d[0],d[0]])),n.beginPath();const R=(i.width-16)/(l.length-1),_=l.map((x,L)=>({x:v+R*L,y:w+b-x*b}));n.moveTo(_[0].x,_[0].y);let E=0;if(_.length>2)for(E=1;E<_.length-2;E++){const x=(_[E].x+_[E+1].x)/2,L=(_[E].y+_[E+1].y)/2;n.quadraticCurveTo(_[E].x,_[E].y,x,L)}if(n.quadraticCurveTo(_[E].x,_[E].y,_[E+1].x,_[E+1].y),n.strokeStyle=u??r.accentColor,n.lineWidth=1+o*.5,n.stroke(),n.lineTo(i.x+i.width-p,S),n.lineTo(i.x+p,S),n.closePath(),c==="area"){n.globalAlpha=.2+.2*o;const x=n.createLinearGradient(0,w,0,w+b*1.4);x.addColorStop(0,u??r.accentColor);const[L,D,C]=sa(u??r.accentColor);x.addColorStop(1,`rgba(${L}, ${D}, ${C}, 0)`),n.fillStyle=x,n.fill(),n.globalAlpha=1}if(a!==void 0&&(c==="line"||c==="area")&&d!==void 0){n.beginPath();const x=Math.min(l.length-1,Math.max(0,Math.round((a-p)/R)));n.moveTo(v+x*R,i.y+1),n.lineTo(v+x*R,i.y+i.height),n.lineWidth=1,n.strokeStyle=r.textLight,n.stroke(),n.save(),n.font=`8px ${r.fontFamily}`,n.fillStyle=r.textMedium,n.textBaseline="top",n.fillText(d[x],v,i.y+r.cellVerticalPadding),n.restore()}}return!0},provideEditor:()=>{},onPaste:(e,t)=>t};function Qu(e,t,n,r,i,o){if(!(r<=0||i<=0)){if(typeof o=="number"&&o<=0){e.rect(t,n,r,i);return}typeof o=="number"&&(o={tl:o,tr:o,br:o,bl:o}),o={tl:Math.min(o.tl,i/2,r/2),tr:Math.min(o.tr,i/2,r/2),bl:Math.min(o.bl,i/2,r/2),br:Math.min(o.br,i/2,r/2)},o.tl=Math.max(0,o.tl),o.tr=Math.max(0,o.tr),o.br=Math.max(0,o.br),o.bl=Math.max(0,o.bl),e.moveTo(t+o.tl,n),e.arcTo(t+r,n,t+r,n+o.tr,o.tr),e.arcTo(t+r,n+i,t+r-o.br,n+i,o.br),e.arcTo(t,n+i,t,n+i-o.bl,o.bl),e.arcTo(t,n,t+o.tl,n,o.tl)}}function ec(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Xe(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?ec(Object(n),!0).forEach(function(r){Ki(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ec(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}var Z1=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function J1(e){var t=e.defaultInputValue,n=t===void 0?"":t,r=e.defaultMenuIsOpen,i=r===void 0?!1:r,o=e.defaultValue,a=o===void 0?null:o,l=e.inputValue,s=e.menuIsOpen,u=e.onChange,c=e.onInputChange,d=e.onMenuClose,h=e.onMenuOpen,f=e.value,m=yr(e,Z1),p=g.useState(l!==void 0?l:n),v=pr(p,2),w=v[0],b=v[1],M=g.useState(s!==void 0?s:i),O=pr(M,2),S=O[0],R=O[1],_=g.useState(f!==void 0?f:a),E=pr(_,2),x=E[0],L=E[1],D=g.useCallback(function(X,re){typeof u=="function"&&u(X,re),L(X)},[u]),C=g.useCallback(function(X,re){var G;typeof c=="function"&&(G=c(X,re)),b(G!==void 0?G:X)},[c]),I=g.useCallback(function(){typeof h=="function"&&h(),R(!0)},[h]),T=g.useCallback(function(){typeof d=="function"&&d(),R(!1)},[d]),k=l!==void 0?l:w,z=s!==void 0?s:S,N=f!==void 0?f:x;return Xe(Xe({},m),{},{inputValue:k,menuIsOpen:z,onChange:D,onInputChange:C,onMenuClose:T,onMenuOpen:I,value:N})}function Q1(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}const eb=Math.min,tb=Math.max,ga=Math.round,Xo=Math.floor,ma=e=>({x:e,y:e});function nb(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function Ma(){return typeof window<"u"}function rf(e){return af(e)?(e.nodeName||"").toLowerCase():"#document"}function br(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function of(e){var t;return(t=(af(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function af(e){return Ma()?e instanceof Node||e instanceof br(e).Node:!1}function rb(e){return Ma()?e instanceof Element||e instanceof br(e).Element:!1}function ll(e){return Ma()?e instanceof HTMLElement||e instanceof br(e).HTMLElement:!1}function tc(e){return!Ma()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof br(e).ShadowRoot}function sf(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=ul(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function ib(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function ob(e){return["html","body","#document"].includes(rf(e))}function ul(e){return br(e).getComputedStyle(e)}function ab(e){if(rf(e)==="html")return e;const t=e.assignedSlot||e.parentNode||tc(e)&&e.host||of(e);return tc(t)?t.host:t}function lf(e){const t=ab(e);return ob(t)?e.ownerDocument?e.ownerDocument.body:e.body:ll(t)&&sf(t)?t:lf(t)}function pa(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=lf(e),o=i===((r=e.ownerDocument)==null?void 0:r.body),a=br(i);if(o){const l=Ds(a);return t.concat(a,a.visualViewport||[],sf(i)?i:[],l&&n?pa(l):[])}return t.concat(i,pa(i,[],n))}function Ds(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function sb(e){const t=ul(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=ll(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,l=ga(n)!==o||ga(r)!==a;return l&&(n=o,r=a),{width:n,height:r,$:l}}function cl(e){return rb(e)?e:e.contextElement}function nc(e){const t=cl(e);if(!ll(t))return ma(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:o}=sb(t);let a=(o?ga(n.width):n.width)/r,l=(o?ga(n.height):n.height)/i;return(!a||!Number.isFinite(a))&&(a=1),(!l||!Number.isFinite(l))&&(l=1),{x:a,y:l}}const lb=ma(0);function ub(e){const t=br(e);return!ib()||!t.visualViewport?lb:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function cb(e,t,n){return!1}function rc(e,t,n,r){t===void 0&&(t=!1);const i=e.getBoundingClientRect(),o=cl(e);let a=ma(1);t&&(a=nc(e));const l=cb()?ub(o):ma(0);let s=(i.left+l.x)/a.x,u=(i.top+l.y)/a.y,c=i.width/a.x,d=i.height/a.y;if(o){const h=br(o),f=r;let m=h,p=Ds(m);for(;p&&r&&f!==m;){const v=nc(p),w=p.getBoundingClientRect(),b=ul(p),M=w.left+(p.clientLeft+parseFloat(b.paddingLeft))*v.x,O=w.top+(p.clientTop+parseFloat(b.paddingTop))*v.y;s*=v.x,u*=v.y,c*=v.x,d*=v.y,s+=M,u+=O,m=br(p),p=Ds(m)}}return nb({width:c,height:d,x:s,y:u})}function db(e,t){let n=null,r;const i=of(e);function o(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function a(l,s){l===void 0&&(l=!1),s===void 0&&(s=1),o();const{left:u,top:c,width:d,height:h}=e.getBoundingClientRect();if(l||t(),!d||!h)return;const f=Xo(c),m=Xo(i.clientWidth-(u+d)),p=Xo(i.clientHeight-(c+h)),v=Xo(u),b={rootMargin:-f+"px "+-m+"px "+-p+"px "+-v+"px",threshold:tb(0,eb(1,s))||1};let M=!0;function O(S){const R=S[0].intersectionRatio;if(R!==s){if(!M)return a();R?a(!1,R):r=setTimeout(()=>{a(!1,1e-7)},1e3)}M=!1}try{n=new IntersectionObserver(O,{...b,root:i.ownerDocument})}catch{n=new IntersectionObserver(O,b)}n.observe(e)}return a(!0),o}function fb(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:s=!1}=r,u=cl(e),c=i||o?[...u?pa(u):[],...pa(t)]:[];c.forEach(w=>{i&&w.addEventListener("scroll",n,{passive:!0}),o&&w.addEventListener("resize",n)});const d=u&&l?db(u,n):null;let h=-1,f=null;a&&(f=new ResizeObserver(w=>{let[b]=w;b&&b.target===u&&f&&(f.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var M;(M=f)==null||M.observe(t)})),n()}),u&&!s&&f.observe(u),f.observe(t));let m,p=s?rc(e):null;s&&v();function v(){const w=rc(e);p&&(w.x!==p.x||w.y!==p.y||w.width!==p.width||w.height!==p.height)&&n(),p=w,m=requestAnimationFrame(v)}return n(),()=>{var w;c.forEach(b=>{i&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),d==null||d(),(w=f)==null||w.disconnect(),f=null,s&&cancelAnimationFrame(m)}}var Os=g.useLayoutEffect,hb=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],va=function(){};function gb(e,t){return t?t[0]==="-"?e+t:e+"__"+t:e}function mb(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=[].concat(r);if(t&&e)for(var a in t)t.hasOwnProperty(a)&&t[a]&&o.push("".concat(gb(e,a)));return o.filter(function(l){return l}).map(function(l){return String(l).trim()}).join(" ")}var ic=function(t){return kb(t)?t.filter(Boolean):Qh(t)==="object"&&t!==null?[t]:[]},uf=function(t){t.className,t.clearValue,t.cx,t.getStyles,t.getClassNames,t.getValue,t.hasValue,t.isMulti,t.isRtl,t.options,t.selectOption,t.selectProps,t.setValue,t.theme;var n=yr(t,hb);return Xe({},n)},tn=function(t,n,r){var i=t.cx,o=t.getStyles,a=t.getClassNames,l=t.className;return{css:o(n,t),className:i(r??{},a(n,t),l)}};function Ra(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function pb(e){return Ra(e)?window.innerHeight:e.clientHeight}function cf(e){return Ra(e)?window.pageYOffset:e.scrollTop}function ba(e,t){if(Ra(e)){window.scrollTo(0,t);return}e.scrollTop=t}function vb(e){var t=getComputedStyle(e),n=t.position==="absolute",r=/(auto|scroll)/;if(t.position==="fixed")return document.documentElement;for(var i=e;i=i.parentElement;)if(t=getComputedStyle(i),!(n&&t.position==="static")&&r.test(t.overflow+t.overflowY+t.overflowX))return i;return document.documentElement}function bb(e,t,n,r){return n*((e=e/r-1)*e*e+1)+t}function Go(e,t){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:200,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:va,i=cf(e),o=t-i,a=10,l=0;function s(){l+=a;var u=bb(l,i,o,n);ba(e,u),l<n?window.requestAnimationFrame(s):r(e)}s()}function oc(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),i=t.offsetHeight/3;r.bottom+i>n.bottom?ba(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+i,e.scrollHeight)):r.top-i<n.top&&ba(e,Math.max(t.offsetTop-i,0))}function wb(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}function ac(){try{return document.createEvent("TouchEvent"),!0}catch{return!1}}function yb(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch{return!1}}var df=!1,Cb={get passive(){return df=!0}},jo=typeof window<"u"?window:{};jo.addEventListener&&jo.removeEventListener&&(jo.addEventListener("p",va,Cb),jo.removeEventListener("p",va,!1));var Sb=df;function xb(e){return e!=null}function kb(e){return Array.isArray(e)}function Ko(e,t,n){return e?t:n}var Mb=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=Object.entries(t).filter(function(a){var l=pr(a,1),s=l[0];return!r.includes(s)});return o.reduce(function(a,l){var s=pr(l,2),u=s[0],c=s[1];return a[u]=c,a},{})},Rb=["children","innerProps"],Eb=["children","innerProps"];function Ib(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,i=e.placement,o=e.shouldScroll,a=e.isFixedPosition,l=e.controlHeight,s=vb(n),u={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return u;var c=s.getBoundingClientRect(),d=c.height,h=n.getBoundingClientRect(),f=h.bottom,m=h.height,p=h.top,v=n.offsetParent.getBoundingClientRect(),w=v.top,b=a?window.innerHeight:pb(s),M=cf(s),O=parseInt(getComputedStyle(n).marginBottom,10),S=parseInt(getComputedStyle(n).marginTop,10),R=w-S,_=b-p,E=R+M,x=d-M-p,L=f-b+M+O,D=M+p-S,C=160;switch(i){case"auto":case"bottom":if(_>=m)return{placement:"bottom",maxHeight:t};if(x>=m&&!a)return o&&Go(s,L,C),{placement:"bottom",maxHeight:t};if(!a&&x>=r||a&&_>=r){o&&Go(s,L,C);var I=a?_-O:x-O;return{placement:"bottom",maxHeight:I}}if(i==="auto"||a){var T=t,k=a?R:E;return k>=r&&(T=Math.min(k-O-l,t)),{placement:"top",maxHeight:T}}if(i==="bottom")return o&&ba(s,L),{placement:"bottom",maxHeight:t};break;case"top":if(R>=m)return{placement:"top",maxHeight:t};if(E>=m&&!a)return o&&Go(s,D,C),{placement:"top",maxHeight:t};if(!a&&E>=r||a&&R>=r){var z=t;return(!a&&E>=r||a&&R>=r)&&(z=a?R-S:E-S),o&&Go(s,D,C),{placement:"top",maxHeight:z}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(i,'".'))}return u}function Tb(e){var t={bottom:"top",top:"bottom"};return e?t[e]:"bottom"}var ff=function(t){return t==="auto"?"bottom":t},Db=function(t,n){var r,i=t.placement,o=t.theme,a=o.borderRadius,l=o.spacing,s=o.colors;return Xe((r={label:"menu"},Ki(r,Tb(i),"100%"),Ki(r,"position","absolute"),Ki(r,"width","100%"),Ki(r,"zIndex",1),r),n?{}:{backgroundColor:s.neutral0,borderRadius:a,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:l.menuGutter,marginTop:l.menuGutter})},hf=g.createContext(null),Ob=function(t){var n=t.children,r=t.minMenuHeight,i=t.maxMenuHeight,o=t.menuPlacement,a=t.menuPosition,l=t.menuShouldScrollIntoView,s=t.theme,u=g.useContext(hf)||{},c=u.setPortalPlacement,d=g.useRef(null),h=g.useState(i),f=pr(h,2),m=f[0],p=f[1],v=g.useState(null),w=pr(v,2),b=w[0],M=w[1],O=s.spacing.controlHeight;return Os(function(){var S=d.current;if(S){var R=a==="fixed",_=l&&!R,E=Ib({maxHeight:i,menuEl:S,minHeight:r,placement:o,shouldScroll:_,isFixedPosition:R,controlHeight:O});p(E.maxHeight),M(E.placement),c==null||c(E.placement)}},[i,o,a,l,r,c,O]),n({ref:d,placerProps:Xe(Xe({},t),{},{placement:b||ff(o),maxHeight:m})})},Pb=function(t){var n=t.children,r=t.innerRef,i=t.innerProps;return Ne("div",Ze({},tn(t,"menu",{menu:!0}),{ref:r},i),n)},_b=Pb,Lb=function(t,n){var r=t.maxHeight,i=t.theme.spacing.baseUnit;return Xe({maxHeight:r,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},n?{}:{paddingBottom:i,paddingTop:i})},Fb=function(t){var n=t.children,r=t.innerProps,i=t.innerRef,o=t.isMulti;return Ne("div",Ze({},tn(t,"menuList",{"menu-list":!0,"menu-list--is-multi":o}),{ref:i},r),n)},gf=function(t,n){var r=t.theme,i=r.spacing.baseUnit,o=r.colors;return Xe({textAlign:"center"},n?{}:{color:o.neutral40,padding:"".concat(i*2,"px ").concat(i*3,"px")})},Ab=gf,Hb=gf,zb=function(t){var n=t.children,r=n===void 0?"No options":n,i=t.innerProps,o=yr(t,Rb);return Ne("div",Ze({},tn(Xe(Xe({},o),{},{children:r,innerProps:i}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),i),r)},Vb=function(t){var n=t.children,r=n===void 0?"Loading...":n,i=t.innerProps,o=yr(t,Eb);return Ne("div",Ze({},tn(Xe(Xe({},o),{},{children:r,innerProps:i}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),i),r)},$b=function(t){var n=t.rect,r=t.offset,i=t.position;return{left:n.left,position:i,top:r,width:n.width,zIndex:1}},Nb=function(t){var n=t.appendTo,r=t.children,i=t.controlElement,o=t.innerProps,a=t.menuPlacement,l=t.menuPosition,s=g.useRef(null),u=g.useRef(null),c=g.useState(ff(a)),d=pr(c,2),h=d[0],f=d[1],m=g.useMemo(function(){return{setPortalPlacement:f}},[]),p=g.useState(null),v=pr(p,2),w=v[0],b=v[1],M=g.useCallback(function(){if(i){var _=wb(i),E=l==="fixed"?0:window.pageYOffset,x=_[h]+E;(x!==(w==null?void 0:w.offset)||_.left!==(w==null?void 0:w.rect.left)||_.width!==(w==null?void 0:w.rect.width))&&b({offset:x,rect:_})}},[i,l,h,w==null?void 0:w.offset,w==null?void 0:w.rect.left,w==null?void 0:w.rect.width]);Os(function(){M()},[M]);var O=g.useCallback(function(){typeof u.current=="function"&&(u.current(),u.current=null),i&&s.current&&(u.current=fb(i,s.current,M,{elementResize:"ResizeObserver"in window}))},[i,M]);Os(function(){O()},[O]);var S=g.useCallback(function(_){s.current=_,O()},[O]);if(!n&&l!=="fixed"||!w)return null;var R=Ne("div",Ze({ref:S},tn(Xe(Xe({},t),{},{offset:w.offset,position:l,rect:w.rect}),"menuPortal",{"menu-portal":!0}),o),r);return Ne(hf.Provider,{value:m},n?Vc.createPortal(R,n):R)},Bb=function(t){var n=t.isDisabled,r=t.isRtl;return{label:"container",direction:r?"rtl":void 0,pointerEvents:n?"none":void 0,position:"relative"}},Wb=function(t){var n=t.children,r=t.innerProps,i=t.isDisabled,o=t.isRtl;return Ne("div",Ze({},tn(t,"container",{"--is-disabled":i,"--is-rtl":o}),r),n)},Ub=function(t,n){var r=t.theme.spacing,i=t.isMulti,o=t.hasValue,a=t.selectProps.controlShouldRenderValue;return Xe({alignItems:"center",display:i&&o&&a?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},n?{}:{padding:"".concat(r.baseUnit/2,"px ").concat(r.baseUnit*2,"px")})},qb=function(t){var n=t.children,r=t.innerProps,i=t.isMulti,o=t.hasValue;return Ne("div",Ze({},tn(t,"valueContainer",{"value-container":!0,"value-container--is-multi":i,"value-container--has-value":o}),r),n)},Yb=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},Xb=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ze({},tn(t,"indicatorsContainer",{indicators:!0}),r),n)},sc,Gb=["size"],jb=["innerProps","isRtl","size"],Kb={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},mf=function(t){var n=t.size,r=yr(t,Gb);return Ne("svg",Ze({height:n,width:n,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:Kb},r))},dl=function(t){return Ne(mf,Ze({size:20},t),Ne("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},pf=function(t){return Ne(mf,Ze({size:20},t),Ne("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},vf=function(t,n){var r=t.isFocused,i=t.theme,o=i.spacing.baseUnit,a=i.colors;return Xe({label:"indicatorContainer",display:"flex",transition:"color 150ms"},n?{}:{color:r?a.neutral60:a.neutral20,padding:o*2,":hover":{color:r?a.neutral80:a.neutral40}})},Zb=vf,Jb=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ze({},tn(t,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),r),n||Ne(pf,null))},Qb=vf,ew=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ze({},tn(t,"clearIndicator",{indicator:!0,"clear-indicator":!0}),r),n||Ne(dl,null))},tw=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing.baseUnit,a=i.colors;return Xe({label:"indicatorSeparator",alignSelf:"stretch",width:1},n?{}:{backgroundColor:r?a.neutral10:a.neutral20,marginBottom:o*2,marginTop:o*2})},nw=function(t){var n=t.innerProps;return Ne("span",Ze({},n,tn(t,"indicatorSeparator",{"indicator-separator":!0})))},rw=eg(sc||(sc=Q1([`
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
`]))),iw=function(t,n){var r=t.isFocused,i=t.size,o=t.theme,a=o.colors,l=o.spacing.baseUnit;return Xe({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:i,lineHeight:1,marginRight:i,textAlign:"center",verticalAlign:"middle"},n?{}:{color:r?a.neutral60:a.neutral20,padding:l*2})},gs=function(t){var n=t.delay,r=t.offset;return Ne("span",{css:$c({animation:"".concat(rw," 1s ease-in-out ").concat(n,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:r?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},ow=function(t){var n=t.innerProps,r=t.isRtl,i=t.size,o=i===void 0?4:i,a=yr(t,jb);return Ne("div",Ze({},tn(Xe(Xe({},a),{},{innerProps:n,isRtl:r,size:o}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),n),Ne(gs,{delay:0,offset:r}),Ne(gs,{delay:160,offset:!0}),Ne(gs,{delay:320,offset:!r}))},aw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.theme,a=o.colors,l=o.borderRadius,s=o.spacing;return Xe({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:s.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},n?{}:{backgroundColor:r?a.neutral5:a.neutral0,borderColor:r?a.neutral10:i?a.primary:a.neutral20,borderRadius:l,borderStyle:"solid",borderWidth:1,boxShadow:i?"0 0 0 1px ".concat(a.primary):void 0,"&:hover":{borderColor:i?a.primary:a.neutral30}})},sw=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.innerRef,a=t.innerProps,l=t.menuIsOpen;return Ne("div",Ze({ref:o},tn(t,"control",{control:!0,"control--is-disabled":r,"control--is-focused":i,"control--menu-is-open":l}),a,{"aria-disabled":r||void 0}),n)},lw=sw,uw=["data"],cw=function(t,n){var r=t.theme.spacing;return n?{}:{paddingBottom:r.baseUnit*2,paddingTop:r.baseUnit*2}},dw=function(t){var n=t.children,r=t.cx,i=t.getStyles,o=t.getClassNames,a=t.Heading,l=t.headingProps,s=t.innerProps,u=t.label,c=t.theme,d=t.selectProps;return Ne("div",Ze({},tn(t,"group",{group:!0}),s),Ne(a,Ze({},l,{selectProps:d,theme:c,getStyles:i,getClassNames:o,cx:r}),u),Ne("div",null,n))},fw=function(t,n){var r=t.theme,i=r.colors,o=r.spacing;return Xe({label:"group",cursor:"default",display:"block"},n?{}:{color:i.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:o.baseUnit*3,paddingRight:o.baseUnit*3,textTransform:"uppercase"})},hw=function(t){var n=uf(t);n.data;var r=yr(n,uw);return Ne("div",Ze({},tn(t,"groupHeading",{"group-heading":!0}),r))},gw=dw,mw=["innerRef","isDisabled","isHidden","inputClassName"],pw=function(t,n){var r=t.isDisabled,i=t.value,o=t.theme,a=o.spacing,l=o.colors;return Xe(Xe({visibility:r?"hidden":"visible",transform:i?"translateZ(0)":""},vw),n?{}:{margin:a.baseUnit/2,paddingBottom:a.baseUnit/2,paddingTop:a.baseUnit/2,color:l.neutral80})},bf={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},vw={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":Xe({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},bf)},bw=function(t){return Xe({label:"input",color:"inherit",background:0,opacity:t?0:1,width:"100%"},bf)},ww=function(t){var n=t.cx,r=t.value,i=uf(t),o=i.innerRef,a=i.isDisabled,l=i.isHidden,s=i.inputClassName,u=yr(i,mw);return Ne("div",Ze({},tn(t,"input",{"input-container":!0}),{"data-value":r||""}),Ne("input",Ze({className:n({input:!0},s),ref:o,style:bw(l),disabled:a},u)))},yw=ww,Cw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,a=r.colors;return Xe({label:"multiValue",display:"flex",minWidth:0},n?{}:{backgroundColor:a.neutral10,borderRadius:o/2,margin:i.baseUnit/2})},Sw=function(t,n){var r=t.theme,i=r.borderRadius,o=r.colors,a=t.cropWithEllipsis;return Xe({overflow:"hidden",textOverflow:a||a===void 0?"ellipsis":void 0,whiteSpace:"nowrap"},n?{}:{borderRadius:i/2,color:o.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},xw=function(t,n){var r=t.theme,i=r.spacing,o=r.borderRadius,a=r.colors,l=t.isFocused;return Xe({alignItems:"center",display:"flex"},n?{}:{borderRadius:o/2,backgroundColor:l?a.dangerLight:void 0,paddingLeft:i.baseUnit,paddingRight:i.baseUnit,":hover":{backgroundColor:a.dangerLight,color:a.danger}})},wf=function(t){var n=t.children,r=t.innerProps;return Ne("div",r,n)},kw=wf,Mw=wf;function Rw(e){var t=e.children,n=e.innerProps;return Ne("div",Ze({role:"button"},n),t||Ne(dl,{size:14}))}var Ew=function(t){var n=t.children,r=t.components,i=t.data,o=t.innerProps,a=t.isDisabled,l=t.removeProps,s=t.selectProps,u=r.Container,c=r.Label,d=r.Remove;return Ne(u,{data:i,innerProps:Xe(Xe({},tn(t,"multiValue",{"multi-value":!0,"multi-value--is-disabled":a})),o),selectProps:s},Ne(c,{data:i,innerProps:Xe({},tn(t,"multiValueLabel",{"multi-value__label":!0})),selectProps:s},n),Ne(d,{data:i,innerProps:Xe(Xe({},tn(t,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(n||"option")},l),selectProps:s}))},Iw=Ew,Tw=function(t,n){var r=t.isDisabled,i=t.isFocused,o=t.isSelected,a=t.theme,l=a.spacing,s=a.colors;return Xe({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},n?{}:{backgroundColor:o?s.primary:i?s.primary25:"transparent",color:r?s.neutral20:o?s.neutral0:"inherit",padding:"".concat(l.baseUnit*2,"px ").concat(l.baseUnit*3,"px"),":active":{backgroundColor:r?void 0:o?s.primary:s.primary50}})},Dw=function(t){var n=t.children,r=t.isDisabled,i=t.isFocused,o=t.isSelected,a=t.innerRef,l=t.innerProps;return Ne("div",Ze({},tn(t,"option",{option:!0,"option--is-disabled":r,"option--is-focused":i,"option--is-selected":o}),{ref:a,"aria-disabled":r},l),n)},Ow=Dw,Pw=function(t,n){var r=t.theme,i=r.spacing,o=r.colors;return Xe({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},n?{}:{color:o.neutral50,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},_w=function(t){var n=t.children,r=t.innerProps;return Ne("div",Ze({},tn(t,"placeholder",{placeholder:!0}),r),n)},Lw=_w,Fw=function(t,n){var r=t.isDisabled,i=t.theme,o=i.spacing,a=i.colors;return Xe({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},n?{}:{color:r?a.neutral40:a.neutral80,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},Aw=function(t){var n=t.children,r=t.isDisabled,i=t.innerProps;return Ne("div",Ze({},tn(t,"singleValue",{"single-value":!0,"single-value--is-disabled":r}),i),n)},Hw=Aw,yf={ClearIndicator:ew,Control:lw,DropdownIndicator:Jb,DownChevron:pf,CrossIcon:dl,Group:gw,GroupHeading:hw,IndicatorsContainer:Xb,IndicatorSeparator:nw,Input:yw,LoadingIndicator:ow,Menu:_b,MenuList:Fb,MenuPortal:Nb,LoadingMessage:Vb,NoOptionsMessage:zb,MultiValue:Iw,MultiValueContainer:kw,MultiValueLabel:Mw,MultiValueRemove:Rw,Option:Ow,Placeholder:Lw,SelectContainer:Wb,SingleValue:Hw,ValueContainer:qb},zw=function(t){return Xe(Xe({},yf),t.components)},lc=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function Vw(e,t){return!!(e===t||lc(e)&&lc(t))}function $w(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!Vw(e[n],t[n]))return!1;return!0}function Nw(e,t){t===void 0&&(t=$w);var n=null;function r(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];if(n&&n.lastThis===this&&t(i,n.lastArgs))return n.lastResult;var a=e.apply(this,i);return n={lastResult:a,lastArgs:i,lastThis:this},a}return r.clear=function(){n=null},r}var Bw={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},Ww=function(t){return Ne("span",Ze({css:Bw},t))},uc=Ww,Uw={guidance:function(t){var n=t.isSearchable,r=t.isMulti,i=t.tabSelectsValue,o=t.context,a=t.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(i?", press Tab to select the option and exit the menu":"",".");case"input":return a?"".concat(t["aria-label"]||"Select"," is focused ").concat(n?",type to refine list":"",", press Down to open the menu, ").concat(r?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(t){var n=t.action,r=t.label,i=r===void 0?"":r,o=t.labels,a=t.isDisabled;switch(n){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(i,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return a?"option ".concat(i," is disabled. Select another option."):"option ".concat(i,", selected.");default:return""}},onFocus:function(t){var n=t.context,r=t.focused,i=t.options,o=t.label,a=o===void 0?"":o,l=t.selectValue,s=t.isDisabled,u=t.isSelected,c=t.isAppleDevice,d=function(p,v){return p&&p.length?"".concat(p.indexOf(v)+1," of ").concat(p.length):""};if(n==="value"&&l)return"value ".concat(a," focused, ").concat(d(l,r),".");if(n==="menu"&&c){var h=s?" disabled":"",f="".concat(u?" selected":"").concat(h);return"".concat(a).concat(f,", ").concat(d(i,r),".")}return""},onFilter:function(t){var n=t.inputValue,r=t.resultsMessage;return"".concat(r).concat(n?" for search term "+n:"",".")}},qw=function(t){var n=t.ariaSelection,r=t.focusedOption,i=t.focusedValue,o=t.focusableOptions,a=t.isFocused,l=t.selectValue,s=t.selectProps,u=t.id,c=t.isAppleDevice,d=s.ariaLiveMessages,h=s.getOptionLabel,f=s.inputValue,m=s.isMulti,p=s.isOptionDisabled,v=s.isSearchable,w=s.menuIsOpen,b=s.options,M=s.screenReaderStatus,O=s.tabSelectsValue,S=s.isLoading,R=s["aria-label"],_=s["aria-live"],E=g.useMemo(function(){return Xe(Xe({},Uw),d||{})},[d]),x=g.useMemo(function(){var k="";if(n&&E.onChange){var z=n.option,N=n.options,X=n.removedValue,re=n.removedValues,G=n.value,j=function(P){return Array.isArray(P)?null:P},se=X||z||j(G),oe=se?h(se):"",ue=N||re||void 0,he=ue?ue.map(h):[],Q=Xe({isDisabled:se&&p(se,l),label:oe,labels:he},n);k=E.onChange(Q)}return k},[n,E,p,l,h]),L=g.useMemo(function(){var k="",z=r||i,N=!!(r&&l&&l.includes(r));if(z&&E.onFocus){var X={focused:z,label:h(z),isDisabled:p(z,l),isSelected:N,options:o,context:z===r?"menu":"value",selectValue:l,isAppleDevice:c};k=E.onFocus(X)}return k},[r,i,h,p,E,o,l,c]),D=g.useMemo(function(){var k="";if(w&&b.length&&!S&&E.onFilter){var z=M({count:o.length});k=E.onFilter({inputValue:f,resultsMessage:z})}return k},[o,f,w,E,b,M,S]),C=(n==null?void 0:n.action)==="initial-input-focus",I=g.useMemo(function(){var k="";if(E.guidance){var z=i?"value":w?"menu":"input";k=E.guidance({"aria-label":R,context:z,isDisabled:r&&p(r,l),isMulti:m,isSearchable:v,tabSelectsValue:O,isInitialFocus:C})}return k},[R,r,i,m,p,v,w,E,l,O,C]),T=Ne(g.Fragment,null,Ne("span",{id:"aria-selection"},x),Ne("span",{id:"aria-focused"},L),Ne("span",{id:"aria-results"},D),Ne("span",{id:"aria-guidance"},I));return Ne(g.Fragment,null,Ne(uc,{id:u},C&&T),Ne(uc,{"aria-live":_,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},a&&!C&&T))},Yw=qw,Ps=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],Xw=new RegExp("["+Ps.map(function(e){return e.letters}).join("")+"]","g"),Cf={};for(var ms=0;ms<Ps.length;ms++)for(var ps=Ps[ms],vs=0;vs<ps.letters.length;vs++)Cf[ps.letters[vs]]=ps.base;var Sf=function(t){return t.replace(Xw,function(n){return Cf[n]})},Gw=Nw(Sf),cc=function(t){return t.replace(/^\s+|\s+$/g,"")},jw=function(t){return"".concat(t.label," ").concat(t.value)},Kw=function(t){return function(n,r){if(n.data.__isNew__)return!0;var i=Xe({ignoreCase:!0,ignoreAccents:!0,stringify:jw,trim:!0,matchFrom:"any"},t),o=i.ignoreCase,a=i.ignoreAccents,l=i.stringify,s=i.trim,u=i.matchFrom,c=s?cc(r):r,d=s?cc(l(n)):l(n);return o&&(c=c.toLowerCase(),d=d.toLowerCase()),a&&(c=Gw(c),d=Sf(d)),u==="start"?d.substr(0,c.length)===c:d.indexOf(c)>-1}},Zw=["innerRef"];function Jw(e){var t=e.innerRef,n=yr(e,Zw),r=Mb(n,"onExited","in","enter","exit","appear");return Ne("input",Ze({ref:t},r,{css:$c({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var Qw=function(t){t.cancelable&&t.preventDefault(),t.stopPropagation()};function ey(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,i=e.onTopArrive,o=e.onTopLeave,a=g.useRef(!1),l=g.useRef(!1),s=g.useRef(0),u=g.useRef(null),c=g.useCallback(function(v,w){if(u.current!==null){var b=u.current,M=b.scrollTop,O=b.scrollHeight,S=b.clientHeight,R=u.current,_=w>0,E=O-S-M,x=!1;E>w&&a.current&&(r&&r(v),a.current=!1),_&&l.current&&(o&&o(v),l.current=!1),_&&w>E?(n&&!a.current&&n(v),R.scrollTop=O,x=!0,a.current=!0):!_&&-w>M&&(i&&!l.current&&i(v),R.scrollTop=0,x=!0,l.current=!0),x&&Qw(v)}},[n,r,i,o]),d=g.useCallback(function(v){c(v,v.deltaY)},[c]),h=g.useCallback(function(v){s.current=v.changedTouches[0].clientY},[]),f=g.useCallback(function(v){var w=s.current-v.changedTouches[0].clientY;c(v,w)},[c]),m=g.useCallback(function(v){if(v){var w=Sb?{passive:!1}:!1;v.addEventListener("wheel",d,w),v.addEventListener("touchstart",h,w),v.addEventListener("touchmove",f,w)}},[f,h,d]),p=g.useCallback(function(v){v&&(v.removeEventListener("wheel",d,!1),v.removeEventListener("touchstart",h,!1),v.removeEventListener("touchmove",f,!1))},[f,h,d]);return g.useEffect(function(){if(t){var v=u.current;return m(v),function(){p(v)}}},[t,m,p]),function(v){u.current=v}}var dc=["boxSizing","height","overflow","paddingRight","position"],fc={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function hc(e){e.preventDefault()}function gc(e){e.stopPropagation()}function mc(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;e===0?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function pc(){return"ontouchstart"in window||navigator.maxTouchPoints}var vc=!!(typeof window<"u"&&window.document&&window.document.createElement),Gi=0,fi={capture:!1,passive:!1};function ty(e){var t=e.isEnabled,n=e.accountForScrollbars,r=n===void 0?!0:n,i=g.useRef({}),o=g.useRef(null),a=g.useCallback(function(s){if(vc){var u=document.body,c=u&&u.style;if(r&&dc.forEach(function(m){var p=c&&c[m];i.current[m]=p}),r&&Gi<1){var d=parseInt(i.current.paddingRight,10)||0,h=document.body?document.body.clientWidth:0,f=window.innerWidth-h+d||0;Object.keys(fc).forEach(function(m){var p=fc[m];c&&(c[m]=p)}),c&&(c.paddingRight="".concat(f,"px"))}u&&pc()&&(u.addEventListener("touchmove",hc,fi),s&&(s.addEventListener("touchstart",mc,fi),s.addEventListener("touchmove",gc,fi))),Gi+=1}},[r]),l=g.useCallback(function(s){if(vc){var u=document.body,c=u&&u.style;Gi=Math.max(Gi-1,0),r&&Gi<1&&dc.forEach(function(d){var h=i.current[d];c&&(c[d]=h)}),u&&pc()&&(u.removeEventListener("touchmove",hc,fi),s&&(s.removeEventListener("touchstart",mc,fi),s.removeEventListener("touchmove",gc,fi)))}},[r]);return g.useEffect(function(){if(t){var s=o.current;return a(s),function(){l(s)}}},[t,a,l]),function(s){o.current=s}}var ny=function(t){var n=t.target;return n.ownerDocument.activeElement&&n.ownerDocument.activeElement.blur()},ry={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function iy(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,i=r===void 0?!0:r,o=e.onBottomArrive,a=e.onBottomLeave,l=e.onTopArrive,s=e.onTopLeave,u=ey({isEnabled:i,onBottomArrive:o,onBottomLeave:a,onTopArrive:l,onTopLeave:s}),c=ty({isEnabled:n}),d=function(f){u(f),c(f)};return Ne(g.Fragment,null,n&&Ne("div",{onClick:ny,css:ry}),t(d))}var oy={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},ay=function(t){var n=t.name,r=t.onFocus;return Ne("input",{required:!0,name:n,tabIndex:-1,"aria-hidden":"true",onFocus:r,css:oy,value:"",onChange:function(){}})},sy=ay;function fl(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function ly(){return fl(/^iPhone/i)}function xf(){return fl(/^Mac/i)}function uy(){return fl(/^iPad/i)||xf()&&navigator.maxTouchPoints>1}function cy(){return ly()||uy()}function dy(){return xf()||cy()}var fy=function(t){return t.label},hy=function(t){return t.label},gy=function(t){return t.value},my=function(t){return!!t.isDisabled},py={clearIndicator:Qb,container:Bb,control:aw,dropdownIndicator:Zb,group:cw,groupHeading:fw,indicatorsContainer:Yb,indicatorSeparator:tw,input:pw,loadingIndicator:iw,loadingMessage:Hb,menu:Db,menuList:Lb,menuPortal:$b,multiValue:Cw,multiValueLabel:Sw,multiValueRemove:xw,noOptionsMessage:Ab,option:Tw,placeholder:Pw,singleValue:Fw,valueContainer:Ub},vy={primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},by=4,kf=4,wy=38,yy=kf*2,Cy={baseUnit:kf,controlHeight:wy,menuGutter:yy},bs={borderRadius:by,colors:vy,spacing:Cy},Sy={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:ac(),captureMenuScroll:!ac(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:Kw(),formatGroupLabel:fy,getOptionLabel:hy,getOptionValue:gy,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:my,loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!yb(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(t){var n=t.count;return"".concat(n," result").concat(n!==1?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function bc(e,t,n,r){var i=Ef(e,t,n),o=If(e,t,n),a=Rf(e,t),l=wa(e,t);return{type:"option",data:t,isDisabled:i,isSelected:o,label:a,value:l,index:r}}function na(e,t){return e.options.map(function(n,r){if("options"in n){var i=n.options.map(function(a,l){return bc(e,a,t,l)}).filter(function(a){return yc(e,a)});return i.length>0?{type:"group",data:n,options:i,index:r}:void 0}var o=bc(e,n,t,r);return yc(e,o)?o:void 0}).filter(xb)}function Mf(e){return e.reduce(function(t,n){return n.type==="group"?t.push.apply(t,As(n.options.map(function(r){return r.data}))):t.push(n.data),t},[])}function wc(e,t){return e.reduce(function(n,r){return r.type==="group"?n.push.apply(n,As(r.options.map(function(i){return{data:i.data,id:"".concat(t,"-").concat(r.index,"-").concat(i.index)}}))):n.push({data:r.data,id:"".concat(t,"-").concat(r.index)}),n},[])}function xy(e,t){return Mf(na(e,t))}function yc(e,t){var n=e.inputValue,r=n===void 0?"":n,i=t.data,o=t.isSelected,a=t.label,l=t.value;return(!Df(e)||!o)&&Tf(e,{label:a,value:l,data:i},r)}function ky(e,t){var n=e.focusedValue,r=e.selectValue,i=r.indexOf(n);if(i>-1){var o=t.indexOf(n);if(o>-1)return n;if(i<t.length)return t[i]}return null}function My(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}var ws=function(t,n){var r,i=(r=t.find(function(o){return o.data===n}))===null||r===void 0?void 0:r.id;return i||null},Rf=function(t,n){return t.getOptionLabel(n)},wa=function(t,n){return t.getOptionValue(n)};function Ef(e,t,n){return typeof e.isOptionDisabled=="function"?e.isOptionDisabled(t,n):!1}function If(e,t,n){if(n.indexOf(t)>-1)return!0;if(typeof e.isOptionSelected=="function")return e.isOptionSelected(t,n);var r=wa(e,t);return n.some(function(i){return wa(e,i)===r})}function Tf(e,t,n){return e.filterOption?e.filterOption(t,n):!0}var Df=function(t){var n=t.hideSelectedOptions,r=t.isMulti;return n===void 0?r:n},Ry=1,Of=function(e){wg(n,e);var t=Sg(n);function n(r){var i;if(Cg(this,n),i=t.call(this,r),i.state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},i.blockOptionHover=!1,i.isComposing=!1,i.commonProps=void 0,i.initialTouchX=0,i.initialTouchY=0,i.openAfterFocus=!1,i.scrollToFocusedOptionOnUpdate=!1,i.userIsDragging=void 0,i.isAppleDevice=dy(),i.controlRef=null,i.getControlRef=function(s){i.controlRef=s},i.focusedOptionRef=null,i.getFocusedOptionRef=function(s){i.focusedOptionRef=s},i.menuListRef=null,i.getMenuListRef=function(s){i.menuListRef=s},i.inputRef=null,i.getInputRef=function(s){i.inputRef=s},i.focus=i.focusInput,i.blur=i.blurInput,i.onChange=function(s,u){var c=i.props,d=c.onChange,h=c.name;u.name=h,i.ariaOnChange(s,u),d(s,u)},i.setValue=function(s,u,c){var d=i.props,h=d.closeMenuOnSelect,f=d.isMulti,m=d.inputValue;i.onInputChange("",{action:"set-value",prevInputValue:m}),h&&(i.setState({inputIsHiddenAfterUpdate:!f}),i.onMenuClose()),i.setState({clearFocusValueOnUpdate:!0}),i.onChange(s,{action:u,option:c})},i.selectOption=function(s){var u=i.props,c=u.blurInputOnSelect,d=u.isMulti,h=u.name,f=i.state.selectValue,m=d&&i.isOptionSelected(s,f),p=i.isOptionDisabled(s,f);if(m){var v=i.getOptionValue(s);i.setValue(f.filter(function(w){return i.getOptionValue(w)!==v}),"deselect-option",s)}else if(!p)d?i.setValue([].concat(As(f),[s]),"select-option",s):i.setValue(s,"select-option");else{i.ariaOnChange(s,{action:"select-option",option:s,name:h});return}c&&i.blurInput()},i.removeValue=function(s){var u=i.props.isMulti,c=i.state.selectValue,d=i.getOptionValue(s),h=c.filter(function(m){return i.getOptionValue(m)!==d}),f=Ko(u,h,h[0]||null);i.onChange(f,{action:"remove-value",removedValue:s}),i.focusInput()},i.clearValue=function(){var s=i.state.selectValue;i.onChange(Ko(i.props.isMulti,[],null),{action:"clear",removedValues:s})},i.popValue=function(){var s=i.props.isMulti,u=i.state.selectValue,c=u[u.length-1],d=u.slice(0,u.length-1),h=Ko(s,d,d[0]||null);c&&i.onChange(h,{action:"pop-value",removedValue:c})},i.getFocusedOptionId=function(s){return ws(i.state.focusableOptionsWithIds,s)},i.getFocusableOptionsWithIds=function(){return wc(na(i.props,i.state.selectValue),i.getElementId("option"))},i.getValue=function(){return i.state.selectValue},i.cx=function(){for(var s=arguments.length,u=new Array(s),c=0;c<s;c++)u[c]=arguments[c];return mb.apply(void 0,[i.props.classNamePrefix].concat(u))},i.getOptionLabel=function(s){return Rf(i.props,s)},i.getOptionValue=function(s){return wa(i.props,s)},i.getStyles=function(s,u){var c=i.props.unstyled,d=py[s](u,c);d.boxSizing="border-box";var h=i.props.styles[s];return h?h(d,u):d},i.getClassNames=function(s,u){var c,d;return(c=(d=i.props.classNames)[s])===null||c===void 0?void 0:c.call(d,u)},i.getElementId=function(s){return"".concat(i.state.instancePrefix,"-").concat(s)},i.getComponents=function(){return zw(i.props)},i.buildCategorizedOptions=function(){return na(i.props,i.state.selectValue)},i.getCategorizedOptions=function(){return i.props.menuIsOpen?i.buildCategorizedOptions():[]},i.buildFocusableOptions=function(){return Mf(i.buildCategorizedOptions())},i.getFocusableOptions=function(){return i.props.menuIsOpen?i.buildFocusableOptions():[]},i.ariaOnChange=function(s,u){i.setState({ariaSelection:Xe({value:s},u)})},i.onMenuMouseDown=function(s){s.button===0&&(s.stopPropagation(),s.preventDefault(),i.focusInput())},i.onMenuMouseMove=function(s){i.blockOptionHover=!1},i.onControlMouseDown=function(s){if(!s.defaultPrevented){var u=i.props.openMenuOnClick;i.state.isFocused?i.props.menuIsOpen?s.target.tagName!=="INPUT"&&s.target.tagName!=="TEXTAREA"&&i.onMenuClose():u&&i.openMenu("first"):(u&&(i.openAfterFocus=!0),i.focusInput()),s.target.tagName!=="INPUT"&&s.target.tagName!=="TEXTAREA"&&s.preventDefault()}},i.onDropdownIndicatorMouseDown=function(s){if(!(s&&s.type==="mousedown"&&s.button!==0)&&!i.props.isDisabled){var u=i.props,c=u.isMulti,d=u.menuIsOpen;i.focusInput(),d?(i.setState({inputIsHiddenAfterUpdate:!c}),i.onMenuClose()):i.openMenu("first"),s.preventDefault()}},i.onClearIndicatorMouseDown=function(s){s&&s.type==="mousedown"&&s.button!==0||(i.clearValue(),s.preventDefault(),i.openAfterFocus=!1,s.type==="touchend"?i.focusInput():setTimeout(function(){return i.focusInput()}))},i.onScroll=function(s){typeof i.props.closeMenuOnScroll=="boolean"?s.target instanceof HTMLElement&&Ra(s.target)&&i.props.onMenuClose():typeof i.props.closeMenuOnScroll=="function"&&i.props.closeMenuOnScroll(s)&&i.props.onMenuClose()},i.onCompositionStart=function(){i.isComposing=!0},i.onCompositionEnd=function(){i.isComposing=!1},i.onTouchStart=function(s){var u=s.touches,c=u&&u.item(0);c&&(i.initialTouchX=c.clientX,i.initialTouchY=c.clientY,i.userIsDragging=!1)},i.onTouchMove=function(s){var u=s.touches,c=u&&u.item(0);if(c){var d=Math.abs(c.clientX-i.initialTouchX),h=Math.abs(c.clientY-i.initialTouchY),f=5;i.userIsDragging=d>f||h>f}},i.onTouchEnd=function(s){i.userIsDragging||(i.controlRef&&!i.controlRef.contains(s.target)&&i.menuListRef&&!i.menuListRef.contains(s.target)&&i.blurInput(),i.initialTouchX=0,i.initialTouchY=0)},i.onControlTouchEnd=function(s){i.userIsDragging||i.onControlMouseDown(s)},i.onClearIndicatorTouchEnd=function(s){i.userIsDragging||i.onClearIndicatorMouseDown(s)},i.onDropdownIndicatorTouchEnd=function(s){i.userIsDragging||i.onDropdownIndicatorMouseDown(s)},i.handleInputChange=function(s){var u=i.props.inputValue,c=s.currentTarget.value;i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange(c,{action:"input-change",prevInputValue:u}),i.props.menuIsOpen||i.onMenuOpen()},i.onInputFocus=function(s){i.props.onFocus&&i.props.onFocus(s),i.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(i.openAfterFocus||i.props.openMenuOnFocus)&&i.openMenu("first"),i.openAfterFocus=!1},i.onInputBlur=function(s){var u=i.props.inputValue;if(i.menuListRef&&i.menuListRef.contains(document.activeElement)){i.inputRef.focus();return}i.props.onBlur&&i.props.onBlur(s),i.onInputChange("",{action:"input-blur",prevInputValue:u}),i.onMenuClose(),i.setState({focusedValue:null,isFocused:!1})},i.onOptionHover=function(s){if(!(i.blockOptionHover||i.state.focusedOption===s)){var u=i.getFocusableOptions(),c=u.indexOf(s);i.setState({focusedOption:s,focusedOptionId:c>-1?i.getFocusedOptionId(s):null})}},i.shouldHideSelectedOptions=function(){return Df(i.props)},i.onValueInputFocus=function(s){s.preventDefault(),s.stopPropagation(),i.focus()},i.onKeyDown=function(s){var u=i.props,c=u.isMulti,d=u.backspaceRemovesValue,h=u.escapeClearsValue,f=u.inputValue,m=u.isClearable,p=u.isDisabled,v=u.menuIsOpen,w=u.onKeyDown,b=u.tabSelectsValue,M=u.openMenuOnFocus,O=i.state,S=O.focusedOption,R=O.focusedValue,_=O.selectValue;if(!p&&!(typeof w=="function"&&(w(s),s.defaultPrevented))){switch(i.blockOptionHover=!0,s.key){case"ArrowLeft":if(!c||f)return;i.focusValue("previous");break;case"ArrowRight":if(!c||f)return;i.focusValue("next");break;case"Delete":case"Backspace":if(f)return;if(R)i.removeValue(R);else{if(!d)return;c?i.popValue():m&&i.clearValue()}break;case"Tab":if(i.isComposing||s.shiftKey||!v||!b||!S||M&&i.isOptionSelected(S,_))return;i.selectOption(S);break;case"Enter":if(s.keyCode===229)break;if(v){if(!S||i.isComposing)return;i.selectOption(S);break}return;case"Escape":v?(i.setState({inputIsHiddenAfterUpdate:!1}),i.onInputChange("",{action:"menu-close",prevInputValue:f}),i.onMenuClose()):m&&h&&i.clearValue();break;case" ":if(f)return;if(!v){i.openMenu("first");break}if(!S)return;i.selectOption(S);break;case"ArrowUp":v?i.focusOption("up"):i.openMenu("last");break;case"ArrowDown":v?i.focusOption("down"):i.openMenu("first");break;case"PageUp":if(!v)return;i.focusOption("pageup");break;case"PageDown":if(!v)return;i.focusOption("pagedown");break;case"Home":if(!v)return;i.focusOption("first");break;case"End":if(!v)return;i.focusOption("last");break;default:return}s.preventDefault()}},i.state.instancePrefix="react-select-"+(i.props.instanceId||++Ry),i.state.selectValue=ic(r.value),r.menuIsOpen&&i.state.selectValue.length){var o=i.getFocusableOptionsWithIds(),a=i.buildFocusableOptions(),l=a.indexOf(i.state.selectValue[0]);i.state.focusableOptionsWithIds=o,i.state.focusedOption=a[l],i.state.focusedOptionId=ws(o,a[l])}return i}return yg(n,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&oc(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(i){var o=this.props,a=o.isDisabled,l=o.menuIsOpen,s=this.state.isFocused;(s&&!a&&i.isDisabled||s&&l&&!i.menuIsOpen)&&this.focusInput(),s&&a&&!i.isDisabled?this.setState({isFocused:!1},this.onMenuClose):!s&&!a&&i.isDisabled&&this.inputRef===document.activeElement&&this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(oc(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(i,o){this.props.onInputChange(i,o)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(i){var o=this,a=this.state,l=a.selectValue,s=a.isFocused,u=this.buildFocusableOptions(),c=i==="first"?0:u.length-1;if(!this.props.isMulti){var d=u.indexOf(l[0]);d>-1&&(c=d)}this.scrollToFocusedOptionOnUpdate=!(s&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:u[c],focusedOptionId:this.getFocusedOptionId(u[c])},function(){return o.onMenuOpen()})}},{key:"focusValue",value:function(i){var o=this.state,a=o.selectValue,l=o.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var s=a.indexOf(l);l||(s=-1);var u=a.length-1,c=-1;if(a.length){switch(i){case"previous":s===0?c=0:s===-1?c=u:c=s-1;break;case"next":s>-1&&s<u&&(c=s+1);break}this.setState({inputIsHidden:c!==-1,focusedValue:a[c]})}}}},{key:"focusOption",value:function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"first",o=this.props.pageSize,a=this.state.focusedOption,l=this.getFocusableOptions();if(l.length){var s=0,u=l.indexOf(a);a||(u=-1),i==="up"?s=u>0?u-1:l.length-1:i==="down"?s=(u+1)%l.length:i==="pageup"?(s=u-o,s<0&&(s=0)):i==="pagedown"?(s=u+o,s>l.length-1&&(s=l.length-1)):i==="last"&&(s=l.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:l[s],focusedValue:null,focusedOptionId:this.getFocusedOptionId(l[s])})}}},{key:"getTheme",value:function(){return this.props.theme?typeof this.props.theme=="function"?this.props.theme(bs):Xe(Xe({},bs),this.props.theme):bs}},{key:"getCommonProps",value:function(){var i=this.clearValue,o=this.cx,a=this.getStyles,l=this.getClassNames,s=this.getValue,u=this.selectOption,c=this.setValue,d=this.props,h=d.isMulti,f=d.isRtl,m=d.options,p=this.hasValue();return{clearValue:i,cx:o,getStyles:a,getClassNames:l,getValue:s,hasValue:p,isMulti:h,isRtl:f,options:m,selectOption:u,selectProps:d,setValue:c,theme:this.getTheme()}}},{key:"hasValue",value:function(){var i=this.state.selectValue;return i.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var i=this.props,o=i.isClearable,a=i.isMulti;return o===void 0?a:o}},{key:"isOptionDisabled",value:function(i,o){return Ef(this.props,i,o)}},{key:"isOptionSelected",value:function(i,o){return If(this.props,i,o)}},{key:"filterOption",value:function(i,o){return Tf(this.props,i,o)}},{key:"formatOptionLabel",value:function(i,o){if(typeof this.props.formatOptionLabel=="function"){var a=this.props.inputValue,l=this.state.selectValue;return this.props.formatOptionLabel(i,{context:o,inputValue:a,selectValue:l})}else return this.getOptionLabel(i)}},{key:"formatGroupLabel",value:function(i){return this.props.formatGroupLabel(i)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var i=this.props,o=i.isDisabled,a=i.isSearchable,l=i.inputId,s=i.inputValue,u=i.tabIndex,c=i.form,d=i.menuIsOpen,h=i.required,f=this.getComponents(),m=f.Input,p=this.state,v=p.inputIsHidden,w=p.ariaSelection,b=this.commonProps,M=l||this.getElementId("input"),O=Xe(Xe(Xe({"aria-autocomplete":"list","aria-expanded":d,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":h,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},d&&{"aria-controls":this.getElementId("listbox")}),!a&&{"aria-readonly":!0}),this.hasValue()?(w==null?void 0:w.action)==="initial-input-focus"&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return a?g.createElement(m,Ze({},b,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:M,innerRef:this.getInputRef,isDisabled:o,isHidden:v,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:s},O)):g.createElement(Jw,Ze({id:M,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:va,onFocus:this.onInputFocus,disabled:o,tabIndex:u,inputMode:"none",form:c,value:""},O))}},{key:"renderPlaceholderOrValue",value:function(){var i=this,o=this.getComponents(),a=o.MultiValue,l=o.MultiValueContainer,s=o.MultiValueLabel,u=o.MultiValueRemove,c=o.SingleValue,d=o.Placeholder,h=this.commonProps,f=this.props,m=f.controlShouldRenderValue,p=f.isDisabled,v=f.isMulti,w=f.inputValue,b=f.placeholder,M=this.state,O=M.selectValue,S=M.focusedValue,R=M.isFocused;if(!this.hasValue()||!m)return w?null:g.createElement(d,Ze({},h,{key:"placeholder",isDisabled:p,isFocused:R,innerProps:{id:this.getElementId("placeholder")}}),b);if(v)return O.map(function(E,x){var L=E===S,D="".concat(i.getOptionLabel(E),"-").concat(i.getOptionValue(E));return g.createElement(a,Ze({},h,{components:{Container:l,Label:s,Remove:u},isFocused:L,isDisabled:p,key:D,index:x,removeProps:{onClick:function(){return i.removeValue(E)},onTouchEnd:function(){return i.removeValue(E)},onMouseDown:function(I){I.preventDefault()}},data:E}),i.formatOptionLabel(E,"value"))});if(w)return null;var _=O[0];return g.createElement(c,Ze({},h,{data:_,isDisabled:p}),this.formatOptionLabel(_,"value"))}},{key:"renderClearIndicator",value:function(){var i=this.getComponents(),o=i.ClearIndicator,a=this.commonProps,l=this.props,s=l.isDisabled,u=l.isLoading,c=this.state.isFocused;if(!this.isClearable()||!o||s||!this.hasValue()||u)return null;var d={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return g.createElement(o,Ze({},a,{innerProps:d,isFocused:c}))}},{key:"renderLoadingIndicator",value:function(){var i=this.getComponents(),o=i.LoadingIndicator,a=this.commonProps,l=this.props,s=l.isDisabled,u=l.isLoading,c=this.state.isFocused;if(!o||!u)return null;var d={"aria-hidden":"true"};return g.createElement(o,Ze({},a,{innerProps:d,isDisabled:s,isFocused:c}))}},{key:"renderIndicatorSeparator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator,a=i.IndicatorSeparator;if(!o||!a)return null;var l=this.commonProps,s=this.props.isDisabled,u=this.state.isFocused;return g.createElement(a,Ze({},l,{isDisabled:s,isFocused:u}))}},{key:"renderDropdownIndicator",value:function(){var i=this.getComponents(),o=i.DropdownIndicator;if(!o)return null;var a=this.commonProps,l=this.props.isDisabled,s=this.state.isFocused,u={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return g.createElement(o,Ze({},a,{innerProps:u,isDisabled:l,isFocused:s}))}},{key:"renderMenu",value:function(){var i=this,o=this.getComponents(),a=o.Group,l=o.GroupHeading,s=o.Menu,u=o.MenuList,c=o.MenuPortal,d=o.LoadingMessage,h=o.NoOptionsMessage,f=o.Option,m=this.commonProps,p=this.state.focusedOption,v=this.props,w=v.captureMenuScroll,b=v.inputValue,M=v.isLoading,O=v.loadingMessage,S=v.minMenuHeight,R=v.maxMenuHeight,_=v.menuIsOpen,E=v.menuPlacement,x=v.menuPosition,L=v.menuPortalTarget,D=v.menuShouldBlockScroll,C=v.menuShouldScrollIntoView,I=v.noOptionsMessage,T=v.onMenuScrollToTop,k=v.onMenuScrollToBottom;if(!_)return null;var z=function(oe,ue){var he=oe.type,Q=oe.data,H=oe.isDisabled,P=oe.isSelected,W=oe.label,ce=oe.value,Oe=p===Q,ze=H?void 0:function(){return i.onOptionHover(Q)},Ce=H?void 0:function(){return i.selectOption(Q)},Ee="".concat(i.getElementId("option"),"-").concat(ue),xe={id:Ee,onClick:Ce,onMouseMove:ze,onMouseOver:ze,tabIndex:-1,role:"option","aria-selected":i.isAppleDevice?void 0:P};return g.createElement(f,Ze({},m,{innerProps:xe,data:Q,isDisabled:H,isSelected:P,key:Ee,label:W,type:he,value:ce,isFocused:Oe,innerRef:Oe?i.getFocusedOptionRef:void 0}),i.formatOptionLabel(oe.data,"menu"))},N;if(this.hasOptions())N=this.getCategorizedOptions().map(function(se){if(se.type==="group"){var oe=se.data,ue=se.options,he=se.index,Q="".concat(i.getElementId("group"),"-").concat(he),H="".concat(Q,"-heading");return g.createElement(a,Ze({},m,{key:Q,data:oe,options:ue,Heading:l,headingProps:{id:H,data:se.data},label:i.formatGroupLabel(se.data)}),se.options.map(function(P){return z(P,"".concat(he,"-").concat(P.index))}))}else if(se.type==="option")return z(se,"".concat(se.index))});else if(M){var X=O({inputValue:b});if(X===null)return null;N=g.createElement(d,m,X)}else{var re=I({inputValue:b});if(re===null)return null;N=g.createElement(h,m,re)}var G={minMenuHeight:S,maxMenuHeight:R,menuPlacement:E,menuPosition:x,menuShouldScrollIntoView:C},j=g.createElement(Ob,Ze({},m,G),function(se){var oe=se.ref,ue=se.placerProps,he=ue.placement,Q=ue.maxHeight;return g.createElement(s,Ze({},m,G,{innerRef:oe,innerProps:{onMouseDown:i.onMenuMouseDown,onMouseMove:i.onMenuMouseMove},isLoading:M,placement:he}),g.createElement(iy,{captureEnabled:w,onTopArrive:T,onBottomArrive:k,lockEnabled:D},function(H){return g.createElement(u,Ze({},m,{innerRef:function(W){i.getMenuListRef(W),H(W)},innerProps:{role:"listbox","aria-multiselectable":m.isMulti,id:i.getElementId("listbox")},isLoading:M,maxHeight:Q,focusedOption:p}),N)}))});return L||x==="fixed"?g.createElement(c,Ze({},m,{appendTo:L,controlElement:this.controlRef,menuPlacement:E,menuPosition:x}),j):j}},{key:"renderFormField",value:function(){var i=this,o=this.props,a=o.delimiter,l=o.isDisabled,s=o.isMulti,u=o.name,c=o.required,d=this.state.selectValue;if(c&&!this.hasValue()&&!l)return g.createElement(sy,{name:u,onFocus:this.onValueInputFocus});if(!(!u||l))if(s)if(a){var h=d.map(function(p){return i.getOptionValue(p)}).join(a);return g.createElement("input",{name:u,type:"hidden",value:h})}else{var f=d.length>0?d.map(function(p,v){return g.createElement("input",{key:"i-".concat(v),name:u,type:"hidden",value:i.getOptionValue(p)})}):g.createElement("input",{name:u,type:"hidden",value:""});return g.createElement("div",null,f)}else{var m=d[0]?this.getOptionValue(d[0]):"";return g.createElement("input",{name:u,type:"hidden",value:m})}}},{key:"renderLiveRegion",value:function(){var i=this.commonProps,o=this.state,a=o.ariaSelection,l=o.focusedOption,s=o.focusedValue,u=o.isFocused,c=o.selectValue,d=this.getFocusableOptions();return g.createElement(Yw,Ze({},i,{id:this.getElementId("live-region"),ariaSelection:a,focusedOption:l,focusedValue:s,isFocused:u,selectValue:c,focusableOptions:d,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var i=this.getComponents(),o=i.Control,a=i.IndicatorsContainer,l=i.SelectContainer,s=i.ValueContainer,u=this.props,c=u.className,d=u.id,h=u.isDisabled,f=u.menuIsOpen,m=this.state.isFocused,p=this.commonProps=this.getCommonProps();return g.createElement(l,Ze({},p,{className:c,innerProps:{id:d,onKeyDown:this.onKeyDown},isDisabled:h,isFocused:m}),this.renderLiveRegion(),g.createElement(o,Ze({},p,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:h,isFocused:m,menuIsOpen:f}),g.createElement(s,Ze({},p,{isDisabled:h}),this.renderPlaceholderOrValue(),this.renderInput()),g.createElement(a,Ze({},p,{isDisabled:h}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(i,o){var a=o.prevProps,l=o.clearFocusValueOnUpdate,s=o.inputIsHiddenAfterUpdate,u=o.ariaSelection,c=o.isFocused,d=o.prevWasFocused,h=o.instancePrefix,f=i.options,m=i.value,p=i.menuIsOpen,v=i.inputValue,w=i.isMulti,b=ic(m),M={};if(a&&(m!==a.value||f!==a.options||p!==a.menuIsOpen||v!==a.inputValue)){var O=p?xy(i,b):[],S=p?wc(na(i,b),"".concat(h,"-option")):[],R=l?ky(o,b):null,_=My(o,O),E=ws(S,_);M={selectValue:b,focusedOption:_,focusedOptionId:E,focusableOptionsWithIds:S,focusedValue:R,clearFocusValueOnUpdate:!1}}var x=s!=null&&i!==a?{inputIsHidden:s,inputIsHiddenAfterUpdate:void 0}:{},L=u,D=c&&d;return c&&!D&&(L={value:Ko(w,b,b[0]||null),options:b,action:"initial-input-focus"},D=!d),(u==null?void 0:u.action)==="initial-input-focus"&&(L=null),Xe(Xe(Xe({},M),x),{},{prevProps:i,ariaSelection:L,prevWasFocused:D})}}]),n}(g.Component);Of.defaultProps=Sy;var Ey=g.forwardRef(function(e,t){var n=J1(e);return g.createElement(Of,Ze({ref:t},n))}),Iy=Ey;const Ty=e=>{const{Menu:t}=yf,{children:n,...r}=e;return g.createElement(t,{...r},n)},Dy=mn("div")({name:"Wrap",class:"gdg-wghi2zc",propsAsIs:!1}),Oy=mn("div")({name:"PortalWrap",class:"gdg-p13nj8j0",propsAsIs:!1}),Py=mn("div")({name:"ReadOnlyWrap",class:"gdg-r6sia3g",propsAsIs:!1}),_y=e=>{const{value:t,onFinishedEditing:n,initialValue:r}=e,{allowedValues:i,value:o}=t.data,[a,l]=g.useState(o),[s,u]=g.useState(r??""),c=Gm(),d=g.useMemo(()=>i.map(h=>typeof h=="string"||h===null||h===void 0?{value:h,label:(h==null?void 0:h.toString())??""}:h),[i]);return t.readonly?g.createElement(Py,null,g.createElement(Zr,{highlight:!0,autoFocus:!1,disabled:!0,value:a??"",onChange:()=>{}})):g.createElement(Dy,null,g.createElement(Iy,{className:"glide-select",inputValue:s,onInputChange:u,menuPlacement:"auto",value:d.find(h=>h.value===a),styles:{control:h=>({...h,border:0,boxShadow:"none"}),option:(h,{isFocused:f})=>({...h,fontSize:c.editorFontSize,fontFamily:c.fontFamily,cursor:f?"pointer":void 0,paddingLeft:c.cellHorizontalPadding,paddingRight:c.cellHorizontalPadding,":active":{...h[":active"],color:c.accentFg},":empty::after":{content:'"&nbsp;"',visibility:"hidden"}})},theme:h=>({...h,colors:{...h.colors,neutral0:c.bgCell,neutral5:c.bgCell,neutral10:c.bgCell,neutral20:c.bgCellMedium,neutral30:c.bgCellMedium,neutral40:c.bgCellMedium,neutral50:c.textLight,neutral60:c.textMedium,neutral70:c.textMedium,neutral80:c.textDark,neutral90:c.textDark,neutral100:c.textDark,primary:c.accentColor,primary75:c.accentColor,primary50:c.accentColor,primary25:c.accentLight}}),menuPortalTarget:document.getElementById("portal"),autoFocus:!0,openMenuOnFocus:!0,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null,Menu:h=>g.createElement(Oy,null,g.createElement(Ty,{className:"click-outside-ignore",...h}))},options:d,onChange:async h=>{h!==null&&(l(h.value),await new Promise(f=>window.requestAnimationFrame(f)),n({...t,data:{...t.data,value:h.value}}))}}))},Ly={kind:te.Custom,isMatch:e=>e.data.kind==="dropdown-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{value:o}=t.data,a=t.data.allowedValues.find(s=>typeof s=="string"||s===null||s===void 0?s===o:s.value===o),l=typeof a=="string"?a:(a==null?void 0:a.label)??"";return l&&(n.fillStyle=r.textDark,n.fillText(l,i.x+r.cellHorizontalPadding,i.y+i.height/2+dr(n,r))),!0},measure:(e,t,n)=>{const{value:r}=t.data;return(r?e.measureText(r).width:0)+n.cellHorizontalPadding*2},provideEditor:()=>({editor:_y,disablePadding:!0,deletedValue:e=>({...e,copyData:"",data:{...e.data,value:""}})}),onPaste:(e,t)=>({...t,value:t.allowedValues.includes(e)?e:t.value})},Ur=6,Fy={marginRight:8},Ay={display:"flex",alignItems:"center",flexGrow:1},Hy={kind:te.Custom,isMatch:e=>e.data.kind==="range-cell",draw:(e,t)=>{const{ctx:n,theme:r,rect:i}=e,{min:o,max:a,value:l,label:s,measureLabel:u}=t.data,c=i.x+r.cellHorizontalPadding,d=i.y+i.height/2,h=a-o,f=(l-o)/h;n.save();let m=0;s!==void 0&&(n.font=`12px ${r.fontFamily}`,m=Qr(u??s,n,`12px ${r.fontFamily}`).width+r.cellHorizontalPadding);const p=i.width-r.cellHorizontalPadding*2-m;if(p>=Ur){const v=n.createLinearGradient(c,d,c+p,d);v.addColorStop(0,r.accentColor),v.addColorStop(f,r.accentColor),v.addColorStop(f,r.bgBubble),v.addColorStop(1,r.bgBubble),n.beginPath(),n.fillStyle=v,Qu(n,c,d-Ur/2,p,Ur,Ur/2),n.fill(),n.beginPath(),Qu(n,c+.5,d-Ur/2+.5,p-1,Ur-1,(Ur-1)/2),n.strokeStyle=r.accentLight,n.lineWidth=1,n.stroke()}return s!==void 0&&(n.textAlign="right",n.fillStyle=r.textDark,n.fillText(s,i.x+i.width-r.cellHorizontalPadding,d+dr(n,`12px ${r.fontFamily}`))),n.restore(),!0},provideEditor:()=>e=>{const{data:t,readonly:n}=e.value,r=t.value.toString(),i=t.min.toString(),o=t.max.toString(),a=t.step.toString(),l=s=>{e.onChange({...e.value,data:{...t,value:Number(s.target.value)}})};return g.createElement("label",{style:Ay},g.createElement("input",{style:Fy,type:"range",value:r,min:i,max:o,step:a,onChange:l,disabled:n}),r)},onPaste:(e,t)=>{let n=Number.parseFloat(e);return n=Number.isNaN(n)?t.value:Math.max(t.min,Math.min(t.max,n)),{...t,value:n}}},zy=mn("input")({name:"StyledInputBox",class:"gdg-s1wtovjx",propsAsIs:!1}),ys=(e,t)=>{if(t==null)return"";const n=t.toISOString();switch(e){case"date":return n.split("T")[0];case"datetime-local":return n.replace("Z","");case"time":return n.split("T")[1].replace("Z","");default:throw new Error(`Unknown date kind ${e}`)}},Vy=e=>{const t=e.value.data,{format:n,displayDate:r}=t,i=t.step!==void 0&&!Number.isNaN(Number(t.step))?Number(t.step):void 0,o=t.min instanceof Date?ys(n,t.min):t.min,a=t.max instanceof Date?ys(n,t.max):t.max;let l=t.date;const s=t.timezoneOffset?t.timezoneOffset*60*1e3:0;s&&l&&(l=new Date(l.getTime()+s));const u=ys(n,l);return e.value.readonly?ie.createElement(Zr,{highlight:!0,autoFocus:!1,disabled:!0,value:r??"",onChange:()=>{}}):ie.createElement(zy,{"data-testid":"date-picker-cell",required:!0,type:n,defaultValue:u,min:o,max:a,step:i,autoFocus:!0,onChange:c=>{isNaN(c.target.valueAsNumber)?e.onChange({...e.value,data:{...e.value.data,date:void 0}}):e.onChange({...e.value,data:{...e.value.data,date:new Date(c.target.valueAsNumber-s)}})}})},$y={kind:te.Custom,isMatch:e=>e.data.kind==="date-picker-cell",draw:(e,t)=>{const{displayDate:n}=t.data;return Us(e,n,t.contentAlign),!0},measure:(e,t,n)=>{const{displayDate:r}=t.data;return e.measureText(r).width+n.cellHorizontalPadding*2},provideEditor:()=>({editor:Vy}),onPaste:(e,t)=>{let n=NaN;return e&&(n=Number(e).valueOf(),Number.isNaN(n)&&(n=Date.parse(e),t.format==="time"&&Number.isNaN(n)&&(n=Date.parse(`1970-01-01T${e}Z`)))),{...t,date:Number.isNaN(n)?void 0:new Date(n)}}},Ny="None";function Cc(e,t,n){e.save(),e.beginPath(),e.moveTo(t.x+t.width-8,t.y+1),e.lineTo(t.x+t.width,t.y+1),e.lineTo(t.x+t.width,t.y+1+8),e.fillStyle=n.accentColor,e.fill(),e.restore()}const By=e=>{const{cell:t,theme:n,ctx:r}=e;Us({...e,theme:{...n,textDark:n.textLight,headerFontFull:`${n.headerFontStyle} ${n.fontFamily}`,baseFontFull:`${n.baseFontStyle} ${n.fontFamily}`,markerFontFull:`${n.markerFontStyle} ${n.fontFamily}`}},Ny,t.contentAlign),r.fillStyle=n.textDark};function Wy(e){const t=ie.useCallback((r,i)=>{const{cell:o,theme:a,ctx:l,rect:s}=r,u=r.col;if(bi(o))Cc(l,s,a);else if(ka(o)&&u<e.length){const c=e[u];["checkbox","line_chart","bar_chart","progress"].includes(c.kind)?i():By(r),c.isRequired&&c.isEditable&&Cc(l,s,a);return}i()},[e]),n=ie.useMemo(()=>[K1,Ly,Hy,$y,...k1],[]);return{drawCell:t,customRenderers:n}}function Uy(){const e=jr();return ie.useMemo(()=>{const n={editable:i=>`<svg xmlns="http://www.w3.org/2000/svg" height="40" viewBox="0 96 960 960" width="40" fill="${i.bgColor}"><path d="m800.641 679.743-64.384-64.384 29-29q7.156-6.948 17.642-6.948 10.485 0 17.742 6.948l29 29q6.948 7.464 6.948 17.95 0 10.486-6.948 17.434l-29 29Zm-310.64 246.256v-64.383l210.82-210.821 64.384 64.384-210.821 210.82h-64.383Zm-360-204.872v-50.254h289.743v50.254H130.001Zm0-162.564v-50.255h454.615v50.255H130.001Zm0-162.307v-50.255h454.615v50.255H130.001Z"/></svg>`};return{glideTheme:{accentColor:e.colors.primary,accentFg:e.colors.white,accentLight:Zo(e.colors.primary,.9),borderColor:e.colors.borderColorLight,horizontalBorderColor:e.colors.borderColorLight,fontFamily:e.genericFonts.bodyFont,bgSearchResult:Zo(e.colors.primary,.9),resizeIndicatorColor:e.colors.primary,bgIconHeader:e.colors.fadedText60,fgIconHeader:e.colors.white,bgHeader:e.colors.bgMix,bgHeaderHasFocus:e.colors.darkenedBgMix15,bgHeaderHovered:e.colors.darkenedBgMix15,textHeader:e.colors.fadedText60,textHeaderSelected:e.colors.white,textGroupHeader:e.colors.fadedText60,headerFontStyle:`${Wn(e.fontSizes.sm)}px`,baseFontStyle:`${Wn(e.fontSizes.sm)}px`,editorFontSize:e.fontSizes.sm,textDark:e.colors.bodyText,textMedium:Zo(e.colors.bodyText,.2),textLight:e.colors.fadedText40,textBubble:e.colors.fadedText60,bgCell:e.colors.bgColor,bgCellMedium:e.colors.bgColor,cellHorizontalPadding:Math.round(Wn(e.spacing.sm)),cellVerticalPadding:Math.round(Wn("0.1875rem")),bgBubble:e.colors.secondaryBg,bgBubbleSelected:e.colors.secondaryBg,linkColor:e.colors.link,drilldownBorder:e.colors.darkenedBgMix25},tableBorderRadius:e.radii.default,tableBorderWidth:1,defaultTableHeight:Math.round(Wn("25rem")),minColumnWidth:Math.round(Wn("3.125rem")),maxColumnWidth:Math.round(Wn("62.5rem")),maxColumnAutoWidth:Math.round(Wn("31.25rem")),defaultRowHeight:Math.round(Wn("2.1875rem")),defaultHeaderHeight:Math.round(Wn("2.1875rem")),bgRowHovered:tg(e.colors.bgColor,e.colors.secondaryBg,.3),headerIcons:n}},[e])}const qy=Fs.getLogger("useDataEditor");function Yy(e,t,n,r,i,o,a,l,s){const u=ie.useCallback(([p,v],w)=>{const b=e[p];if(!b.isEditable)return;const M=b.indexNumber,O=n.current.getOriginalRowIndex(i(v)),S=r([p,v]),R=b.getCellValue(S),_=b.getCellValue(w);if(!bi(S)&&_===R)return;const E=b.getCell(_,!0);bi(E)?qy.warn(`Not applying the cell edit since it causes this error:
 ${E.data}`):(n.current.setCell(M,O,{...E,lastUpdated:performance.now()}),l())},[e,n,i,r,l]),c=ie.useCallback(()=>{if(t)return;const p=new Map;e.forEach(v=>{p.set(v.indexNumber,v.getCell(v.defaultValue))}),n.current.addRow(p),a()},[e,n,t,a]),d=ie.useCallback(()=>{t||(c(),l())},[c,l,t]),h=ie.useCallback(p=>{var v;if(p.rows.length>0){if(t)return!0;const w=p.rows.toArray().map(b=>n.current.getOriginalRowIndex(i(b)));return n.current.deleteRows(w),a(),s(),l(),!1}if((v=p.current)!=null&&v.range){const w=[],b=p.current.range;for(let M=b.y;M<b.y+b.height;M++)for(let O=b.x;O<b.x+b.width;O++){const S=e[O];S.isEditable&&!S.isRequired&&(w.push({cell:[O,M]}),u([O,M],S.getCell(null)))}return w.length>0&&(l(),o(w)),!1}return!0},[e,n,t,o,i,l,u,s,a]),f=ie.useCallback((p,v)=>{const[w,b]=p,M=[];for(let O=0;O<v.length;O++){const S=v[O];if(O+b>=n.current.getNumRows()){if(t)break;c()}for(let R=0;R<S.length;R++){const _=S[R],E=O+b,x=R+w;if(x>=e.length)break;const L=e[x];if(L.isEditable){const D=L.getCell(_,!0);if(vt(D)&&!bi(D)){const C=L.indexNumber,I=n.current.getOriginalRowIndex(i(E)),T=L.getCellValue(r([x,E]));L.getCellValue(D)!==T&&(n.current.setCell(C,I,{...D,lastUpdated:performance.now()}),M.push({cell:[x,E]}))}}}M.length>0&&(l(),o(M))}return!1},[e,n,t,i,r,c,l,o]),m=ie.useCallback((p,v)=>{const w=p[0];if(w>=e.length)return!0;const b=e[w];if(b.validateInput){const M=b.validateInput(b.getCellValue(v));return M===!0||M===!1?M:b.getCell(M)}return!0},[e]);return{onCellEdited:u,onPaste:f,onRowAppended:d,onDelete:h,validateCell:m}}const Pf=",",Ji='"',Xy='"',_f=`
`,Gy="\uFEFF",jy=new RegExp(`[${[Pf,Ji,_f].join("")}]`),Sc=Fs.getLogger("useDataExporter");function xc(e){return e.map(t=>Ky(t)).join(Pf)+_f}function Ky(e){if(Be(e))return"";const t=wt(e);return jy.test(t)?`${Ji}${t.replace(new RegExp(Ji,"g"),Xy+Ji)}${Ji}`:t}async function kc(e,t,n,r){const i=new TextEncoder;await e.write(i.encode(Gy));const o=n.map(a=>a.name);await e.write(i.encode(xc(o)));for(let a=0;a<r;a++){const l=[];n.forEach((s,u,c)=>{l.push(s.getCellValue(t([u,a])))}),await e.write(i.encode(xc(l)))}await e.close()}function Zy(e,t,n,r){return{exportToCsv:ie.useCallback(async()=>{const a=`${new Date().toISOString().slice(0,16).replace(":","-")}_export.csv`;try{const u=await(await(await Ls(()=>import("./es6.CxQz807-.js").then(c=>c.a),__vite__mapDeps([15,1,2]),import.meta.url)).showSaveFilePicker({suggestedName:a,types:[{accept:{"text/csv":[".csv"]}}],excludeAcceptAllOption:!1})).createWritable();await kc(u,e,t,n)}catch(l){if(l instanceof Error&&l.name==="AbortError")return;try{Sc.warn("Failed to export data as CSV with FileSystem API, trying fallback method",l);let s="";const u=new WritableStream({write:async f=>{s+=new TextDecoder("utf-8").decode(f)},close:async()=>{}});await kc(u.getWriter(),e,t,n);const c=new Blob([s],{type:"text/csv;charset=utf-8;"}),d=URL.createObjectURL(c),h=vg({enforceDownloadInNewTab:r,url:d,filename:a});h.style.display="none",document.body.appendChild(h),h.click(),document.body.removeChild(h),URL.revokeObjectURL(d)}catch(s){Sc.error("Failed to export data as CSV",s)}}},[t,n,e,r])}}function Jy(e,t,n,r){return{getCellContent:ie.useCallback(([o,a])=>{var l;if(o>t.length-1)return Ht("Column index out of bounds","This error should never happen. Please report this bug.");if(a>n-1)return Ht("Row index out of bounds","This error should never happen. Please report this bug.");try{const s=t[o],u=s.indexNumber,c=r.current.getOriginalRowIndex(a),d=r.current.isAddedRow(c);if(s.isEditable||d){const m=r.current.getCell(u,c);if(vt(m))return{...s.getCell(s.getCellValue(m),!1),lastUpdated:m.lastUpdated};if(d)return Ht("Error during cell creation",`This error should never happen. Please report this bug. No cell found for an added row: col=${u}; row=${c}`)}const h=e.getCell(c,u),f=ng(e,c,u);return L1(s,h,f,(l=e.styler)==null?void 0:l.cssStyles)}catch(s){return Ht("Error during cell creation",`This error should never happen. Please report this bug. 
Error: ${s}`)}},[t,n,e,r])}}function Qy(e){const[t,n]=ie.useState(void 0),r=ie.useCallback(o=>{if(o.kind!=="cell")n(void 0);else{const[,a]=o.location;n(a)}},[n]);return{getRowThemeOverride:ie.useCallback(o=>{if(o===t)return{bgCell:e.bgRowHovered,bgCellMedium:e.bgRowHovered}},[e.bgRowHovered,t]),onItemHovered:r}}function eC(e,t,n,r,i){const[o,a]=ie.useState({columns:pt.empty(),rows:pt.empty(),current:void 0}),l=!t&&!n&&(e.selectionMode.includes(Mn.SelectionMode.MULTI_ROW)||e.selectionMode.includes(Mn.SelectionMode.SINGLE_ROW)),s=l&&e.selectionMode.includes(Mn.SelectionMode.MULTI_ROW),u=!t&&!n&&(e.selectionMode.includes(Mn.SelectionMode.SINGLE_COLUMN)||e.selectionMode.includes(Mn.SelectionMode.MULTI_COLUMN)),c=u&&e.selectionMode.includes(Mn.SelectionMode.MULTI_COLUMN),d=o.rows.length>0,h=o.columns.length>0,f=o.current!==void 0,m=ie.useCallback(v=>{const w=!Xa(v.rows.toArray(),o.rows.toArray()),b=!Xa(v.columns.toArray(),o.columns.toArray()),M=!Xa(v.current,o.current);let O=l&&w||u&&b,S=v;if((l||u)&&v.current!==void 0&&M&&(S={...v,rows:o.rows,columns:o.columns},O=!1),w&&v.rows.length>0&&b&&v.columns.length===0&&(S={...S,columns:o.columns},O=!0),b&&v.columns.length>0&&w&&v.rows.length===0&&(S={...S,rows:o.rows},O=!0),b&&S.columns.length>=0){let R=S.columns;r.forEach((_,E)=>{_.isIndex&&(R=R.remove(E))}),R.length<S.columns.length&&(S={...S,columns:R})}a(S),O&&i(S)},[o,l,u,i,r]),p=ie.useCallback((v=!1,w=!1)=>{const b={columns:w?o.columns:pt.empty(),rows:v?o.rows:pt.empty(),current:void 0};a(b),(!v&&l||!w&&u)&&i(b)},[o,l,u,i]);return{gridSelection:o,isRowSelectionActivated:l,isMultiRowSelectionActivated:s,isColumnSelectionActivated:u,isMultiColumnSelectionActivated:c,isRowSelected:d,isColumnSelected:h,isCellSelected:f,clearSelection:p,processSelectionChange:m}}function tC(e,t,n,r,i,o,a){const l=e.rowHeight??t.defaultRowHeight,s=t.defaultHeaderHeight+l+2*t.tableBorderWidth,u=r?2:1,c=e.editingMode===Mn.EditingMode.DYNAMIC?1:0,d=n+c;let h=Math.max(d*l+u*t.defaultHeaderHeight+2*t.tableBorderWidth,s),f=Math.min(h,t.defaultTableHeight);e.height&&(f=Math.max(e.height,s),h=Math.max(e.height,h)),o&&(f=Math.min(f,o),h=Math.min(h,o),e.height||(f=h));const m=t.minColumnWidth+2*t.tableBorderWidth,p=Math.max(i,m);let v,w=p;e.useContainerWidth?v=p:e.width&&(v=Math.min(Math.max(e.width,m),p),w=Math.min(Math.max(e.width,w),p));const[b,M]=ie.useState({width:v||"100%",height:f});return ie.useLayoutEffect(()=>{e.useContainerWidth&&b.width==="100%"&&M(O=>({...O,width:p}))},[p]),ie.useLayoutEffect(()=>{M(O=>({...O,width:v||"100%"}))},[v]),ie.useLayoutEffect(()=>{M(O=>({...O,height:f}))},[f,n]),ie.useLayoutEffect(()=>{if(a){const O=e.useContainerWidth||vt(e.width)&&e.width>0;M({width:O?w:"100%",height:h})}else M({width:v||"100%",height:f})},[a]),{minHeight:s,maxHeight:h,minWidth:m,maxWidth:w,rowHeight:l,resizableSize:b,setResizableSize:M}}const nC=600,rC="⚠️ Please fill out this cell.";function iC(e,t,n=[]){const[r,i]=ie.useState(),o=ie.useRef(null),a=ie.useCallback(s=>{if(clearTimeout(o.current),o.current=0,i(void 0),(s.kind==="header"||s.kind==="cell")&&s.location){const u=s.location[0],c=s.location[1];let d;if(u<0||u>=e.length||n.includes(c))return;const h=e[u];if(s.kind==="header"&&vt(h))d=h.help;else if(s.kind==="cell"){const f=t([u,c]);bi(f)?d=f.errorDetails:h.isRequired&&h.isEditable&&ka(f)?d=rC:u1(f)&&(d=f.tooltip)}d&&(o.current=setTimeout(()=>{d&&i({content:d,left:s.bounds.x+s.bounds.width/2,top:s.bounds.y})},nC))}},[e,t,i,o,n]),l=ie.useCallback(()=>{i(void 0)},[i]);return{tooltip:r,clearTooltip:l,onItemHovered:a}}function oC({top:e,left:t,content:n,clearTooltip:r}){const[i,o]=ie.useState(!0),a=jr(),{colors:l,fontSizes:s,radii:u,fontWeights:c}=a,d=ie.useCallback(()=>{o(!1),r()},[r,o]);return ut(Sa,{content:ut(rg,{"data-testid":"stDataFrameTooltipContent",children:ut(ig,{style:{fontSize:s.sm},source:n,allowHTML:!1})}),placement:ya.top,accessibilityType:_c.tooltip,showArrow:!1,popoverMargin:5,onClickOutside:d,onEsc:d,overrides:{Body:{style:{borderTopLeftRadius:u.default,borderTopRightRadius:u.default,borderBottomLeftRadius:u.default,borderBottomRightRadius:u.default,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important",backgroundColor:"transparent"}},Inner:{style:{backgroundColor:Ca(a)?l.bgColor:l.secondaryBg,color:l.bodyText,fontSize:s.sm,fontWeight:c.normal,paddingTop:"0 !important",paddingBottom:"0 !important",paddingLeft:"0 !important",paddingRight:"0 !important"}}},isOpen:i,children:ut("div",{"data-testid":"stDataFrameTooltipTarget",style:{position:"fixed",top:e,left:t}})})}const aC=g.memo(oC),Mc=Ci("div",{target:"e1mgrj8w0"})(({hasCustomizedScrollbars:e,theme:t})=>({position:"relative",display:"inline-block","& .stDataFrameGlideDataEditor":{height:"100%",minWidth:"100%",borderRadius:t.radii.default},"& .dvn-scroller":{...!e&&{scrollbarWidth:"thin"},overflowX:"auto !important",overflowY:"auto !important"},"& .gdg-seveqep":{maxWidth:"19rem",width:"80%",minWidth:"6rem",top:t.spacing.sm,right:t.spacing.sm,padding:t.spacing.sm,borderRadius:t.radii.default,"& .gdg-search-status":{paddingTop:t.spacing.twoXS,fontSize:t.fontSizes.twoSm},"& .gdg-search-progress":{display:"none"},"& input":{width:"100%"},"& button":{width:t.iconSizes.xl,height:t.iconSizes.xl,"& .button-icon":{width:t.iconSizes.base,height:t.iconSizes.base}}}})),Rc=150,sC=15e4,Ec=6;function lC({element:e,data:t,disabled:n,widgetMgr:r,disableFullscreenMode:i,fragmentId:o}){const{expanded:a,expand:l,collapse:s,width:u,height:c}=og(cg),d=ie.useRef(null),h=ie.useRef(null),f=ie.useRef(null),m=Uy(),{getRowThemeOverride:p,onItemHovered:v}=Qy(m),{libConfig:{enforceDownloadInNewTab:w=!1}}=ie.useContext(ag),[b,M]=ie.useState(!0),[O,S]=ie.useState(!1),[R,_]=ie.useState(!1),[E,x]=ie.useState(!1),[L,D]=ie.useState(),[C,I]=ie.useState(!1),T=ie.useMemo(()=>window.matchMedia&&window.matchMedia("(pointer: coarse)").matches,[]),k=ie.useMemo(()=>window.navigator.userAgent.includes("Mac OS")&&window.navigator.userAgent.includes("Safari")||window.navigator.userAgent.includes("Chrome"),[]);Be(e.editingMode)&&(e.editingMode=Mn.EditingMode.READ_ONLY);const{READ_ONLY:z,DYNAMIC:N}=Mn.EditingMode,X=t.dimensions,re=Math.max(0,X.numDataRows),G=re===0&&!(e.editingMode===N&&X.numDataColumns>0),j=re>sC,se=!j&&!G&&e.editingMode!==N,oe=!G&&e.editingMode===N&&!n,ue=ie.useRef(new Yo(re)),[he,Q]=ie.useState(ue.current.getNumRows());ie.useEffect(()=>{ue.current=new Yo(re),Q(ue.current.getNumRows())},[re]);const H=ie.useCallback(()=>{ue.current=new Yo(re),Q(ue.current.getNumRows())},[re]),[P,W]=ie.useState(e.columnOrder);ie.useEffect(()=>{W(e.columnOrder)},[e.columnOrder.join(",")]);const{columns:ce,allColumns:Oe,setColumnConfigMapping:ze}=H1(e,t,n,P);ie.useEffect(()=>{if(e.editingMode===z)return;const fe=r.getStringValue({id:e.id,formId:e.formId});fe&&(ue.current.fromJson(fe,ce),Q(ue.current.getNumRows()))},[]);const{getCellContent:Ce}=Jy(t,ce,he,ue),{columns:Ee,sortColumn:xe,getOriginalIndex:It,getCellContent:yt}=X1(re,ce,Ce),it=ie.useCallback(fe=>{const lt={selection:{rows:[],columns:[]}};lt.selection.rows=fe.rows.toArray().map(Zt=>It(Zt)),lt.selection.columns=fe.columns.toArray().map(Zt=>ro(Ee[Zt]));const Wt=JSON.stringify(lt),dn=r.getStringValue({id:e.id,formId:e.formId});(dn===void 0||dn!==Wt)&&r.setStringValue({id:e.id,formId:e.formId},Wt,{fromUi:!0},o)},[Ee,e.id,e.formId,r,o,It]),{debouncedCallback:le}=zu(it,Rc),{gridSelection:tt,isRowSelectionActivated:ve,isMultiRowSelectionActivated:ge,isColumnSelectionActivated:pe,isMultiColumnSelectionActivated:Ve,isRowSelected:_e,isColumnSelected:nt,isCellSelected:be,clearSelection:de,processSelectionChange:ot}=eC(e,G,n,Ee,le);ie.useEffect(()=>{de(!0,!0)},[a]);const He=ie.useCallback(fe=>{var lt;(lt=h.current)==null||lt.updateCells(fe)},[]);ie.useEffect(()=>{var lt,Wt,dn,Zt;if(!ve&&!pe)return;const fe=r.getStringValue({id:e.id,formId:e.formId});if(fe){const jt=Ee.map(Xn=>ro(Xn)),Sn=JSON.parse(fe);let vn=pt.empty(),Cr=pt.empty();(Wt=(lt=Sn.selection)==null?void 0:lt.rows)==null||Wt.forEach(Xn=>{vn=vn.add(Xn)}),(Zt=(dn=Sn.selection)==null?void 0:dn.columns)==null||Zt.forEach(Xn=>{Cr=Cr.add(jt.indexOf(Xn))}),(vn.length>0||Cr.length>0)&&ot({rows:vn,columns:Cr,current:void 0})}},[]);const et=ie.useCallback(()=>{he!==ue.current.getNumRows()&&Q(ue.current.getNumRows())},[he]),bt=ie.useCallback(()=>{const fe=ue.current.toJson(Ee);let lt=r.getStringValue({id:e.id,formId:e.formId});lt===void 0&&(lt=new Yo(0).toJson([])),fe!==lt&&r.setStringValue({id:e.id,formId:e.formId},fe,{fromUi:!0},o)},[Ee,e.id,e.formId,r,o]),{debouncedCallback:gt}=zu(bt,Rc),{exportToCsv:$e}=Zy(yt,Ee,he,w),{onCellEdited:Ot,onPaste:Yt,onRowAppended:kt,onDelete:Lt,validateCell:nn}=Yy(Ee,e.editingMode!==N,ue,yt,It,He,et,gt,de),zt=ie.useMemo(()=>G?[0]:oe?[he]:[],[G,oe,he]),{tooltip:ln,clearTooltip:Tt,onItemHovered:Ct}=iC(Ee,yt,zt),{drawCell:Dn,customRenderers:yn}=Wy(Ee),{provideEditor:Pe}=j1(),Ft=g.useCallback(fe=>({...fe,hasMenu:!G}),[G]),fn=ie.useMemo(()=>Ee.map(fe=>Ft(Ts(fe))),[Ee,Ft]),{columns:hn,onColumnResize:pn}=N1(fn),st=t.dimensions.numHeaderRows>1,{minHeight:Xt,maxHeight:Pt,minWidth:rn,maxWidth:Dt,rowHeight:At,resizableSize:We,setResizableSize:Bt}=tC(e,m,he,st,u||0,c,a),_t=ie.useCallback(([fe,lt])=>({...c1(!0,!1),displayData:"empty",contentAlign:"center",allowOverlay:!1,themeOverride:{textDark:m.glideTheme.textLight},span:[0,Math.max(Ee.length-1,0)]}),[Ee,m.glideTheme.textLight]),Vt=ie.useCallback(()=>{H(),de()},[H,de]);lg({element:e,widgetMgr:r,onFormCleared:Vt});const{pinColumn:$t,unpinColumn:Cn,freezeColumns:nr}=V1(Ee,G,u||0,m.minColumnWidth,de,ze),{changeColumnFormat:Hn}=z1(ze),{hideColumn:V,showColumn:Qe}=G1(de,ze),{onColumnMoved:Ue}=$1(Ee,nr,$t,Cn,W);return ie.useEffect(()=>{setTimeout(()=>{var fe,lt;if(f.current&&h.current){const Wt=(lt=(fe=f.current)==null?void 0:fe.querySelector(".dvn-stack"))==null?void 0:lt.getBoundingClientRect();Wt&&(_(Wt.height>f.current.clientHeight),x(Wt.width>f.current.clientWidth))}},1)},[We,he,hn]),g.useEffect(()=>{Oe.length==Ee.length&&I(!1)},[Oe.length,Ee.length]),Ln(Mc,{className:"stDataFrame","data-testid":"stDataFrame",hasCustomizedScrollbars:k,ref:f,onMouseDown:fe=>{if(f.current&&k){const lt=f.current.getBoundingClientRect();E&&lt.height-(Ec+1)<fe.clientY-lt.top&&fe.stopPropagation(),R&&lt.width-(Ec+1)<fe.clientX-lt.left&&fe.stopPropagation()}},onBlur:fe=>{!b&&!T&&!fe.currentTarget.contains(fe.relatedTarget)&&de(!0,!0)},children:[Ln(dg,{isFullScreen:a,disableFullscreenMode:i,locked:_e&&!ve||be||T&&b||C,onExpand:l,onCollapse:s,target:Mc,children:[(ve&&_e||pe&&nt)&&ut(di,{label:"Clear selection",icon:bg,onClick:()=>{de(),Tt()}}),oe&&_e&&ut(di,{label:"Delete row(s)",icon:xg,onClick:()=>{Lt&&(Lt(tt),Tt())}}),oe&&!_e&&ut(di,{label:"Add row",icon:Nc,onClick:()=>{var fe;kt&&(M(!0),kt(),Tt(),(fe=h.current)==null||fe.scrollTo(0,he,"vertical"))}}),!G&&Oe.length>Ee.length&&ut(n1,{columns:Oe,columnOrder:P,setColumnOrder:W,hideColumn:V,showColumn:Qe,isOpen:C,onClose:()=>I(!1),children:ut(di,{label:"Show/hide columns",icon:Wc,onClick:()=>I(!0)})}),!j&&!G&&ut(di,{label:"Download as CSV",icon:kg,onClick:()=>$e()}),!G&&ut(di,{label:"Search",icon:Bc,onClick:()=>{O?S(!1):(M(!0),S(!0)),Tt()}})]}),ut(sg,{"data-testid":"stDataFrameResizable",ref:d,defaultSize:We,style:{border:`${m.tableBorderWidth}px solid ${m.glideTheme.borderColor}`,borderRadius:`${m.tableBorderRadius}`},minHeight:Xt,maxHeight:Pt,minWidth:rn,maxWidth:Dt,size:We,enable:{top:!1,right:!1,bottom:!1,left:!1,topRight:!1,bottomRight:!0,bottomLeft:!1,topLeft:!1},grid:[1,At],snapGap:At/3,onResizeStop:(fe,lt,Wt,dn)=>{if(d.current){const Zt=2*m.tableBorderWidth;Bt({width:d.current.size.width,height:Pt-d.current.size.height===Zt?d.current.size.height+Zt:d.current.size.height})}},children:ut(Yv,{className:"stDataFrameGlideDataEditor","data-testid":"stDataFrameGlideDataEditor",ref:h,columns:hn,rows:G?1:he,minColumnWidth:m.minColumnWidth,maxColumnWidth:m.maxColumnWidth,maxColumnAutoWidth:m.maxColumnAutoWidth,rowHeight:At,headerHeight:m.defaultHeaderHeight,getCellContent:G?_t:yt,onColumnResize:T?void 0:pn,resizeIndicator:"header",freezeColumns:nr,smoothScrollX:!0,smoothScrollY:!0,verticalBorder:!0,getCellsForSelection:!0,rowMarkers:"none",rangeSelect:T?"cell":"rect",columnSelect:"none",rowSelect:"none",onColumnMoved:pe?void 0:Ue,onItemHovered:fe=>{v==null||v(fe),Ct==null||Ct(fe)},keybindings:{downFill:!0},onKeyDown:fe=>{(fe.ctrlKey||fe.metaKey)&&fe.key==="f"&&(S(lt=>!lt),fe.stopPropagation(),fe.preventDefault())},showSearch:O,onSearchClose:()=>{S(!1),Tt()},onHeaderClicked:(fe,lt)=>{!se||pe||(ve&&_e?de():de(!0,!0),xe(fe,"auto"))},gridSelection:tt,onGridSelectionChange:fe=>{(b||T)&&(ot(fe),ln!==void 0&&Tt())},theme:m.glideTheme,getRowThemeOverride:p,onMouseMove:fe=>{fe.kind==="out-of-bounds"&&b?M(!1):fe.kind!=="out-of-bounds"&&!b&&M(!0)},fixedShadowX:!0,fixedShadowY:!0,experimental:{scrollbarWidthOverride:0,...k&&{paddingBottom:E?-6:void 0,paddingRight:R?-6:void 0}},provideEditor:Pe,drawCell:Dn,customRenderers:yn,imageEditorOverride:x1,headerIcons:m.headerIcons,validateCell:nn,onHeaderMenuClick:(fe,lt)=>{D({columnIdx:fe,headerBounds:lt})},onPaste:!1,...ve&&{rowMarkers:{kind:"checkbox",checkboxStyle:"square",theme:{bgCell:m.glideTheme.bgHeader,bgCellMedium:m.glideTheme.bgHeader}},rowSelectionMode:ge?"multi":"auto",rowSelect:n?"none":ge?"multi":"single",rowSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...pe&&{columnSelect:n?"none":Ve?"multi":"single",columnSelectionBlending:"mixed",rangeSelectionBlending:"exclusive"},...!G&&e.editingMode!==z&&!n&&{fillHandle:!T,onCellEdited:Ot,onPaste:Yt,onDelete:Lt},...!G&&e.editingMode===N&&{trailingRowOptions:{sticky:!1,tint:!0},rowMarkers:{kind:"checkbox",checkboxStyle:"square",theme:{bgCell:m.glideTheme.bgHeader,bgCellMedium:m.glideTheme.bgHeader}},rowSelectionMode:"multi",rowSelect:n?"none":"multi",onRowAppended:n?void 0:kt,onHeaderClicked:void 0}})}),ln&&ln.content&&ut(aC,{top:ln.top,left:ln.left,content:ln.content,clearTooltip:Tt}),L&&Vc.createPortal(ut(Jv,{top:L.headerBounds.y+L.headerBounds.height,left:L.headerBounds.x+L.headerBounds.width,columnKind:ce[L.columnIdx].kind,onCloseMenu:()=>D(void 0),onSortColumn:se?fe=>{de(!0,!0),xe(L.columnIdx,fe,!0)}:void 0,isColumnPinned:ce[L.columnIdx].isPinned,onUnpinColumn:()=>{Cn(ce[L.columnIdx].id)},onPinColumn:()=>{$t(ce[L.columnIdx].id)},onHideColumn:()=>{V(ce[L.columnIdx].id)},onChangeFormat:fe=>{Hn(ce[L.columnIdx].id,fe),setTimeout(()=>{var lt;(lt=h.current)==null||lt.remeasureColumns(pt.fromSingleSelection(L.columnIdx))},100)},onAutosize:()=>{var fe;(fe=h.current)==null||fe.remeasureColumns(pt.fromSingleSelection(L.columnIdx))}}),document.querySelector("#portal"))]})}const uC=ug(lC),cC=g.memo(uC),kC=Object.freeze(Object.defineProperty({__proto__:null,default:cC},Symbol.toStringTag,{value:"Module"}));export{k0 as C,Sd as T,mi as a,Dg as b,kC as c,hi as i,Xm as m,mn as s};
