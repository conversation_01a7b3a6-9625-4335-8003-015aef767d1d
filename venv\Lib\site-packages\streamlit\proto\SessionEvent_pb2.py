# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/SessionEvent.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import Exception_pb2 as streamlit_dot_proto_dot_Exception__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/SessionEvent.proto\x1a\x1fstreamlit/proto/Exception.proto\"\x93\x01\n\x0cSessionEvent\x12 \n\x16script_changed_on_disk\x18\x01 \x01(\x08H\x00\x12%\n\x1bscript_was_manually_stopped\x18\x02 \x01(\x08H\x00\x12\x32\n\x1cscript_compilation_exception\x18\x03 \x01(\x0b\x32\n.ExceptionH\x00\x42\x06\n\x04typeB1\n\x1c\x63om.snowflake.apps.streamlitB\x11SessionEventProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.SessionEvent_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\021SessionEventProto'
  _globals['_SESSIONEVENT']._serialized_start=72
  _globals['_SESSIONEVENT']._serialized_end=219
# @@protoc_insertion_point(module_scope)
