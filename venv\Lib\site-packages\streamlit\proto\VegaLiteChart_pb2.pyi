"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.DataFrame_pb2
import streamlit.proto.NamedDataSet_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class VegaLiteChart(google.protobuf.message.Message):
    """DEPRECATED: This proto message is deprecated and unused. The ArrowVegaLiteChart
    proto message should be used instead.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    DATASETS_FIELD_NUMBER: builtins.int
    USE_CONTAINER_WIDTH_FIELD_NUMBER: builtins.int
    spec: builtins.str
    """The a JSON-formatted string with the Vega-Lite spec."""
    use_container_width: builtins.bool
    """If True, will overwrite the chart width spec to fit to container."""
    @property
    def data(self) -> streamlit.proto.DataFrame_pb2.DataFrame:
        """TODO Maybe remove
        The dataframe that will be used as the chart's main data source, if
        specified using Vega-Lite's inline API.
        """

    @property
    def datasets(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[streamlit.proto.NamedDataSet_pb2.NamedDataSet]:
        """Dataframes associated with this chart using Vega-Lite's datasets API, if
        any.
        """

    def __init__(
        self,
        *,
        spec: builtins.str = ...,
        data: streamlit.proto.DataFrame_pb2.DataFrame | None = ...,
        datasets: collections.abc.Iterable[streamlit.proto.NamedDataSet_pb2.NamedDataSet] | None = ...,
        use_container_width: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["data", b"data", "datasets", b"datasets", "spec", b"spec", "use_container_width", b"use_container_width"]) -> None: ...

global___VegaLiteChart = VegaLiteChart
