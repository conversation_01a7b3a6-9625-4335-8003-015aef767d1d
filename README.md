# Product Comparison Tool

This tool allows users to upload multiple supplier product catalogs (Excel, CSV, JSON) and compares products based on fuzzy name matching and semantic similarity.

## Features

- Supports multiple input file formats (.xlsx, .xls, .csv, .json).
- Flexible column mapping using common synonyms for product name, price, ID, etc.
- Product matching using a combination of RapidFuzz (WRatio) and SentenceTransformer (all-MiniLM-L6-v2) embeddings.
- Configurable matching thresholds via the UI.
- Basic price parsing from various string formats.
- Caching of file processing and embedding generation steps for improved performance on subsequent runs.
- Streamlit web interface for file uploads, configuration, and results display.
- Pairwise comparison between all uploaded valid files.

## Project Structure

```
product_comparison_tool/
├── cache/              # Caching directories (joblib, streamlit)
│   └── joblib/
├── data/               # Placeholder for sample data (not included)
├── src/                # Source code
│   ├── config/         # Configuration files
│   │   ├── __init__.py
│   │   └── settings.py # Main configuration (thresholds, synonyms, etc.)
│   ├── engine/         # Core comparison logic
│   │   ├── __init__.py
│   │   ├── data_loader.py # File loading and column mapping
│   │   └── matcher.py     # Product matching (fuzzy, semantic), price parsing
│   ├── ui/             # User interface code
│   │   ├── __init__.py
│   │   └── app.py      # Main Streamlit application
│   └── __init__.py
├── temp_uploads/       # Temporary storage for uploaded files
├── tests/              # Placeholder for unit/integration tests
│   ├── __init__.py
│   └── data/           # Test data generated by data_loader.py example
├── .gitignore          # Example gitignore (if using git)
├── README.md           # This file
├── requirements.txt    # Python dependencies
└── todo.md             # Development checklist (completed)
```

## Setup and Usage

1.  **Prerequisites:**
    *   Python 3.8+ installed.
    *   `pip` package installer.

2.  **Installation:**
    *   Unzip the provided `product_comparison_tool.zip` file.
    *   Navigate to the `product_comparison_tool` directory in your terminal.
    *   Create a virtual environment (recommended):
        ```bash
        python -m venv venv
        source venv/bin/activate  # On Windows use `venv\Scripts\activate`
        ```
    *   Install the required dependencies:
        ```bash
        pip install --upgrade pip
        pip install -r requirements.txt
        ```
        *Note: Installation, especially for `torch` and `sentence-transformers`, might take some time as models may need to be downloaded.* 

3.  **Running the Application:**
    *   Ensure you are in the `product_comparison_tool` directory with the virtual environment activated.
    *   Run the Streamlit application:
        ```bash
        streamlit run src/ui/app.py
        ```
    *   The application should open automatically in your web browser. If not, navigate to the local URL provided in the terminal (usually `http://localhost:8501`).

4.  **Using the Tool:**
    *   Use the sidebar to adjust the Fuzzy Match and Semantic Similarity thresholds if needed.
    *   Click the "Browse files" button under "1. Upload Supplier Catalogs" to select two or more catalog files (Excel, CSV, or JSON).
    *   The tool will process the files, attempt to map columns automatically, and display mapping details.
    *   Once processing is complete, the tool will perform pairwise comparisons between all valid uploaded files.
    *   The comparison results, including matched product names, prices (if found), and similarity scores, will be displayed in a table.

## Future Enhancements (Based on Initial Requirements)

-   **Advanced Column Mapping:** Implement manual override/confirmation for column mapping in the UI.
-   **Price Normalization:** Add currency detection/conversion and unit normalization.
-   **Visualizations:** Integrate interactive charts (price distribution, similarity vs. price) using libraries like Plotly or Altair.
-   **Feedback Loop:** Implement functionality for users to confirm/reject matches to fine-tune the model.
-   **Multi-Way Comparison:** Develop a strategy to consolidate results from multiple pairwise comparisons or perform a true multi-way match.
-   **Error Handling:** Further improve robustness for malformed files and edge cases.
-   **Testing:** Add comprehensive unit and integration tests.

