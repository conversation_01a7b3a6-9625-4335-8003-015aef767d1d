# src/engine/matcher.py

import pandas as pd
import numpy as np
from rapidfuzz import fuzz, process
from sentence_transformers import SentenceTransformer, util
import torch
import re
import logging
import os
import sys
from typing import List, Tuple, Dict, Optional, Any
from joblib import Memory

# Add project root to Python path if running script directly for testing
if __name__ == "__main__":
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

from src.config.settings import CACHE_DIR_JOBLIB, BATCH_SIZE_EMBEDDINGS

# Setup caching
os.makedirs(CACHE_DIR_JOBLIB, exist_ok=True)
memory = Memory(CACHE_DIR_JOBLIB, verbose=0)

logging.basicConfig(level=logging.INFO, format=\"%(asctime)s - %(levelname)s - %(message)s\")

# --- Text Preprocessing ---
def clean_text(text: Any) -> str:
    """Basic text cleaning: lowercase, remove extra whitespace, simple punctuation/tag removal."""
    if pd.isna(text):
        return \"\"
    if not isinstance(text, str):
        text = str(text)
    text = text.lower()
    text = re.sub(r\"<[^>]+>\", \" \", text) # Remove HTML tags
    text = re.sub(r\"[\W_]+\", \" \", text) # Replace non-alphanumeric with space
    # text = re.sub(r\"\d+\", \" \", text) # Optionally remove numbers
    text = re.sub(r\"\s+\", \" \", text).strip() # Remove extra whitespace
    return text

# Placeholder for more advanced preprocessing (stemming, lemmatization, synonym handling)
# def advanced_preprocess(text: str) -> str:
#     # Requires libraries like nltk or spacy
#     pass

# --- Matching Logic ---

# Initialize the sentence transformer model globally
# Using a smaller, efficient model. Consider making the model name configurable.
# Wrap in try-except block to handle potential download/load errors
MODEL_NAME = \"all-MiniLM-L6-v2\"
try:
    logging.info(f"Loading SentenceTransformer model: {MODEL_NAME}...")
    model = SentenceTransformer(MODEL_NAME)
    logging.info("SentenceTransformer model loaded successfully.")
except Exception as e:
    logging.error(f"Error loading SentenceTransformer model \"{MODEL_NAME}\": {e}. Semantic similarity will be unavailable.")
    model = None

@memory.cache
def get_embeddings(texts: Tuple[str, ...], batch_size: int = BATCH_SIZE_EMBEDDINGS) -> Optional[np.ndarray]:
    """Generates sentence embeddings for a tuple of texts with caching. Tuple is used because lists are not hashable for joblib cache keys."""
    if model is None:
        logging.warning("SentenceTransformer model not loaded. Cannot generate embeddings.")
        return None
    
    text_list = list(texts) # Convert back to list for processing
    if not text_list or not all(isinstance(t, str) for t in text_list):
        logging.warning("Invalid input for embedding generation. Need a list of strings.")
        return None

    try:
        logging.info(f"Generating embeddings for {len(text_list)} texts (batch size: {batch_size})...")
        # Ensure texts are strings
        embeddings = model.encode(text_list, batch_size=batch_size, convert_to_tensor=True, show_progress_bar=False) # Disable progress bar for cleaner logs
        logging.info("Embeddings generated successfully.")
        # Detach from GPU and convert to numpy for broader compatibility & caching
        return embeddings.cpu().numpy()
    except Exception as e:
        logging.error(f"Error generating embeddings: {e}")
        return None

def calculate_similarity(embeddings1: np.ndarray, embeddings2: np.ndarray) -> Optional[np.ndarray]:
    """Calculates cosine similarity between two sets of embeddings."""
    if embeddings1 is None or embeddings2 is None:
        logging.warning("Cannot calculate similarity, embeddings are missing.")
        return None
    if embeddings1.shape[0] == 0 or embeddings2.shape[0] == 0:
        logging.warning("Cannot calculate similarity, one or both embedding sets are empty.")
        return np.array([[]]) # Return empty array with correct dimensions
        
    try:
        # Compute cosine similarity using sentence-transformers util for efficiency
        cosine_scores = util.pytorch_cos_sim(torch.tensor(embeddings1), torch.tensor(embeddings2))
        return cosine_scores.cpu().numpy()
    except Exception as e:
        logging.error(f"Error calculating cosine similarity: {e}")
        return None

def find_best_matches(df1: pd.DataFrame, df2: pd.DataFrame,
                      name_col1: str = \"product_name\", name_col2: str = \"product_name\",
                      fuzzy_threshold: int = 80, semantic_threshold: float = 0.7,
                      batch_size: int = BATCH_SIZE_EMBEDDINGS) -> pd.DataFrame:
    """
    Finds the best matches between two DataFrames based on fuzzy and semantic similarity.

    Args:
        df1: First DataFrame (must contain name_col1).
        df2: Second DataFrame (must contain name_col2).
        name_col1: Name column in df1 (standardized name).
        name_col2: Name column in df2 (standardized name).
        fuzzy_threshold: Minimum fuzzy match score (0-100) using WRatio.
        semantic_threshold: Minimum semantic similarity score (0-1).
        batch_size: Batch size for embedding generation.

    Returns:
        DataFrame containing matched pairs and their scores, merged with original data.
        Returns an empty DataFrame if required columns are missing or no matches are found.
    """
    logging.info(f"Starting product matching between two sources ({len(df1)} vs {len(df2)} items). Settings: Fuzzy>={fuzzy_threshold}, Semantic>={semantic_threshold}")

    # --- 0. Input Validation --- 
    if name_col1 not in df1.columns or name_col2 not in df2.columns:
        logging.error(f"Required name columns \"{name_col1}\" or \"{name_col2}\" not found in input DataFrames. Aborting match.")
        return pd.DataFrame()
        
    # Drop rows where the name column is NaN, as they cannot be matched
    df1_proc = df1.dropna(subset=[name_col1]).copy()
    df2_proc = df2.dropna(subset=[name_col2]).copy()

    if df1_proc.empty or df2_proc.empty:
        logging.warning("One or both DataFrames are empty after dropping NaN names. No matches possible.")
        return pd.DataFrame()

    # --- 1. Preprocessing --- 
    df1_proc[\"clean_name\"] = df1_proc[name_col1].apply(clean_text)
    df2_proc[\"clean_name\"] = df2_proc[name_col2].apply(clean_text)

    # Store original indices to merge back later
    df1_proc[\"original_index_1\"] = df1_proc.index
    df2_proc[\"original_index_2\"] = df2_proc.index

    # --- 2. Semantic Similarity (if model available) --- 
    embeddings1 = None
    embeddings2 = None
    cosine_scores = None
    if model is not None:
        # Use tuple for caching
        embeddings1 = get_embeddings(tuple(df1_proc[\"clean_name\"]),
                                     batch_size=batch_size)
        embeddings2 = get_embeddings(tuple(df2_proc[\"clean_name\"]),
                                     batch_size=batch_size)
        
        if embeddings1 is not None and embeddings2 is not None:
            cosine_scores = calculate_similarity(embeddings1, embeddings2)
            if cosine_scores is None:
                 logging.warning("Failed to calculate semantic scores despite having embeddings.")
        else:
            logging.warning("Failed to generate embeddings for one or both dataframes. Semantic scoring skipped.")
    else:
        logging.warning("SentenceTransformer model not loaded. Semantic scoring skipped.")

    # --- 3. Fuzzy Matching & Combining Results --- 
    matches = []
    # Use RapidFuzz process.extract to find potential matches efficiently
    # Create dicts for rapidfuzz: {index: clean_name}
    choices1 = df1_proc[\"clean_name\"].to_dict()
    choices2 = df2_proc[\"clean_name\"].to_dict()
    
    logging.info("Performing fuzzy matching using rapidfuzz.process.extract...")
    # Find best match in df2 for each item in df1
    # Using WRatio scorer as it handles differences in word order/length well
    # Limit=1 returns only the best match above the cutoff
    extracted_matches = process.extract(
        df1_proc[\"clean_name\"].tolist(), 
        choices2, 
        scorer=fuzz.WRatio, 
        score_cutoff=fuzzy_threshold, 
        limit=1, 
        workers=-1 # Use all available cores
    )

    logging.info("Processing fuzzy matches and combining with semantic scores...")
    # extracted_matches is a list where each element corresponds to an item in df1_proc
    # Each element is a list of tuples: [(match_in_df2, score, index_in_df2)] or [] if no match
    for i, result_list in enumerate(extracted_matches):
        if result_list: # If a match was found for df1 item i
            matched_name2, fuzzy_score, original_idx2 = result_list[0] # Get the single best match
            original_idx1 = df1_proc.index[i] # Get original index from df1_proc
            
            semantic_score = None
            if cosine_scores is not None:
                # Need the positional indices used for embeddings
                try:
                    pos_idx1 = i # Positional index in df1_proc list
                    # Find positional index for original_idx2 in df2_proc
                    pos_idx2 = df2_proc.index.get_loc(original_idx2)
                    semantic_score = cosine_scores[pos_idx1, pos_idx2]
                except KeyError:
                    logging.warning(f"KeyError: Could not find index {original_idx2} in df2_proc index for semantic scoring.")
                except IndexError:
                     logging.warning(f"IndexError: Accessing cosine_scores[{pos_idx1}, {pos_idx2}] failed.")

            # Apply semantic threshold if score is available
            if semantic_score is None or semantic_score >= semantic_threshold:
                matches.append({
                    \"original_index_1\": original_idx1,
                    \"original_index_2\": original_idx2,
                    \"fuzzy_score\": fuzzy_score,
                    \"semantic_score\": semantic_score # Store None if not calculated or below threshold initially
                })

    match_df = pd.DataFrame(matches)

    if match_df.empty:
        logging.info("No matches found meeting the combined criteria.")
        return pd.DataFrame()

    # --- 4. Refine Matches (Optional: e.g., ensure 1-to-1 mapping) --- 
    # The current approach finds the best match in df2 for each df1 item.
    # To ensure a more 1-to-1 mapping, we can drop duplicates based on index_2 as well.
    # Sort by scores first to keep the *best* match if duplicates exist.
    sort_by = []
    ascending = []
    if \"semantic_score\" in match_df.columns and model is not None:
        sort_by.extend([\"semantic_score\", \"fuzzy_score\"])
        ascending.extend([False, False])
    else:
        sort_by.append(\"fuzzy_score\")
        ascending.append(False)
        
    match_df = match_df.sort_values(by=sort_by, ascending=ascending)
    
    # Keep the best match for each item in df1 (already handled by limit=1 in extract)
    # Keep the best match for each item in df2 (remove duplicates on index_2, keeping first/best score)
    best_matches = match_df.drop_duplicates(subset=[\"original_index_2\"], keep=\"first\")

    logging.info(f"Found {len(best_matches)} unique matches after filtering.")

    # --- 5. Merge with Original Data --- 
    # Merge based on the original indices stored earlier
    final_df = pd.merge(best_matches, df1, left_on=\"original_index_1\", right_index=True, how=\"left\")
    final_df = pd.merge(final_df, df2, left_on=\"original_index_2\", right_index=True, how=\"left\", suffixes=(\"_1\", \"_2\"))

    # Clean up intermediate columns and rename for clarity
    final_df = final_df.drop(columns=[\"original_index_1\", \"original_index_2\"])
    # Rename standardized columns back if needed, or keep standard names like product_name_1, product_name_2
    # Example: Rename name_col1_1 -> product_name_1, etc.
    rename_map = {}
    if f"{name_col1}_1" in final_df.columns: rename_map[f"{name_col1}_1"] = "product_name_1"
    if f"{name_col2}_2" in final_df.columns: rename_map[f"{name_col2}_2"] = "product_name_2"
    # Add renaming for other standardized columns (price, id, etc.)
    for col_base in [\"price\", \"product_id\", \"brand\", \"model\", \"unit\"]:
         if f"{col_base}_1" in final_df.columns: rename_map[f"{col_base}_1"] = f"{col_base}_1"
         if f"{col_base}_2" in final_df.columns: rename_map[f"{col_base}_2"] = f"{col_base}_2"
         # Special handling for parsed price if it exists
         if f"parsed_{col_base}_1" in final_df.columns: rename_map[f"parsed_{col_base}_1"] = f"parsed_{col_base}_1"
         if f"parsed_{col_base}_2" in final_df.columns: rename_map[f"parsed_{col_base}_2"] = f"parsed_{col_base}_2"
         
    final_df = final_df.rename(columns=rename_map)
    
    # Reorder columns for better readability
    core_cols = [\"product_name_1\", \"product_name_2\", \"fuzzy_score\", \"semantic_score\"]
    cols_1 = sorted([col for col in final_df.columns if col.endswith(\"_1\") and col not in core_cols])
    cols_2 = sorted([col for col in final_df.columns if col.endswith(\"_2\") and col not in core_cols])
    other_cols = sorted([col for col in final_df.columns if not col.endswith(\"_1\") and not col.endswith(\"_2\") and col not in core_cols])
    
    final_ordered_cols = core_cols + cols_1 + cols_2 + other_cols
    # Ensure all columns are included, even if logic missed some
    final_ordered_cols = [col for col in final_ordered_cols if col in final_df.columns]
    missing_cols = [col for col in final_df.columns if col not in final_ordered_cols]
    final_df = final_df[final_ordered_cols + missing_cols]

    return final_df

# --- Price Parsing/Normalization (Basic Placeholders) ---

def parse_price(price_input: Any) -> Optional[float]:
    """Attempts to parse a price string/number into a float, handling common formats."""
    if pd.isna(price_input):
        return None
    if isinstance(price_input, (int, float)):
        return float(price_input)
    if not isinstance(price_input, str):
        # Attempt conversion if it looks numeric-like after string conversion
        try:
            return float(str(price_input))
        except (ValueError, TypeError):
             return None
             
    price_str = price_input.strip()
    if not price_str:
        return None
        
    try:
        # Remove currency symbols (common ones), thousands separators (commas/spaces), and whitespace
        # Be careful not to remove decimal points/commas yet
        cleaned_str = re.sub(r"[$\€£¥]|\s+|(?<!\d)[,](?!\d)", "", price_str) # Remove symbols, spaces, commas not between digits
        cleaned_str = cleaned_str.replace(\" \", "") # Remove spaces used as thousands separators

        # Handle European style commas as decimal separators (e.g., 1.234,56 or 1234,56)
        if \",\" in cleaned_str and \".\" in cleaned_str:
            if cleaned_str.rfind(\",\") > cleaned_str.rfind(\".\"):
                # Format like 1.234,56 -> remove ., replace , with .
                cleaned_str = cleaned_str.replace(\".\", \"\").replace(\",\", \".\")
            else:
                # Format like 1,234.56 -> remove ,
                 cleaned_str = cleaned_str.replace(\",\", \"\")
        elif \",\" in cleaned_str:
             # Format like 1234,56 -> replace , with .
             cleaned_str = cleaned_str.replace(\",\", \".\")
        # else: standard format like 1234.56 or 1234

        # Final check for multiple decimal points after cleaning
        if cleaned_str.count(\".\") > 1:
             # Invalid format, e.g., resulted from bad cleaning
             logging.debug(f"Invalid price format after cleaning: {cleaned_str} (from: {price_input})")
             return None
             
        return float(cleaned_str)
    except (ValueError, TypeError) as e:
        logging.debug(f"Could not parse price \"{price_input}\": {e}")
        return None

# Placeholder for currency conversion
# def convert_currency(price: float, source_currency: str, target_currency: str) -> Optional[float]:
#     # Requires an external API or library (e.g., forex-python)
#     pass

# Placeholder for unit normalization
# def normalize_unit(price: float, unit_info: str, target_unit: str) -> Optional[float]:
#     # Complex logic to parse units (e.g., kg, lb, pack of 6) and convert
#     pass

if __name__ == "__main__":
    print("✅ Matcher module loaded successfully!")

