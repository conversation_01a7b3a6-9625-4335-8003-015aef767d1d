import{n as l,k as s,r,j as n}from"./index.C1z8KpLA.js";import{P as i,R as m}from"./RenderInPortalIfExists.BgaoZgep.js";const c=""+new URL("../media/balloon-0.Czj7AKwE.png",import.meta.url).href,p=""+new URL("../media/balloon-1.CNvFFrND.png",import.meta.url).href,f=""+new URL("../media/balloon-2.DTvC6B1t.png",import.meta.url).href,d=""+new URL("../media/balloon-3.CgSk4tbL.png",import.meta.url).href,B=""+new URL("../media/balloon-4.mbtFrzxf.png",import.meta.url).href,L=""+new URL("../media/balloon-5.CSwkUfRA.png",import.meta.url).href,a=300,h=121,t=20,g=80,u=1e3,x=s("from{transform:translateY(calc(100vh + ",a,"px));}to{transform:translateY(0);}"),_=l("img",{target:"eosrfo90"})(({theme:o})=>({position:"fixed",top:"-300px",marginLeft:`${-121/2}px`,zIndex:o.zIndices.balloons,left:`${Math.random()*(g-t)+t}vw`,animationDelay:`${Math.random()*u}ms`,height:`${a}px`,width:`${h}px`,pointerEvents:"none",animationDuration:"750ms",animationName:x,animationTimingFunction:"ease-in",animationDirection:"normal",animationIterationCount:1,opacity:1})),w=30,e=[c,p,f,d,B,L],A=e.length,I=({particleType:o})=>n(_,{src:e[o]}),M=({scriptRunId:o})=>n(m,{children:n(i,{className:"stBalloons","data-testid":"stBalloons",scriptRunId:o,numParticleTypes:A,numParticles:w,ParticleComponent:I})}),S=r.memo(M);export{w as NUM_BALLOONS,S as default};
