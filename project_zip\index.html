<!-- Product Comparison Tool - Category-Based Matching -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Comparison Tool - Category-Based Matching</title>
    <!-- Using specific versions for stability -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        /* --- CSS Variables for Theming and Maintainability --- */
        :root {
            --primary-color-start: #667eea;
            --primary-color-end: #764ba2;
            --primary-gradient: linear-gradient(135deg, var(--primary-color-start) 0%, var(--primary-color-end) 100%);
            --primary-gradient-hover: linear-gradient(45deg, var(--primary-color-start), var(--primary-color-end));
            --secondary-gradient: linear-gradient(145deg, #f8f9ff, #e8eaff);
            --settings-gradient: linear-gradient(145deg, #f0f2ff, #e0e4ff);
            --text-color: #333;
            --text-light: #666;
            --text-white: white;
            --border-color: #e0e4ff;
            --border-dashed: var(--primary-color-start);
            --border-dashed-hover: #4c63d2;
            --background-light: rgba(255, 255, 255, 0.95);
            --background-table-even: #f8f9ff;
            --background-tab-inactive: rgba(102, 126, 234, 0.1);
            --shadow-color-light: rgba(0, 0, 0, 0.1);
            --shadow-color-primary: rgba(102, 126, 234, 0.3);
            --shadow-color-primary-hover: rgba(102, 126, 234, 0.4);
            --success-bg: #e8f5e8;
            --success-text: #2e7d32;
            --warning-bg: #fff3e0;
            --warning-text: #ef6c00;
            --error-bg: #ffebee;
            --error-text: #c62828;
            --disabled-bg: #ccc;
            --border-radius-sm: 10px;
            --border-radius-md: 15px;
            --border-radius-lg: 20px;
            --border-radius-xl: 25px;
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            
            /* Category colors */
            --cat-camera: #4CAF50;
            --cat-network: #2196F3;
            --cat-storage: #FF9800;
            --cat-cable: #9C27B0;
            --cat-accessory: #795548;
            --cat-power: #F44336;
            --cat-other: #607D8B;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            background: var(--primary-gradient);
            min-height: 100vh;
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: var(--text-white);
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-panel {
            background: var(--background-light);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: 0 20px 40px var(--shadow-color-light);
            margin-bottom: 20px;
        }

        .upload-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .upload-box {
            border: 2px dashed var(--border-dashed);
            border-radius: var(--border-radius-md);
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            background: var(--secondary-gradient);
            position: relative;
            overflow: hidden;
        }

        .upload-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(102,126,234,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
            transition: all 0.3s ease;
            opacity: 0;
            pointer-events: none; /* Ensure it doesn't block interaction */
        }

        .upload-box:hover {
            border-color: var(--border-dashed-hover);
            transform: translateY(-5px);
            box-shadow: 0 15px 30px var(--shadow-color-primary);
        }

        .upload-box:hover::before {
            opacity: 1;
        }

        .upload-input {
            /* Visually hidden but accessible */
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border-width: 0;
        }

        .upload-label {
            display: inline-block;
            padding: 15px 30px;
            background: var(--primary-gradient-hover);
            color: var(--text-white);
            border-radius: var(--border-radius-xl);
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .upload-label:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px var(--shadow-color-primary-hover);
        }

        .file-info {
            margin-top: 15px;
            padding: 10px;
            background: var(--background-tab-inactive);
            border-radius: var(--border-radius-sm);
            display: none; /* Initially hidden */
            text-align: left;
            font-size: 0.9rem;
        }

        .settings-panel {
            background: var(--settings-gradient);
            border-radius: var(--border-radius-md);
            padding: 25px;
            margin-bottom: 30px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .setting-group {
            display: flex;
            flex-direction: column;
        }

        .setting-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--border-dashed-hover); /* Using a defined color */
        }

        .setting-input {
            padding: 10px 15px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            font-size: 1rem; /* Use rem for accessibility */
            transition: border-color 0.3s ease;
            background-color: white;
        }

        .setting-input:focus {
            border-color: var(--primary-color-start);
            outline: none;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        /* Style range inputs consistently */
        input[type="range"] {
            cursor: pointer;
            height: 8px;
            -webkit-appearance: none;
            appearance: none;
            background: var(--border-color);
            border-radius: 5px;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: var(--primary-color-start);
            border-radius: 50%;
            cursor: pointer;
        }
        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: var(--primary-color-start);
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        .range-value-display {
            display: inline-block;
            margin-left: 10px;
            font-weight: 500;
            color: var(--text-light);
        }

        .compare-btn {
            width: 100%;
            padding: 18px;
            background: var(--primary-gradient-hover);
            color: var(--text-white);
            border: none;
            border-radius: var(--border-radius-md);
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
        }

        .compare-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px var(--shadow-color-primary-hover);
        }

        .compare-btn:disabled {
            background: var(--disabled-bg);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.7;
        }

        .results-section {
            margin-top: 30px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            background: var(--background-tab-inactive);
            border-radius: var(--border-radius-md);
            padding: 5px;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-radius: var(--border-radius-sm);
            transition: all 0.3s ease;
            font-weight: 600;
            border: none; /* Use button element for semantics */
            background: none;
            color: var(--text-color);
        }

        .tab.active {
            background: var(--primary-gradient-hover);
            color: var(--text-white);
            box-shadow: 0 5px 15px var(--shadow-color-primary);
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(145deg, #ffffff, var(--background-table-even));
            padding: 25px;
            border-radius: var(--border-radius-md);
            text-align: center;
            box-shadow: 0 10px 20px var(--shadow-color-light);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color-start);
            margin-bottom: 10px;
        }

        .stat-label {
            color: var(--text-light);
            font-weight: 600;
        }

        .comparison-table-container {
            overflow-x: auto; /* Ensure table is scrollable on small screens */
            background: white;
            border-radius: var(--border-radius-md);
            box-shadow: 0 10px 20px var(--shadow-color-light);
            margin-bottom: 20px;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            white-space: nowrap; /* Prevent wrapping in table cells initially */
        }

        .comparison-table th {
            background: var(--primary-gradient-hover);
            color: var(--text-white);
            padding: 15px 20px;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0; /* Make header sticky */
            z-index: 1;
        }

        .comparison-table td {
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            white-space: normal; /* Allow wrapping for content */
        }

        .comparison-table tr:nth-child(even) {
            background: var(--background-table-even);
        }

        .comparison-table tr:hover {
            background: var(--background-tab-inactive);
        }

        .price-diff {
            padding: 5px 10px;
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            font-size: 0.9rem;
            display: inline-block; /* Ensure background covers content */
        }

        .price-diff.positive {
            background: var(--success-bg);
            color: var(--success-text);
        }

        .price-diff.negative {
            background: var(--error-bg);
            color: var(--error-text);
        }

        .similarity-score {
            padding: 3px 8px;
            border-radius: var(--border-radius-lg);
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
        }

        .similarity-high {
            background: var(--success-bg);
            color: var(--success-text);
        }

        .similarity-medium {
            background: var(--warning-bg);
            color: var(--warning-text);
        }

        .similarity-low {
            background: var(--error-bg);
            color: var(--error-text);
        }

        .chart-container {
            background: white;
            border-radius: var(--border-radius-md);
            padding: 30px;
            box-shadow: 0 10px 20px var(--shadow-color-light);
            margin-bottom: 20px;
            position: relative; /* Needed for chart responsiveness */
            height: 400px; /* Default height */
        }

        .loading-overlay {
            position: fixed; /* Cover the whole screen */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            color: white;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid var(--text-white);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        #progress-text {
            font-size: 1rem;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-section {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .export-btn {
            padding: 12px 24px;
            border: 2px solid var(--primary-color-start);
            background: transparent;
            color: var(--primary-color-start);
            border-radius: var(--border-radius-xl);
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            background: var(--primary-color-start);
            color: var(--text-white);
            transform: translateY(-2px);
        }

        .error-message {
            background-color: var(--error-bg);
            color: var(--error-text);
            padding: 15px;
            border-radius: var(--border-radius-sm);
            margin-bottom: 20px;
            border: 1px solid var(--error-text);
            display: none; /* Hidden by default */
        }

        /* Category badges */
        .category-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
            margin-right: 5px;
        }
        
        .category-camera { background-color: var(--cat-camera); }
        .category-network { background-color: var(--cat-network); }
        .category-storage { background-color: var(--cat-storage); }
        .category-cable { background-color: var(--cat-cable); }
        .category-accessory { background-color: var(--cat-accessory); }
        .category-power { background-color: var(--cat-power); }
        .category-other { background-color: var(--cat-other); }

        /* Category distribution section */
        .category-distribution {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .category-card {
            background: white;
            border-radius: var(--border-radius-md);
            padding: 20px;
            box-shadow: 0 10px 20px var(--shadow-color-light);
        }

        .category-card h3 {
            margin-bottom: 15px;
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .category-bar {
            height: 25px;
            margin: 8px 0;
            border-radius: 5px;
            position: relative;
            background: #f0f0f0;
            overflow: hidden;
        }

        .category-bar-fill {
            height: 100%;
            border-radius: 5px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
            white-space: nowrap;
        }

        .category-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
            margin-right: 5px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .header h1 {
                font-size: 2rem;
            }
            .main-panel {
                padding: 20px;
            }
            .upload-section, .settings-grid, .stats-grid {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                 grid-template-columns: repeat(2, 1fr);
            }
            .tabs {
                flex-direction: column;
            }
            .tab {
                margin-bottom: 5px;
            }
            .comparison-table th,
            .comparison-table td {
                padding: 10px 15px;
            }
            .chart-container {
                height: 300px; /* Adjust chart height for smaller screens */
            }
        }

        @media (max-width: 480px) {
             .stats-grid {
                 grid-template-columns: 1fr;
            }
            .header h1 {
                font-size: 1.8rem;
            }
            .header p {
                font-size: 1rem;
            }
            .upload-label,
            .compare-btn,
            .export-btn {
                padding: 12px 20px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span role="img" aria-label="magnifying glass">🔍</span> Product Comparison Tool</h1>
            <p>Compare products across suppliers by category with enhanced analysis</p>
        </div>

        <div class="main-panel">
            <div id="error-message-area" class="error-message"></div>

            <div class="upload-section">
                <div class="upload-box">
                    <input type="file" id="file-input" class="upload-input" accept=".xlsx,.xls,.csv" multiple>
                    <label for="file-input" class="upload-label"><span role="img" aria-label="folder">📁</span> Upload Supplier Files</label>
                    <p style="margin-top: 10px; color: var(--text-light);">Supports Excel (.xlsx, .xls) and CSV files. Select 2 or more.</p>
                    <div id="file-info" class="file-info"></div>
                </div>
            </div>

            <div class="settings-panel">
                <h3 style="margin-bottom: 20px; color: var(--border-dashed-hover);"><span role="img" aria-label="gear">⚙️</span> Comparison Settings</h3>
                <div class="settings-grid">
                    <div class="setting-group">
                        <label for="similarity-threshold" class="setting-label">Similarity Threshold</label>
                        <input type="range" id="similarity-threshold" class="setting-input" min="0.1" max="1" step="0.05" value="0.2">
                        <span id="threshold-value" class="range-value-display">0.20</span>
                    </div>
                    <div class="setting-group">
                        <label for="max-results" class="setting-label">Max Matches per Product</label>
                        <input type="number" id="max-results" class="setting-input" min="1" max="10" value="3">
                    </div>
                    <div class="setting-group">
                        <label for="category-filter" class="setting-label">Category Filter</label>
                        <select id="category-filter" class="setting-input">
                            <option value="all">All Categories</option>
                            <option value="camera">Cameras</option>
                            <option value="network">Network Equipment</option>
                            <option value="storage">Storage</option>
                            <option value="cable">Cables</option>
                            <option value="accessory">Accessories</option>
                            <option value="power">Power Equipment</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label for="sort-by" class="setting-label">Sort Results By</label>
                        <select id="sort-by" class="setting-input">
                            <option value="similarity">Similarity (Highest First)</option>
                            <option value="price-diff-asc">Price Difference (Lowest First)</option>
                            <option value="price-diff-desc">Price Difference (Highest First)</option>
                            <option value="price-asc">Price (Lowest First)</option>
                            <option value="price-desc">Price (Highest First)</option>
                        </select>
                    </div>
                </div>
            </div>

            <button id="compare-btn" class="compare-btn" disabled>
                <span role="img" aria-label="rocket">🚀</span> Start Comparison Analysis
            </button>

            <div id="results" class="results-section" style="display: none;">
                <div class="tabs" role="tablist">
                    <button class="tab active" data-tab="overview" role="tab" aria-selected="true" aria-controls="overview-content"><span role="img" aria-label="bar chart">📊</span> Overview</button>
                    <button class="tab" data-tab="categories" role="tab" aria-selected="false" aria-controls="categories-content"><span role="img" aria-label="folder">📁</span> Categories</button>
                    <button class="tab" data-tab="matches" role="tab" aria-selected="false" aria-controls="matches-content"><span role="img" aria-label="target">🎯</span> Potential Matches</button>
                    <button class="tab" data-tab="charts" role="tab" aria-selected="false" aria-controls="charts-content"><span role="img" aria-label="chart increasing">📈</span> Charts</button>
                    <button class="tab" data-tab="insights" role="tab" aria-selected="false" aria-controls="insights-content"><span role="img" aria-label="light bulb">💡</span> Insights</button>
                </div>

                <div id="overview-content" class="tab-content active" role="tabpanel" aria-labelledby="overview-tab">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value" id="total-products">0</div>
                            <div class="stat-label">Total Products</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="total-categories">0</div>
                            <div class="stat-label">Product Categories</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="potential-matches">0</div>
                            <div class="stat-label">Potential Matches</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" id="avg-price-diff">0 DZD</div>
                            <div class="stat-label">Avg. Price Difference</div>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="overview-chart"></canvas>
                    </div>
                </div>
                
                <div id="categories-content" class="tab-content" role="tabpanel" aria-labelledby="categories-tab">
                    <div class="category-distribution">
                        <div class="category-card">
                            <h3>File 1 Categories</h3>
                            <div id="file1-categories"></div>
                            <div class="category-legend" id="file1-legend"></div>
                        </div>
                        <div class="category-card">
                            <h3>File 2 Categories</h3>
                            <div id="file2-categories"></div>
                            <div class="category-legend" id="file2-legend"></div>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <canvas id="category-comparison-chart"></canvas>
                    </div>
                </div>

                <div id="matches-content" class="tab-content" role="tabpanel" aria-labelledby="matches-tab">
                    <div class="comparison-table-container">
                        <table class="comparison-table" id="matches-table">
                            <thead>
                                <tr>
                                    <th>Product (File 1)</th>
                                    <th>Price (File 1)</th>
                                    <th>Product (File 2)</th>
                                    <th>Price (File 2)</th>
                                    <th>Price Difference</th>
                                    <th>Similarity</th>
                                </tr>
                            </thead>
                            <tbody id="matches-tbody">
                                <!-- Match rows will be inserted here by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="charts-content" class="tab-content" role="tabpanel" aria-labelledby="charts-tab">
                    <div class="chart-container">
                        <canvas id="price-distribution-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <canvas id="similarity-distribution-chart"></canvas>
                    </div>
                </div>

                <div id="insights-content" class="tab-content" role="tabpanel" aria-labelledby="insights-tab">
                    <h4>Key Insights:</h4>
                    <ul id="insights-list"></ul>
                </div>

                <div class="export-section">
                    <button class="export-btn" id="export-csv-btn" disabled><span role="img" aria-label="page facing up">📄</span> Export Matches (CSV)</button>
                    <button class="export-btn" id="export-excel-btn" disabled><span role="img" aria-label="bar chart">📊</span> Export Matches (Excel)</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner"></div>
        <p id="progress-text">Processing...</p>
    </div>

    <script>
        // --- Global Scope & Configuration --- //
        const AppState = {
            uploadedFilesData: [],
            comparisonResults: {
                allProducts: [],
                potentialMatches: [],
                categoryMatches: {},
                file1Categories: {},
                file2Categories: {},
                commonCategories: []
            },
            chartInstances: {},
            settings: {
                similarityThreshold: 0.20,
                maxResultsPerProduct: 3,
                categoryFilter: 'all',
                sortBy: 'similarity'
            }
        };

        // Category colors mapping
        const categoryColors = {
            'camera': 'var(--cat-camera)',
            'network': 'var(--cat-network)',
            'storage': 'var(--cat-storage)',
            'cable': 'var(--cat-cable)',
            'accessory': 'var(--cat-accessory)',
            'power': 'var(--cat-power)',
            'other': 'var(--cat-other)'
        };

        // --- DOM Element References --- //
        const DOMElements = {
            fileInput: document.getElementById('file-input'),
            fileInfo: document.getElementById('file-info'),
            compareBtn: document.getElementById('compare-btn'),
            resultsSection: document.getElementById('results'),
            tabs: document.querySelectorAll('.tab'),
            tabContents: document.querySelectorAll('.tab-content'),
            matchesTbody: document.getElementById('matches-tbody'),
            totalProductsStat: document.getElementById('total-products'),
            totalCategoriesStat: document.getElementById('total-categories'),
            potentialMatchesStat: document.getElementById('potential-matches'),
            avgPriceDiffStat: document.getElementById('avg-price-diff'),
            file1CategoriesContainer: document.getElementById('file1-categories'),
            file2CategoriesContainer: document.getElementById('file2-categories'),
            file1Legend: document.getElementById('file1-legend'),
            file2Legend: document.getElementById('file2-legend'),
            overviewChartCanvas: document.getElementById('overview-chart'),
            categoryComparisonChartCanvas: document.getElementById('category-comparison-chart'),
            priceChartCanvas: document.getElementById('price-distribution-chart'),
            similarityChartCanvas: document.getElementById('similarity-distribution-chart'),
            insightsList: document.getElementById('insights-list'),
            exportCsvBtn: document.getElementById('export-csv-btn'),
            exportExcelBtn: document.getElementById('export-excel-btn'),
            loadingOverlay: document.getElementById('loading-overlay'),
            progressText: document.getElementById('progress-text'),
            errorMessageArea: document.getElementById('error-message-area'),
            similarityThresholdInput: document.getElementById('similarity-threshold'),
            similarityThresholdValue: document.getElementById('threshold-value'),
            maxResultsInput: document.getElementById('max-results'),
            categoryFilterInput: document.getElementById('category-filter'),
            sortByInput: document.getElementById('sort-by')
        };

        // --- Initialization --- //
        document.addEventListener('DOMContentLoaded', initializeApp);

        function initializeApp() {
            console.log('Initializing Category-Based Comparison Tool...');
            setupEventListeners();
            updateSettingsFromUI(); // Initial sync
            DOMElements.similarityThresholdValue.textContent = parseFloat(DOMElements.similarityThresholdInput.value).toFixed(2);
            console.log('Initialization complete.');
        }

        function setupEventListeners() {
            DOMElements.fileInput.addEventListener('change', handleFileUpload);
            DOMElements.compareBtn.addEventListener('click', startComparisonWorkflow);

            DOMElements.tabs.forEach(tab => {
                tab.addEventListener('click', handleTabSwitch);
            });

            // Settings listeners
            DOMElements.similarityThresholdInput.addEventListener('input', () => {
                DOMElements.similarityThresholdValue.textContent = parseFloat(DOMElements.similarityThresholdInput.value).toFixed(2);
                updateSettingsFromUI();
            });
            DOMElements.maxResultsInput.addEventListener('input', updateSettingsFromUI);
            DOMElements.categoryFilterInput.addEventListener('change', () => {
                updateSettingsFromUI();
                if (AppState.comparisonResults.potentialMatches.length > 0) {
                    filterAndDisplayMatches();
                }
            });
            DOMElements.sortByInput.addEventListener('change', () => {
                updateSettingsFromUI();
                if (AppState.comparisonResults.potentialMatches.length > 0) {
                    filterAndDisplayMatches();
                }
            });

            // Export listeners
            DOMElements.exportCsvBtn.addEventListener('click', () => exportMatches('csv'));
            DOMElements.exportExcelBtn.addEventListener('click', () => exportMatches('excel'));

            console.log('Event listeners set up.');
        }

        // --- UI Update Functions --- //

        function updateSettingsFromUI() {
            AppState.settings.similarityThreshold = parseFloat(DOMElements.similarityThresholdInput.value);
            AppState.settings.maxResultsPerProduct = parseInt(DOMElements.maxResultsInput.value);
            AppState.settings.categoryFilter = DOMElements.categoryFilterInput.value;
            AppState.settings.sortBy = DOMElements.sortByInput.value;
            console.log('Settings updated:', AppState.settings);
        }

        function showLoading(show, message = 'Processing...') {
            DOMElements.progressText.textContent = message;
            if (show) {
                DOMElements.loadingOverlay.classList.add('show');
                DOMElements.compareBtn.disabled = true;
            } else {
                DOMElements.loadingOverlay.classList.remove('show');
                DOMElements.compareBtn.disabled = DOMElements.fileInput.files.length < 2;
            }
        }

        function displayError(message) {
            DOMElements.errorMessageArea.textContent = message;
            DOMElements.errorMessageArea.style.display = 'block';
            console.error('Error Displayed:', message);
        }

        function clearError() {
            DOMElements.errorMessageArea.textContent = '';
            DOMElements.errorMessageArea.style.display = 'none';
        }

        function updateFileInfo(files) {
            if (files.length > 0) {
                DOMElements.fileInfo.style.display = 'block';
                DOMElements.fileInfo.innerHTML = `
                    <strong>${files.length} File(s) selected:</strong><br>
                    ${files.map(file => `• ${escapeHtml(file.name)} (${formatFileSize(file.size)})`).join('<br>')}
                `;
                DOMElements.compareBtn.disabled = files.length < 2;
            } else {
                DOMElements.fileInfo.style.display = 'none';
                DOMElements.compareBtn.disabled = true;
            }
        }

        function handleTabSwitch(event) {
            const clickedTab = event.currentTarget;
            const targetTabContentId = clickedTab.getAttribute('aria-controls');

            DOMElements.tabs.forEach(tab => {
                tab.classList.remove('active');
                tab.setAttribute('aria-selected', 'false');
            });
            clickedTab.classList.add('active');
            clickedTab.setAttribute('aria-selected', 'true');

            DOMElements.tabContents.forEach(content => {
                if (content.id === targetTabContentId) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });

            if (targetTabContentId === 'charts-content') {
                resizeCharts();
            }
        }

        // --- File Handling & Parsing --- //

        function handleFileUpload(event) {
            const files = Array.from(event.target.files);
            console.log(`Files selected: ${files.length}`);
            updateFileInfo(files);
            resetResults();
        }

        async function processUploadedFiles() {
            const files = Array.from(DOMElements.fileInput.files);
            if (files.length < 2) {
                throw new Error('Please select at least 2 files for comparison.');
            }

            showLoading(true, `Processing ${files.length} files...`);
            AppState.uploadedFilesData = [];

            const processingPromises = files.map((file, index) => {
                updateProgress(`Reading file ${index + 1}/${files.length}: ${escapeHtml(file.name)}`);
                return readFileContent(file)
                    .then(content => parseFileData(content, file.name))
                    .then(data => preprocessData(data, file.name))
                    .then(processed => {
                        AppState.uploadedFilesData.push({ filename: file.name, data: processed });
                        console.log(`Processed ${file.name}: ${processed.length} valid products found.`);
                    });
            });

            await Promise.all(processingPromises);

            if (AppState.uploadedFilesData.length !== files.length) {
                throw new Error('Some files could not be processed correctly.');
            }
            if (AppState.uploadedFilesData.some(f => f.data.length === 0)) {
                 console.warn("Warning: One or more files resulted in zero valid products after preprocessing.");
            }

            console.log('All files processed.');
            updateProgress('File processing complete. Starting comparison...');
        }

        function readFileContent(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve({ content: e.target.result, type: file.type, name: file.name });
                reader.onerror = (e) => reject(new Error(`Error reading file ${file.name}: ${e.target.error}`));

                if (file.name.toLowerCase().endsWith('.csv') || file.type === 'text/csv') {
                    reader.readAsText(file);
                } else if (file.name.toLowerCase().endsWith('.xls') || file.name.toLowerCase().endsWith('.xlsx')) {
                    reader.readAsArrayBuffer(file);
                } else {
                    reject(new Error(`Unsupported file type: ${file.name}. Please use .csv, .xls, or .xlsx.`));
                }
            });
        }

        function parseFileData(fileInfo, filename) {
            try {
                if (filename.toLowerCase().endsWith('.csv')) {
                    return parseCSV(fileInfo.content);
                } else {
                    const workbook = XLSX.read(fileInfo.content, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    if (!sheetName) throw new Error(`No sheets found in ${filename}`);
                    const worksheet = workbook.Sheets[sheetName];
                    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' });
                    if (rawData.length < 2) return [];

                    const headers = rawData[0].map(String);
                    const jsonData = rawData.slice(1).map(row => {
                        const rowObject = {};
                        headers.forEach((header, index) => {
                            const trimmedHeader = header.trim();
                            rowObject[trimmedHeader] = row[index];
                        });
                        return rowObject;
                    });
                    return jsonData;
                }
            } catch (error) {
                console.error(`Error parsing ${filename}:`, error);
                throw new Error(`Failed to parse file ${filename}. Ensure it's a valid ${filename.toLowerCase().endsWith('.csv') ? 'CSV' : 'Excel'} file. Error: ${error.message}`);
            }
        }

        function parseCSV(csvText) {
            const lines = csvText.split(/\r\n|\n/);
            if (lines.length === 0) return [];
            const headers = lines[0].split(',').map(h => h.trim().replace(/^"|"$/g, ''));
            const data = [];
            for (let i = 1; i < lines.length; i++) {
                if (!lines[i].trim()) continue;
                const values = lines[i].split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/).map(v => v.trim().replace(/^"|"$/g, ''));
                if (values.length === headers.length) {
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = values[index] || '';
                    });
                    data.push(row);
                }
            }
            return data;
        }

        // --- Data Preprocessing & Cleaning --- //

        function preprocessData(data, filename) {
            console.log(`Preprocessing ${filename}. Initial rows: ${data.length}`);
            const processed = data.map((row, index) => {
                try {
                    const id = detectColumnValue(row, ['id', 'sku', 'code', 'ref', 'identifier', 'partnumber']);
                    const description = detectColumnValue(row, ['description', 'product', 'name', 'title', 'item', 'desc']);
                    const price = detectColumnValue(row, ['price', 'cost', 'amount', 'value', 'unit_price']);

                    if (!description) return null;
                    const parsedPrice = parsePrice(price);
                    if (isNaN(parsedPrice) || parsedPrice <= 0) return null;

                    // Categorize the product
                    const category = categorizeProduct(description);

                    return {
                        originalRow: row,
                        id: String(id || `row_${index + 1}`),
                        description: String(description),
                        price: parsedPrice,
                        sourceFile: filename,
                        normalizedDescription: normalizeText(description),
                        category: category,
                        keywords: extractKeywords(description)
                    };
                } catch (error) {
                    console.warn(`Error processing row ${index + 1} in ${filename}:`, error);
                    return null;
                }
            });
            const finalData = processed.filter(item => item !== null);
            console.log(`Preprocessing ${filename} complete. Valid rows: ${finalData.length}`);
            return finalData;
        }

        function detectColumnValue(row, possibleKeys) {
            for (const key in row) {
                const lowerKey = key.trim().toLowerCase();
                for (const possible of possibleKeys) {
                    if (lowerKey.includes(possible)) {
                        return row[key];
                    }
                }
            }
            // Fallbacks
            if (possibleKeys.includes('description')) {
                let longestString = '';
                for (const key in row) {
                    if (typeof row[key] === 'string' && row[key].length > longestString.length) {
                        longestString = row[key];
                    }
                }
                if (longestString) return longestString;
            }
            if (possibleKeys.includes('price')) {
                 for (const key in row) {
                    const potentialPrice = parsePrice(row[key]);
                    if (!isNaN(potentialPrice) && potentialPrice > 0) {
                        return row[key];
                    }
                 }
            }
            if (possibleKeys.includes('id')) {
                const firstKey = Object.keys(row)[0];
                if (firstKey) return row[firstKey];
            }
            return '';
        }

        function parsePrice(priceValue) {
            if (typeof priceValue === 'number') return priceValue;
            if (typeof priceValue !== 'string' || !priceValue.trim()) return NaN;
            let cleaned = priceValue.replace(/[$,€£¥]/g, '');
            const hasComma = cleaned.includes(',');
            const hasDot = cleaned.includes('.');
            if (hasComma && hasDot) {
                if (cleaned.lastIndexOf('.') > cleaned.lastIndexOf(',')) {
                    cleaned = cleaned.replace(/,/g, '');
                } else {
                    cleaned = cleaned.replace(/\./g, '').replace(',', '.');
                }
            } else if (hasComma) {
                 cleaned = cleaned.replace(',', '.');
            }
            cleaned = cleaned.replace(/[^\d.-]/g, '');
            const parsed = parseFloat(cleaned);
            return isNaN(parsed) ? NaN : parsed;
        }

        function normalizeText(text) {
            if (!text || typeof text !== 'string') return '';
            return text
                .toLowerCase()
                .normalize('NFD').replace(/[\u0300-\u036f]/g, '')
                .replace(/[^\w\s-]/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();
        }

        function categorizeProduct(description) {
            if (!description || typeof description !== 'string') return 'other';
            
            const text = description.toLowerCase();
            
            // Define category patterns
            const categories = {
                "camera": ["camera", "dome", "bullet", "ipc", "surveillance", "hfw", "hdbw"],
                "storage": ["disque", "dur", "nvr", "stockage", "storage", "hdd"],
                "network": ["switch", "réseau", "network", "ethernet", "rj45", "brassage", "panneau", "port"],
                "cable": ["câble", "cable", "fibre", "optique", "rj45", "utp", "ftp", "cordon"],
                "accessory": ["accessoire", "support", "bracket", "mount", "boitier", "boîtier"],
                "software": ["logiciel", "software", "licence", "license"],
                "power": ["alimentation", "power", "supply", "batterie", "battery"]
            };
            
            for (const [category, keywords] of Object.entries(categories)) {
                if (keywords.some(keyword => text.includes(keyword))) {
                    return category;
                }
            }
            
            return "other";
        }

        function extractKeywords(description) {
            if (!description || typeof description !== 'string') return [];
            
            const normalized = normalizeText(description);
            
            // Remove common stop words
            const stopWords = ['the', 'and', 'for', 'with', 'des', 'les', 'pour', 'de', 'la', 'le', 'du', 'un', 'une'];
            const words = normalized.split(/\s+/);
            
            // Filter out stop words and short words
            return words.filter(word => 
                word.length > 2 && 
                !stopWords.includes(word) && 
                !/^\d+$/.test(word) // Exclude pure numbers
            );
        }

        // --- Comparison Logic --- //

        async function performComparison() {
            const { similarityThreshold } = AppState.settings;
            const filesToCompare = AppState.uploadedFilesData;
            
            // Reset comparison results
            AppState.comparisonResults = {
                allProducts: [],
                potentialMatches: [],
                categoryMatches: {},
                file1Categories: {},
                file2Categories: {},
                commonCategories: []
            };

            if (filesToCompare.length < 2) {
                throw new Error('Internal error: Not enough processed files data for comparison.');
            }
            if (filesToCompare.some(f => f.data.length === 0)) {
                throw new Error('Cannot perform comparison: One or more files have no valid product data after processing. Please check file contents and column headers (Description, Price).');
            }

            console.log(`Starting category-based comparison between ${filesToCompare.length} files with threshold ${similarityThreshold}`);
            updateProgress('Analyzing product categories...');

            // For simplicity, we'll compare the first two files
            const file1 = filesToCompare[0];
            const file2 = filesToCompare[1];
            
            // Collect all products
            AppState.comparisonResults.allProducts = [
                ...file1.data.map(item => ({ ...item, fileIndex: 0 })),
                ...file2.data.map(item => ({ ...item, fileIndex: 1 }))
            ];
            
            // Count categories in each file
            const file1Categories = {};
            const file2Categories = {};
            
            file1.data.forEach(item => {
                file1Categories[item.category] = (file1Categories[item.category] || 0) + 1;
            });
            
            file2.data.forEach(item => {
                file2Categories[item.category] = (file2Categories[item.category] || 0) + 1;
            });
            
            AppState.comparisonResults.file1Categories = file1Categories;
            AppState.comparisonResults.file2Categories = file2Categories;
            
            // Find common categories
            const commonCategories = Object.keys(file1Categories).filter(
                category => Object.keys(file2Categories).includes(category)
            );
            
            AppState.comparisonResults.commonCategories = commonCategories;
            
            console.log('File 1 categories:', file1Categories);
            console.log('File 2 categories:', file2Categories);
            console.log('Common categories:', commonCategories);
            
            updateProgress('Finding potential matches...');
            
            // Find potential matches
            const potentialMatches = [];
            const categoryMatches = {};
            
            // Initialize category matches
            commonCategories.forEach(category => {
                categoryMatches[category] = [];
            });
            
            // Compare products
            for (let i = 0; i < file1.data.length; i++) {
                const item1 = file1.data[i];
                
                for (let j = 0; j < file2.data.length; j++) {
                    const item2 = file2.data[j];
                    
                    // Calculate similarity score
                    const similarityScore = calculateSimilarity(item1, item2);
                    
                    // If similarity is above threshold, add to potential matches
                    if (similarityScore >= similarityThreshold) {
                        const match = {
                            item1: item1,
                            item2: item2,
                            similarity: similarityScore,
                            priceDifference: item2.price - item1.price,
                            sameCategory: item1.category === item2.category
                        };
                        
                        potentialMatches.push(match);
                        
                        // If same category, add to category matches
                        if (match.sameCategory) {
                            categoryMatches[item1.category].push(match);
                        }
                    }
                }
            }
            
            // Sort potential matches by similarity
            potentialMatches.sort((a, b) => b.similarity - a.similarity);
            
            // Sort category matches by similarity
            Object.keys(categoryMatches).forEach(category => {
                categoryMatches[category].sort((a, b) => b.similarity - a.similarity);
            });
            
            AppState.comparisonResults.potentialMatches = potentialMatches;
            AppState.comparisonResults.categoryMatches = categoryMatches;
            
            console.log(`Found ${potentialMatches.length} potential matches above threshold ${similarityThreshold}`);
            console.log('Category matches:', categoryMatches);
            
            updateProgress('Comparison complete.');
        }

        function calculateSimilarity(item1, item2) {
            // If same category, boost similarity
            const categoryBoost = item1.category === item2.category ? 0.2 : 0;
            
            // Calculate keyword overlap
            const keywords1 = new Set(item1.keywords);
            const keywords2 = new Set(item2.keywords);
            
            let keywordSimilarity = 0;
            
            if (keywords1.size > 0 && keywords2.size > 0) {
                const intersection = new Set([...keywords1].filter(x => keywords2.has(x)));
                const union = new Set([...keywords1, ...keywords2]);
                
                keywordSimilarity = intersection.size / union.size;
            }
            
            // Calculate text similarity using Jaccard similarity of character trigrams
            const trigrams1 = getNGrams(item1.normalizedDescription, 3);
            const trigrams2 = getNGrams(item2.normalizedDescription, 3);
            
            let trigramSimilarity = 0;
            
            if (trigrams1.size > 0 && trigrams2.size > 0) {
                const intersection = new Set([...trigrams1].filter(x => trigrams2.has(x)));
                const union = new Set([...trigrams1, ...trigrams2]);
                
                trigramSimilarity = intersection.size / union.size;
            }
            
            // Combine similarities with weights
            const combinedSimilarity = (keywordSimilarity * 0.6) + (trigramSimilarity * 0.4) + categoryBoost;
            
            // Cap at 1.0
            return Math.min(combinedSimilarity, 1.0);
        }

        function getNGrams(text, n) {
            const ngrams = new Set();
            if (!text || text.length < n) {
                return ngrams;
            }
            for (let i = 0; i <= text.length - n; i++) {
                ngrams.add(text.substring(i, i + n));
            }
            return ngrams;
        }

        // --- Results Display --- //

        function displayResults() {
            const results = AppState.comparisonResults;
            console.log('Displaying results...');
            updateProgress('Rendering results...');

            updateOverviewStats(results);
            displayCategoryDistribution(results);
            filterAndDisplayMatches();
            createOrUpdateCharts(results);
            generateInsights(results);

            DOMElements.resultsSection.style.display = 'block';
            DOMElements.exportCsvBtn.disabled = results.potentialMatches.length === 0;
            DOMElements.exportExcelBtn.disabled = results.potentialMatches.length === 0;

            const overviewTabButton = document.querySelector('.tab[data-tab="overview"]');
            if (overviewTabButton) {
                handleTabSwitch({ currentTarget: overviewTabButton });
            }

            console.log('Results displayed.');
            updateProgress('Analysis complete.');
            showLoading(false);
        }

        function resetResults() {
            console.log('Resetting results area...');
            AppState.comparisonResults = {
                allProducts: [],
                potentialMatches: [],
                categoryMatches: {},
                file1Categories: {},
                file2Categories: {},
                commonCategories: []
            };
            AppState.uploadedFilesData = [];
            DOMElements.resultsSection.style.display = 'none';
            DOMElements.matchesTbody.innerHTML = '';
            DOMElements.insightsList.innerHTML = '';
            DOMElements.file1CategoriesContainer.innerHTML = '';
            DOMElements.file2CategoriesContainer.innerHTML = '';
            DOMElements.file1Legend.innerHTML = '';
            DOMElements.file2Legend.innerHTML = '';
            DOMElements.totalProductsStat.textContent = '0';
            DOMElements.totalCategoriesStat.textContent = '0';
            DOMElements.potentialMatchesStat.textContent = '0';
            DOMElements.avgPriceDiffStat.textContent = '0 DZD';
            Object.values(AppState.chartInstances).forEach(chart => chart.destroy());
            AppState.chartInstances = {};
            DOMElements.exportCsvBtn.disabled = true;
            DOMElements.exportExcelBtn.disabled = true;
            clearError();
            console.log('Results area reset.');
        }

        function updateOverviewStats(results) {
            const totalProducts = results.allProducts.length;
            const allCategories = new Set([
                ...Object.keys(results.file1Categories),
                ...Object.keys(results.file2Categories)
            ]);
            const totalCategories = allCategories.size;
            const potentialMatches = results.potentialMatches.length;
            
            // Calculate average price difference
            let avgPriceDiff = 0;
            if (potentialMatches > 0) {
                const totalPriceDiff = results.potentialMatches.reduce((sum, match) => {
                    return sum + Math.abs(match.priceDifference);
                }, 0);
                avgPriceDiff = totalPriceDiff / potentialMatches;
            }
            
            DOMElements.totalProductsStat.textContent = totalProducts;
            DOMElements.totalCategoriesStat.textContent = totalCategories;
            DOMElements.potentialMatchesStat.textContent = potentialMatches;
            DOMElements.avgPriceDiffStat.textContent = `${avgPriceDiff.toFixed(2)} DZD`;
        }

        function displayCategoryDistribution(results) {
            // Display file 1 categories
            displayCategoryBars(results.file1Categories, DOMElements.file1CategoriesContainer, DOMElements.file1Legend);
            
            // Display file 2 categories
            displayCategoryBars(results.file2Categories, DOMElements.file2CategoriesContainer, DOMElements.file2Legend);
        }

        function displayCategoryBars(categories, container, legendContainer) {
            container.innerHTML = '';
            legendContainer.innerHTML = '';
            
            // Calculate total for percentages
            const total = Object.values(categories).reduce((sum, count) => sum + count, 0);
            
            // Create bars for each category
            Object.entries(categories).forEach(([category, count]) => {
                const percentage = (count / total * 100).toFixed(1);
                
                const barContainer = document.createElement('div');
                barContainer.style.display = 'flex';
                barContainer.style.alignItems = 'center';
                barContainer.style.marginBottom = '10px';
                
                const categoryLabel = document.createElement('div');
                categoryLabel.style.width = '100px';
                categoryLabel.style.fontWeight = '600';
                categoryLabel.textContent = capitalizeFirstLetter(category);
                
                const barWrapper = document.createElement('div');
                barWrapper.className = 'category-bar';
                barWrapper.style.flex = '1';
                
                const bar = document.createElement('div');
                bar.className = 'category-bar-fill';
                bar.style.width = `${percentage}%`;
                bar.style.backgroundColor = categoryColors[category] || categoryColors.other;
                bar.textContent = `${percentage}% (${count})`;
                
                barWrapper.appendChild(bar);
                barContainer.appendChild(categoryLabel);
                barContainer.appendChild(barWrapper);
                
                container.appendChild(barContainer);
                
                // Add to legend
                const legendItem = document.createElement('div');
                legendItem.className = 'legend-item';
                
                const colorBox = document.createElement('div');
                colorBox.className = 'legend-color';
                colorBox.style.backgroundColor = categoryColors[category] || categoryColors.other;
                
                const legendText = document.createElement('span');
                legendText.textContent = capitalizeFirstLetter(category);
                
                legendItem.appendChild(colorBox);
                legendItem.appendChild(legendText);
                
                legendContainer.appendChild(legendItem);
            });
        }

        function filterAndDisplayMatches() {
            const { categoryFilter, sortBy, maxResultsPerProduct } = AppState.settings;
            const matches = AppState.comparisonResults.potentialMatches;
            
            // Filter matches by category if needed
            let filteredMatches = matches;
            if (categoryFilter !== 'all') {
                filteredMatches = matches.filter(match => 
                    match.item1.category === categoryFilter || 
                    match.item2.category === categoryFilter
                );
            }
            
            // Sort matches
            switch (sortBy) {
                case 'similarity':
                    filteredMatches.sort((a, b) => b.similarity - a.similarity);
                    break;
                case 'price-diff-asc':
                    filteredMatches.sort((a, b) => Math.abs(a.priceDifference) - Math.abs(b.priceDifference));
                    break;
                case 'price-diff-desc':
                    filteredMatches.sort((a, b) => Math.abs(b.priceDifference) - Math.abs(a.priceDifference));
                    break;
                case 'price-asc':
                    filteredMatches.sort((a, b) => a.item1.price - b.item1.price);
                    break;
                case 'price-desc':
                    filteredMatches.sort((a, b) => b.item1.price - a.item1.price);
                    break;
            }
            
            // Limit matches per product if needed
            const productMatchCounts = {};
            const limitedMatches = [];
            
            for (const match of filteredMatches) {
                const product1Id = `${match.item1.sourceFile}-${match.item1.id}`;
                const product2Id = `${match.item2.sourceFile}-${match.item2.id}`;
                
                productMatchCounts[product1Id] = productMatchCounts[product1Id] || 0;
                productMatchCounts[product2Id] = productMatchCounts[product2Id] || 0;
                
                if (productMatchCounts[product1Id] < maxResultsPerProduct && 
                    productMatchCounts[product2Id] < maxResultsPerProduct) {
                    limitedMatches.push(match);
                    productMatchCounts[product1Id]++;
                    productMatchCounts[product2Id]++;
                }
            }
            
            // Display matches
            populateMatchesTable(limitedMatches);
        }

        function populateMatchesTable(matches) {
            const tbody = DOMElements.matchesTbody;
            tbody.innerHTML = '';

            if (matches.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px;">No potential matches found based on the current settings. Try lowering the Similarity Threshold or changing the Category Filter.</td></tr>';
                return;
            }

            const fragment = document.createDocumentFragment();
            matches.forEach(match => {
                const tr = document.createElement('tr');
                const priceDiff = match.priceDifference;
                const priceDiffClass = priceDiff > 0 ? 'positive' : (priceDiff < 0 ? 'negative' : '');
                const priceDiffText = `${priceDiff > 0 ? '+' : ''}${priceDiff.toFixed(2)}`;
                const similarityPercent = (match.similarity * 100).toFixed(1);
                let similarityClass = 'similarity-low';
                if (match.similarity >= 0.7) {
                    similarityClass = 'similarity-high';
                } else if (match.similarity >= 0.4) {
                    similarityClass = 'similarity-medium';
                }

                tr.innerHTML = `
                    <td>
                        ${escapeHtml(match.item1.description)}
                        <br>
                        <span class="category-badge category-${match.item1.category}">${capitalizeFirstLetter(match.item1.category)}</span>
                        <small>(${escapeHtml(match.item1.sourceFile)})</small>
                    </td>
                    <td>${match.item1.price.toFixed(2)} DZD</td>
                    <td>
                        ${escapeHtml(match.item2.description)}
                        <br>
                        <span class="category-badge category-${match.item2.category}">${capitalizeFirstLetter(match.item2.category)}</span>
                        <small>(${escapeHtml(match.item2.sourceFile)})</small>
                    </td>
                    <td>${match.item2.price.toFixed(2)} DZD</td>
                    <td><span class="price-diff ${priceDiffClass}">${priceDiffText} DZD</span></td>
                    <td><span class="similarity-score ${similarityClass}">${similarityPercent}%</span></td>
                `;
                fragment.appendChild(tr);
            });
            tbody.appendChild(fragment);
        }

        function createOrUpdateCharts(results) {
            Object.values(AppState.chartInstances).forEach(chart => chart.destroy());
            AppState.chartInstances = {};
            
            if (results.allProducts.length === 0) return;

            try {
                // Create overview chart (category distribution)
                const categories = [...new Set([...Object.keys(results.file1Categories), ...Object.keys(results.file2Categories)])];
                const file1Data = categories.map(cat => results.file1Categories[cat] || 0);
                const file2Data = categories.map(cat => results.file2Categories[cat] || 0);
                
                AppState.chartInstances.overviewChart = new Chart(DOMElements.overviewChartCanvas, {
                    type: 'bar',
                    data: {
                        labels: categories.map(capitalizeFirstLetter),
                        datasets: [
                            {
                                label: 'File 1',
                                data: file1Data,
                                backgroundColor: 'rgba(102, 126, 234, 0.6)',
                                borderColor: 'rgba(102, 126, 234, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'File 2',
                                data: file2Data,
                                backgroundColor: 'rgba(118, 75, 162, 0.6)',
                                borderColor: 'rgba(118, 75, 162, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: { 
                            y: { 
                                beginAtZero: true, 
                                title: { display: true, text: 'Number of Products' } 
                            }, 
                            x: { 
                                title: { display: true, text: 'Product Categories' } 
                            } 
                        },
                        plugins: { 
                            title: { display: true, text: 'Product Category Distribution by File' },
                            legend: { position: 'top' }
                        }
                    }
                });
                
                // Create category comparison chart
                AppState.chartInstances.categoryComparisonChart = new Chart(DOMElements.categoryComparisonChartCanvas, {
                    type: 'radar',
                    data: {
                        labels: categories.map(capitalizeFirstLetter),
                        datasets: [
                            {
                                label: 'File 1',
                                data: file1Data,
                                backgroundColor: 'rgba(102, 126, 234, 0.2)',
                                borderColor: 'rgba(102, 126, 234, 1)',
                                pointBackgroundColor: 'rgba(102, 126, 234, 1)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgba(102, 126, 234, 1)'
                            },
                            {
                                label: 'File 2',
                                data: file2Data,
                                backgroundColor: 'rgba(118, 75, 162, 0.2)',
                                borderColor: 'rgba(118, 75, 162, 1)',
                                pointBackgroundColor: 'rgba(118, 75, 162, 1)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgba(118, 75, 162, 1)'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { 
                            title: { display: true, text: 'Category Distribution Comparison' },
                            legend: { position: 'top' }
                        },
                        scales: {
                            r: {
                                angleLines: { display: true },
                                suggestedMin: 0
                            }
                        }
                    }
                });
                
                // Create price distribution chart
                const prices = results.allProducts.map(p => p.price);
                const priceBins = createHistogramBins(prices, 10);
                
                AppState.chartInstances.priceChart = new Chart(DOMElements.priceChartCanvas, {
                    type: 'bar',
                    data: {
                        labels: priceBins.map(bin => `${bin.min.toFixed(0)}-${bin.max.toFixed(0)} DZD`),
                        datasets: [{
                            label: 'Product Price Distribution',
                            data: priceBins.map(bin => bin.count),
                            backgroundColor: 'rgba(102, 126, 234, 0.6)',
                            borderColor: 'rgba(102, 126, 234, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: { 
                            y: { beginAtZero: true, title: { display: true, text: 'Number of Products' } }, 
                            x: { title: { display: true, text: 'Price Range (DZD)' } } 
                        },
                        plugins: { title: { display: true, text: 'Distribution of Product Prices' } }
                    }
                });
                
                // Create similarity distribution chart
                if (results.potentialMatches.length > 0) {
                    const similarities = results.potentialMatches.map(r => r.similarity);
                    const similarityBins = createHistogramBins(similarities, 10, 0, 1);
                    
                    AppState.chartInstances.similarityChart = new Chart(DOMElements.similarityChartCanvas, {
                        type: 'bar',
                        data: {
                            labels: similarityBins.map(bin => `${(bin.min * 100).toFixed(0)}%- ${(bin.max * 100).toFixed(0)}%`),
                            datasets: [{
                                label: 'Match Similarity Distribution',
                                data: similarityBins.map(bin => bin.count),
                                backgroundColor: 'rgba(118, 75, 162, 0.6)',
                                borderColor: 'rgba(118, 75, 162, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: { 
                                y: { beginAtZero: true, title: { display: true, text: 'Number of Matches' } }, 
                                x: { title: { display: true, text: 'Similarity Score Range' } } 
                            },
                            plugins: { title: { display: true, text: 'Distribution of Match Similarity Scores' } }
                        }
                    });
                }
            } catch (error) {
                console.error("Error creating charts:", error);
            }
        }

        function createHistogramBins(data, numBins, minVal = null, maxVal = null) {
            if (data.length === 0) return [];
            const minValue = minVal ?? Math.min(...data);
            const maxValue = maxVal ?? Math.max(...data);
            const range = maxValue - minValue;
            const binSize = range > 0 ? range / numBins : 1;
            const bins = Array(numBins).fill(0).map((_, i) => ({ min: minValue + i * binSize, max: minValue + (i + 1) * binSize, count: 0 }));
            if (bins.length > 0) { bins[bins.length - 1].max = maxValue; }
            data.forEach(value => {
                let binIndex = Math.floor((value - minValue) / binSize);
                if (value === maxValue) { binIndex = numBins - 1; }
                binIndex = Math.max(0, Math.min(binIndex, numBins - 1));
                if (bins[binIndex]) { bins[binIndex].count++; }
            });
            return bins;
        }

        function resizeCharts() {
            Object.values(AppState.chartInstances).forEach(chart => {
                if (chart && typeof chart.resize === 'function') { chart.resize(); }
            });
        }

        // --- Insights Generation --- //

        function generateInsights(results) {
            const insightsList = DOMElements.insightsList;
            insightsList.innerHTML = '';
            const insights = [];
            
            if (results.allProducts.length === 0) {
                insights.push('No products found to generate insights.');
                return;
            }
            
            // Category distribution insights
            const allCategories = [...new Set([...Object.keys(results.file1Categories), ...Object.keys(results.file2Categories)])];
            
            if (allCategories.length > 0) {
                // Find dominant categories
                const file1DominantCategory = Object.entries(results.file1Categories)
                    .sort((a, b) => b[1] - a[1])[0];
                const file2DominantCategory = Object.entries(results.file2Categories)
                    .sort((a, b) => b[1] - a[1])[0];
                
                if (file1DominantCategory && file2DominantCategory) {
                    insights.push(`File 1 is primarily focused on <strong>${capitalizeFirstLetter(file1DominantCategory[0])}</strong> products (${file1DominantCategory[1]} items).`);
                    insights.push(`File 2 is primarily focused on <strong>${capitalizeFirstLetter(file2DominantCategory[0])}</strong> products (${file2DominantCategory[1]} items).`);
                    
                    if (file1DominantCategory[0] === file2DominantCategory[0]) {
                        insights.push(`Both files share the same dominant category: <strong>${capitalizeFirstLetter(file1DominantCategory[0])}</strong>.`);
                    }
                }
                
                // Find unique categories
                const file1UniqueCategories = Object.keys(results.file1Categories)
                    .filter(cat => !Object.keys(results.file2Categories).includes(cat));
                const file2UniqueCategories = Object.keys(results.file2Categories)
                    .filter(cat => !Object.keys(results.file1Categories).includes(cat));
                
                if (file1UniqueCategories.length > 0) {
                    insights.push(`File 1 contains unique categories not found in File 2: <strong>${file1UniqueCategories.map(capitalizeFirstLetter).join(', ')}</strong>.`);
                }
                
                if (file2UniqueCategories.length > 0) {
                    insights.push(`File 2 contains unique categories not found in File 1: <strong>${file2UniqueCategories.map(capitalizeFirstLetter).join(', ')}</strong>.`);
                }
            }
            
            // Match insights
            if (results.potentialMatches.length > 0) {
                // Best matches
                const bestMatch = results.potentialMatches[0];
                insights.push(`Highest similarity match (${(bestMatch.similarity * 100).toFixed(1)}%) found between "${escapeHtml(bestMatch.item1.description)}" and "${escapeHtml(bestMatch.item2.description)}".`);
                
                // Price difference insights
                const priceDiffs = results.potentialMatches.map(m => m.priceDifference);
                const avgPriceDiff = priceDiffs.reduce((sum, diff) => sum + diff, 0) / priceDiffs.length;
                
                if (avgPriceDiff > 0) {
                    insights.push(`On average, products in File 2 are <strong>${avgPriceDiff.toFixed(2)} DZD more expensive</strong> than similar products in File 1.`);
                } else if (avgPriceDiff < 0) {
                    insights.push(`On average, products in File 2 are <strong>${Math.abs(avgPriceDiff).toFixed(2)} DZD less expensive</strong> than similar products in File 1.`);
                }
                
                // Find best savings
                const bestSaving = [...results.potentialMatches]
                    .filter(m => m.priceDifference < 0)
                    .sort((a, b) => a.priceDifference - b.priceDifference)[0];
                
                if (bestSaving) {
                    insights.push(`Largest potential saving found: <strong>${Math.abs(bestSaving.priceDifference).toFixed(2)} DZD</strong> on product "${escapeHtml(bestSaving.item1.description)}" vs "${escapeHtml(bestSaving.item2.description)}".`);
                }
                
                // Category match insights
                const categoryCounts = {};
                results.potentialMatches.forEach(match => {
                    if (match.sameCategory) {
                        categoryCounts[match.item1.category] = (categoryCounts[match.item1.category] || 0) + 1;
                    }
                });
                
                const bestMatchCategory = Object.entries(categoryCounts)
                    .sort((a, b) => b[1] - a[1])[0];
                
                if (bestMatchCategory) {
                    insights.push(`The <strong>${capitalizeFirstLetter(bestMatchCategory[0])}</strong> category has the most potential matches (${bestMatchCategory[1]}).`);
                }
            } else {
                insights.push('No potential matches found between the files. Try lowering the similarity threshold for more results.');
            }
            
            // Add insights to the list
            insights.forEach(insight => {
                const li = document.createElement('li');
                li.innerHTML = insight;
                li.style.marginBottom = '15px';
                insightsList.appendChild(li);
            });
        }

        // --- Export Functionality --- //

        function exportMatches(format) {
            const matches = AppState.comparisonResults.potentialMatches;
            if (matches.length === 0) {
                displayError('No results to export.');
                return;
            }
            
            const dataToExport = matches.map(match => ({
                'Product_File1': match.item1.description,
                'Category_File1': match.item1.category,
                'Price_File1': match.item1.price,
                'Source_File1': match.item1.sourceFile,
                'Product_File2': match.item2.description,
                'Category_File2': match.item2.category,
                'Price_File2': match.item2.price,
                'Source_File2': match.item2.sourceFile,
                'Price_Difference': match.priceDifference,
                'Similarity_Score': match.similarity,
                'Same_Category': match.sameCategory ? 'Yes' : 'No'
            }));
            
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `product_comparison_${timestamp}`;
            
            try {
                if (format === 'csv') {
                    exportToCSV(dataToExport, `${filename}.csv`);
                } else if (format === 'excel') {
                    exportToExcel(dataToExport, `${filename}.xlsx`);
                }
            } catch (error) {
                console.error(`Export failed (${format}):`, error);
                displayError(`Failed to export data as ${format.toUpperCase()}. Please try again.`);
            }
        }

        function exportToCSV(data, filename) {
            if (!data || data.length === 0) return;
            const headers = Object.keys(data[0]);
            const csvRows = [
                headers.join(','),
                ...data.map(row => headers.map(fieldName => JSON.stringify(row[fieldName], (key, value) => value === null ? '' : value)).join(','))
            ];
            const csvString = csvRows.join('\r\n');
            downloadFile(csvString, filename, 'text/csv;charset=utf-8;');
        }

        function exportToExcel(data, filename) {
            if (!data || data.length === 0) return;
            const worksheet = XLSX.utils.json_to_sheet(data);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Comparison Results');
            XLSX.writeFile(workbook, filename);
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        }

        // --- Workflow --- //

        async function startComparisonWorkflow() {
            clearError();
            resetResults();
            try {
                updateSettingsFromUI();
                await processUploadedFiles();
                await performComparison();
                displayResults();
            } catch (error) {
                console.error('Comparison Workflow Error:', error);
                displayError(error.message || 'An unexpected error occurred during the comparison process.');
                showLoading(false);
            }
        }

        function updateProgress(message) {
            if (DOMElements.loadingOverlay.classList.contains('show')) {
                DOMElements.progressText.textContent = message;
            }
            console.log(`Progress: ${message}`);
        }

        // --- Utility Functions --- //

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function escapeHtml(unsafe) {
            if (typeof unsafe !== 'string') return unsafe;
            return unsafe
                 .replace(/&/g, "&amp;")
                 .replace(/</g, "&lt;")
                 .replace(/>/g, "&gt;")
                 .replace(/"/g, "&quot;")
                 .replace(/'/g, "&#039;");
        }
        
        function capitalizeFirstLetter(string) {
            if (!string) return '';
            return string.charAt(0).toUpperCase() + string.slice(1);
        }
    </script>
</body>
</html>
