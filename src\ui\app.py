# src/ui/app.py

import streamlit as st
import pandas as pd
import os
import sys
import logging
import itertools
from typing import Dict, Tuple, List, Any, Optional

# Add project root to Python path to allow importing engine modules
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.config import settings
from src.engine.data_loader import load_data, map_columns
from src.engine.matcher import find_best_matches, parse_price
# Import visualization functions when created
# from src.ui.visualizations import display_price_distribution, display_similarity_scatter

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

st.set_page_config(layout="wide", page_title="Product Comparison Tool")

st.title("Supplier Product Comparison Tool")
st.write("Upload supplier catalogs (Excel, CSV, JSON) to compare products and prices across multiple files.")

# --- Configuration Sidebar ---
st.sidebar.header("Configuration")
fuzzy_threshold = st.sidebar.slider("Fuzzy Match Threshold (WRatio)", 0, 100, settings.FUZZY_MATCH_THRESHOLD)
semantic_threshold = st.sidebar.slider("Semantic Similarity Threshold", 0.0, 1.0, settings.SEMANTIC_SIMILARITY_THRESHOLD, 0.05)
target_currency = st.sidebar.text_input("Target Currency for Analysis (Future Use)", settings.DEFAULT_TARGET_CURRENCY)

# --- Caching Functions ---
# Cache the loading and initial processing of a single file
# Use a dictionary representation of the UploadedFile for caching
@st.cache_data(ttl=3600) # Cache for 1 hour
def cached_load_and_process_file(uploaded_file_info: Dict[str, Any]) -> Tuple[Optional[pd.DataFrame], Optional[Dict[str, Any]], Optional[str]]:
    """Loads, maps columns, and parses price for a single uploaded file."""
    file_name = uploaded_file_info["name"]
    file_content = uploaded_file_info["content"]
    logging.info(f"Processing file (cache check): {file_name}")

    # Save temporary file to load with pandas
    # Ensure temp dir exists
    temp_dir = os.path.join(project_root, "temp_uploads")
    try:
        os.makedirs(temp_dir, exist_ok=True)
    except OSError as e:
        logging.error(f"Could not create temp directory {temp_dir}: {e}")
        return None, None, f"Server error: Could not create temporary directory."

    temp_file_path = os.path.join(temp_dir, file_name)
    try:
        with open(temp_file_path, "wb") as f:
            f.write(file_content)

        # Load data
        df = load_data(temp_file_path)
        if df is None:
            error_msg = f"Failed to load {file_name}. Check logs or file format."
            logging.error(error_msg)
            return None, None, error_msg

        # Map columns
        # Use settings imported within the function scope or passed as args if needed
        df_mapped, mapping, not_found = map_columns(df.copy(), settings.COLUMN_SYNONYMS, settings.DEFAULT_COLUMNS)
        # The mapping returned is Standard Name -> Original Name
        column_mapping_info = {"mapping": mapping, "not_found": not_found}

        # Basic Preprocessing (Price Parsing) - Apply to the mapped DataFrame
        if "price" in df_mapped.columns:
            df_mapped["parsed_price"] = df_mapped["price"].apply(parse_price)
            # Placeholder for currency/unit normalization
            # df_mapped["normalized_price"] = ...
        else:
            logging.warning(f'"price" column not found or mapped in {file_name}. Price analysis will be limited.')
            # Add the column anyway for consistency downstream, filled with None
            df_mapped["parsed_price"] = None

        # Ensure essential columns exist for matching, even if empty
        if "product_name" not in df_mapped.columns:
             logging.warning(f'"product_name" column not found or mapped in {file_name}. Matching will not be possible for this file.')
             # Return the DataFrame but signal the issue
             # The calling code should handle this

        return df_mapped, column_mapping_info, None

    except Exception as e:
        error_msg = f"Error processing {file_name}: {e}"
        logging.exception(f"Unhandled error during file processing for {file_name}")
        return None, None, error_msg
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except OSError as e:
                logging.error(f"Error removing temporary file {temp_file_path}: {e}")

# --- File Upload ---
st.header("1. Upload Supplier Catalogs")
uploaded_files = st.file_uploader(
    "Choose files (Excel, CSV, JSON)",
    accept_multiple_files=True,
    type=["xlsx", "xls", "csv", "json"]
)

# --- Data Loading and Processing ---
if uploaded_files:
    if len(uploaded_files) < 2:
        st.warning("Please upload at least two files for comparison.")
    else:
        st.header("2. Processing Catalogs")
        # Use columns for better layout
        col1, col2 = st.columns([3, 1])
        with col1:
            progress_bar = st.progress(0)
            status_text = st.empty()
        with col2:
             # Button to clear cache manually if needed
             if st.button("Clear Cache & Rerun"):
                 st.cache_data.clear()
                 st.rerun()

        processed_data = {}
        column_mappings_display = {}
        load_errors = []

        num_files = len(uploaded_files)
        for i, uploaded_file in enumerate(uploaded_files):
            file_name = uploaded_file.name
            status_text.text(f"Processing {file_name} ({i+1}/{num_files})...")

            # Prepare info dict for caching function (must be hashable)
            # Read content once using getvalue()
            try:
                file_content = uploaded_file.getvalue()
            except Exception as e:
                 st.error(f"Error reading file {file_name}: {e}")
                 load_errors.append(f"{file_name} (Read Error)")
                 progress_bar.progress((i + 1) / num_files)
                 continue

            uploaded_file_info = {
                "name": file_name,
                "content": file_content,
                "size": uploaded_file.size,
                "type": uploaded_file.type
            }

            # Call cached function
            df_processed, mapping_info, error = cached_load_and_process_file(uploaded_file_info)

            if error:
                st.error(f"Error processing {file_name}: {error}")
                load_errors.append(file_name)
            elif df_processed is not None and mapping_info is not None:
                # Check if essential product_name column exists after mapping
                if "product_name" not in df_processed.columns:
                    st.error(f'Crucial "product_name" column could not be mapped in {file_name}. This file cannot be used for comparison.')
                    load_errors.append(f'{file_name} (Missing "product_name")')
                    # Don't add to processed_data if name is missing
                else:
                    processed_data[file_name] = df_processed
                    column_mappings_display[file_name] = mapping_info
                    # Display mapping info only if successful and name column exists
                    with st.expander(f"Column Mapping Details for {file_name}"):
                        st.write("Mapped Standard Columns -> Original File Columns:")
                        st.json(mapping_info["mapping"])
                        if mapping_info["not_found"]:
                            st.warning(f"Could not automatically find columns for standard keys: {mapping_info['not_found']}")
                        if "parsed_price" not in df_processed.columns or df_processed["parsed_price"] is None:
                             st.warning('"price" column could not be mapped or parsed. Price comparison involving this file will be limited.')
            else:
                 # Should not happen if error is None, but as a safeguard
                 st.error(f"An unexpected issue occurred while processing {file_name}.")
                 load_errors.append(file_name)

            progress_bar.progress((i + 1) / num_files)

        status_text.text("Initial file processing complete.")
        if load_errors:
             st.warning(f'Issues encountered with files: {", ".join(load_errors)}. These files may be excluded from comparison.')

        # --- Comparison ---
        valid_files_for_comparison = list(processed_data.keys())
        if len(valid_files_for_comparison) >= 2:
            st.header("3. Product Comparison Results")

            # Generate all unique pairs of files for comparison
            file_pairs = list(itertools.combinations(valid_files_for_comparison, 2))

            all_matches_list = []
            comparison_status = st.empty()
            comparison_progress = st.progress(0)
            num_comparisons = len(file_pairs)

            if num_comparisons > 0:
                for j, (file1, file2) in enumerate(file_pairs):
                    comparison_status.text(f"Comparing {file1} with {file2} ({j+1}/{num_comparisons})...")
                    df1 = processed_data[file1]
                    df2 = processed_data[file2]

                    # We already checked for product_name existence when adding to processed_data
                    try:
                        # Use streamlit caching for the matching step as well?
                        # Key would need to include df hashes/ids and parameters.
                        # For now, run directly.
                        matches_df = find_best_matches(
                            df1, df2,
                            name_col1="product_name", name_col2="product_name",
                            fuzzy_threshold=fuzzy_threshold,
                            semantic_threshold=semantic_threshold,
                            batch_size=settings.BATCH_SIZE_EMBEDDINGS
                        )

                        if not matches_df.empty:
                            # Add source file info for clarity in combined results
                            matches_df["source_file_1"] = file1
                            matches_df["source_file_2"] = file2

                            # Select and reorder columns for display
                            # Use the standardized names from the matcher output
                            display_cols = [
                                "source_file_1",
                                "product_name_1",
                                "source_file_2",
                                "product_name_2",
                                "fuzzy_score",
                                "semantic_score"
                            ]
                            # Add price and ID columns if they exist (using standard names + suffix)
                            for col_base in ["parsed_price", "product_id", "brand"]:
                                col1_name = f"{col_base}_1"
                                col2_name = f"{col_base}_2"
                                if col1_name in matches_df.columns: display_cols.insert(2, col1_name)
                                if col2_name in matches_df.columns: display_cols.insert(4, col2_name)

                            # Filter to existing columns before selecting & appending
                            existing_display_cols = [col for col in display_cols if col in matches_df.columns]
                            all_matches_list.append(matches_df[existing_display_cols])
                        # else: # Don't necessarily need to report every non-match pair
                        #     logging.info(f"No matches found between {file1} and {file2} with current settings.")
                    except Exception as e:
                         st.error(f"Error during comparison between {file1} and {file2}: {e}")
                         logging.exception(f"Comparison failed for pair: {file1}, {file2}")

                    comparison_progress.progress((j + 1) / num_comparisons)

                comparison_status.text("Comparison complete.")

                if all_matches_list:
                    final_comparison_df = pd.concat(all_matches_list, ignore_index=True)

                    # Sort results for better overview (e.g., by highest semantic then fuzzy score)
                    sort_cols = []
                    ascending_order = []
                    if "semantic_score" in final_comparison_df.columns:
                        sort_cols.append("semantic_score")
                        ascending_order.append(False)
                    if "fuzzy_score" in final_comparison_df.columns:
                        sort_cols.append("fuzzy_score")
                        ascending_order.append(False)

                    if sort_cols:
                         final_comparison_df = final_comparison_df.sort_values(
                             by=sort_cols, ascending=ascending_order
                         ).reset_index(drop=True)

                    # Define formatting for display
                    format_dict = {}
                    if "parsed_price_1" in final_comparison_df.columns: format_dict["parsed_price_1"] = "{:.2f}"
                    if "parsed_price_2" in final_comparison_df.columns: format_dict["parsed_price_2"] = "{:.2f}"
                    if "fuzzy_score" in final_comparison_df.columns: format_dict["fuzzy_score"] = "{:.1f}"
                    if "semantic_score" in final_comparison_df.columns: format_dict["semantic_score"] = "{:.3f}"

                    st.dataframe(final_comparison_df.style.format(format_dict, na_rep="N/A"), height=600, use_container_width=True)

                    # --- Visualizations (Placeholders) ---
                    st.header("4. Analysis & Visualizations")
                    st.write("_(Visualizations coming soon...)_")
                    # Example placeholder:
                    # if "parsed_price_1" in final_comparison_df.columns and "parsed_price_2" in final_comparison_df.columns:
                    #     try:
                    #         # Ensure prices are numeric for plotting
                    #         price_diff = pd.to_numeric(final_comparison_df["parsed_price_1"]) - pd.to_numeric(final_comparison_df["parsed_price_2"])
                    #         st.line_chart(price_diff.dropna())
                    #     except Exception as viz_e:
                    #         st.warning(f"Could not generate price difference chart: {viz_e}")

                    # --- Feedback (Placeholder) ---
                    st.header("5. Review Matches (Feedback - Coming Soon)")
                    st.write("_(Ability to confirm/reject matches will be added here to improve future results.)_")

                else:
                    st.info("No matches found across any file pairs with the current settings.")
            else:
                 st.info("No valid file pairs available for comparison.")
        else:
            st.warning('Could not process enough valid files (at least 2 with "product_name" mapped) to perform a comparison.')
else:
    st.info("Upload at least two supplier catalog files to begin.")

# Clean up temp directory? (Streamlit doesn't have a clean exit hook)
# Consider a scheduled cleanup job if temp files become an issue.

