# Base image for Product Comparison Tool
# To use the app:
# 1. Build: docker compose build
# 2. Run: docker compose up
# 3. Access: http://localhost:8501 in your browser
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/temp_uploads /app/cache/joblib /app/cache/streamlit

# Fix paths in settings.py to use container paths
RUN sed -i 's|/home/<USER>/product_comparison_tool/cache/joblib|/app/cache/joblib|g' src/config/settings.py && \
    sed -i 's|/home/<USER>/product_comparison_tool/cache/streamlit|/app/cache/streamlit|g' src/config/settings.py

# Fix temp directory path in app.py
RUN sed -i 's|temp_dir = "/home/<USER>/product_comparison_tool/temp_uploads"|temp_dir = "/app/temp_uploads"|g' src/ui/app.py

# Expose Streamlit port
EXPOSE 8501

# Set environment variable to make Streamlit accessible outside container
ENV STREAMLIT_SERVER_PORT=8501
ENV STREAMLIT_SERVER_ADDRESS=0.0.0.0

# Run the application
CMD ["streamlit", "run", "src/ui/app.py"]

