# src/engine/data_loader.py

import pandas as pd
import json
import logging
import os
from typing import List, Dict, Any, Optional, Tuple

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def load_data(file_path: str) -> Optional[pd.DataFrame]:
    """Loads data from Excel, CSV, or JSON files into a pandas DataFrame."""
    try:
        _, file_extension = os.path.splitext(file_path)
        file_extension = file_extension.lower()

        if file_extension in [".xlsx", ".xls"]:
            # Attempt to read all sheets if no specific sheet is targeted initially
            # For simplicity, we'll read the first sheet here.
            # A more robust solution might try multiple sheets or ask the user.
            df = pd.read_excel(file_path, sheet_name=0) # Reads the first sheet
            logging.info(f"Successfully loaded Excel file: {file_path}")
            return df
        elif file_extension == ".csv":
            # Try detecting separator, fall back to comma
            try:
                df = pd.read_csv(file_path, sep=None, engine='python') # Auto-detect separator
            except Exception:
                 df = pd.read_csv(file_path) # Default to comma
            logging.info(f"Successfully loaded CSV file: {file_path}")
            return df
        elif file_extension == ".json":
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            # Assuming JSON is a list of records
            if isinstance(data, list):
                df = pd.DataFrame(data)
                logging.info(f"Successfully loaded JSON file (list of records): {file_path}")
                return df
            # Add handling for other JSON structures if needed
            else:
                logging.warning(f"Unsupported JSON structure in {file_path}. Expected a list of objects.")
                return None
        else:
            logging.error(f"Unsupported file format: {file_extension} for file {file_path}")
            return None
    except FileNotFoundError:
        logging.error(f"File not found: {file_path}")
        return None
    except Exception as e:
        logging.error(f"Error loading file {file_path}: {e}")
        return None

def detect_header(df: pd.DataFrame) -> int:
    """Simple heuristic to detect the header row. Assumes header is likely within the first 5 rows."""
    # This is a placeholder. A more robust implementation would analyze row content.
    # For now, assume the first row is the header.
    return 0

def map_columns(df: pd.DataFrame, column_synonyms: Dict[str, List[str]], default_columns: Dict[str, str]) -> Tuple[pd.DataFrame, Dict[str, str], List[str]]:
    """
    Identifies and maps columns based on synonyms and default preferences.
    Returns the DataFrame with standardized column names, the mapping found, and columns not found.
    """
    original_columns = df.columns.tolist()
    # Create a copy to avoid modifying the original DataFrame columns during processing
    df_processed = df.copy()
    df_processed.columns = df_processed.columns.astype(str).str.strip().str.lower()
    standardized_columns = df_processed.columns.tolist()

    mapping = {}
    found_standard_names = set()
    not_found = []

    # Iterate through the standard names we expect (e.g., product_name, price)
    for standard_name, preferred_name in default_columns.items():
        found = False
        preferred_name_lower = preferred_name.strip().lower()
        synonyms = column_synonyms.get(standard_name, [])

        # 1. Check if the preferred column name exists
        if preferred_name_lower in standardized_columns:
            mapping[standard_name] = preferred_name_lower
            found_standard_names.add(standard_name)
            found = True
        else:
            # 2. Check synonyms if preferred name not found
            for synonym in synonyms:
                synonym_lower = synonym.strip().lower()
                if synonym_lower in standardized_columns and standard_name not in found_standard_names:
                    # Ensure we don't map multiple original columns to the same standard name via synonyms
                    if synonym_lower not in mapping.values():
                         mapping[standard_name] = synonym_lower
                         found_standard_names.add(standard_name)
                         found = True
                         break # Found a match for this standard name
        
        if not found:
            # 3. If still not found, check if the standard_name key itself exists as a column
            # (e.g., if the file already has a column named 'product_name')
            if standard_name in standardized_columns and standard_name not in found_standard_names:
                 if standard_name not in mapping.values():
                    mapping[standard_name] = standard_name
                    found_standard_names.add(standard_name)
                    found = True

        if not found:
            not_found.append(standard_name)

    # Rename columns in the original DataFrame based on the final mapping
    # Use the original column names preserved before lowercasing/stripping for renaming
    rename_dict = {}
    original_mapping = {orig.strip().lower(): orig for orig in original_columns}
    for standard, mapped_lower in mapping.items():
        original_case_col = original_mapping.get(mapped_lower)
        if original_case_col:
             rename_dict[original_case_col] = standard # Map Original Name -> Standard Name
        else:
             # Fallback if somehow the original mapping failed (shouldn't happen)
             logging.warning(f"Could not find original case for mapped column: {mapped_lower}")
             # Try renaming the lowercased version - less ideal
             if mapped_lower in df.columns.str.strip().str.lower().tolist():
                 # This requires finding the exact match in the lowercased list again
                 # It's safer to just use the standard name directly if original case is lost
                 pass # Avoid complex logic, will select later

    df_renamed = df.rename(columns=rename_dict)

    # Select only the columns that were successfully mapped (now using standard names)
    final_columns = list(mapping.keys())
    # Ensure only existing columns are selected
    final_columns_existing = [col for col in final_columns if col in df_renamed.columns]
    df_final = df_renamed[final_columns_existing]

    logging.info(f"Column mapping complete. Mapped standard names: {final_columns_existing}. Could not find: {not_found}")
    # Provide the mapping from standard name back to the *original* column name found
    final_mapping_to_original = {std_name: original_mapping.get(mapped_lower) 
                                 for std_name, mapped_lower in mapping.items() 
                                 if original_mapping.get(mapped_lower)}
    logging.info(f"Final mapping (Standard Name -> Original Name): {final_mapping_to_original}")

    return df_final, final_mapping_to_original, not_found

if __name__ == '__main__':
    # Example Usage (for testing)
    test_dir = '/home/<USER>/product_comparison_tool/tests/data'
    os.makedirs(test_dir, exist_ok=True)
    excel_path = os.path.join(test_dir, 'test_products.xlsx')
    csv_path = os.path.join(test_dir, 'test_products.csv')
    json_path = os.path.join(test_dir, 'test_products.json')

    dummy_data = {
        ' Product Name ' : ['Laptop A', 'Mouse B', 'Keyboard C'],
        'Price': [999.99, 29.99, 79.99],
        'Brand': ['TechCorp', 'MouseCorp', 'KeyCorp']
    }
    df_dummy = pd.DataFrame(dummy_data)
    df_dummy.to_excel(excel_path, index=False)
    df_dummy.to_csv(csv_path, index=False)
    df_dummy.to_json(json_path, orient='records')

    # Test loading
    df_excel = load_data(excel_path)
    df_csv = load_data(csv_path)
    df_json = load_data(json_path)

    print("--- Excel Load ---")
    print(df_excel.head() if df_excel is not None else "Failed")
    print("\n--- CSV Load ---")
    print(df_csv.head() if df_csv is not None else "Failed")
    print("\n--- JSON Load ---")
    print(df_json.head() if df_json is not None else "Failed")

    # Test column mapping
    if df_excel is not None:
        # Need to import settings from the correct relative path for testing
        try:
            from src.config.settings import COLUMN_SYNONYMS, DEFAULT_COLUMNS
        except ImportError:
            # Adjust path if running script directly for testing
            sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), \'..\
            from src.config.settings import COLUMN_SYNONYMS, DEFAULT_COLUMNS
            
        df_mapped, mapping, not_found = map_columns(df_excel.copy(), COLUMN_SYNONYMS, DEFAULT_COLUMNS)
        print("\n--- Column Mapping (Excel) ---")
        print("Mapped DF Head:\n", df_mapped.head())
        print("Mapping (Standard -> Original):", mapping)
        print("Not Found Standard Keys:", not_found)



